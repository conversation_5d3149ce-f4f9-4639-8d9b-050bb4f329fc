<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>اختبار إحصائيات الخط الزمني - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 2rem;
            direction: rtl;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #4a90a4;
        }
        
        .test-header h1 {
            color: #2c3e50;
            margin: 0;
        }
        
        .test-status {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-item {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .status-loading {
            background: rgba(74, 144, 164, 0.1);
            color: #4a90a4;
            border: 1px solid rgba(74, 144, 164, 0.2);
        }
        
        .test-actions {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 0.5rem;
        }
        
        .btn-primary {
            background: #4a90a4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #357a8a;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .timeline-stats-demo {
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-chart-bar"></i> اختبار إحصائيات الخط الزمني</h1>
            <p>اختبار شامل لوظائف إحصائيات الخط الزمني في لوحة التحكم</p>
        </div>
        
        <div class="test-status">
            <div class="status-item status-loading" id="supabaseStatus">
                <i class="fas fa-spinner fa-spin"></i> جاري فحص Supabase
            </div>
            <div class="status-item status-loading" id="dataStatus">
                <i class="fas fa-spinner fa-spin"></i> جاري فحص البيانات
            </div>
            <div class="status-item status-loading" id="functionsStatus">
                <i class="fas fa-spinner fa-spin"></i> جاري فحص الوظائف
            </div>
        </div>
        
        <div class="test-actions">
            <button class="btn btn-primary" onclick="runTests()">
                <i class="fas fa-play"></i> تشغيل الاختبارات
            </button>
            <button class="btn btn-secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
        </div>
        
        <div class="test-results" id="testResults">
            <div style="color: #6c757d; text-align: center;">
                <i class="fas fa-info-circle"></i> اضغط على "تشغيل الاختبارات" لبدء الفحص
            </div>
        </div>
        
        <div class="timeline-stats-demo" id="timelineStatsDemo">
            <!-- Timeline Stats will be loaded here -->
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
        
        let supabase;
        let testResults = [];

        // Initialize Supabase
        function initSupabase() {
            try {
                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return true;
            } catch (error) {
                console.error('Failed to initialize Supabase:', error);
                return false;
            }
        }

        // Get Supabase client
        function getSupabaseClient() {
            return supabase;
        }

        // Log test result
        function logResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testResults.push(`[${timestamp}] ${icon} ${message}`);
            updateResultsDisplay();
        }

        // Update results display
        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testResults.join('<br>');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Clear results
        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('testResults').innerHTML = '<div style="color: #6c757d; text-align: center;"><i class="fas fa-info-circle"></i> تم مسح النتائج</div>';
        }

        // Update status indicator
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status-item status-${status}`;
            const icon = status === 'success' ? 'fas fa-check' : status === 'error' ? 'fas fa-times' : 'fas fa-spinner fa-spin';
            element.innerHTML = `<i class="${icon}"></i> ${message}`;
        }

        // Test Supabase connection
        async function testSupabaseConnection() {
            logResult('بدء اختبار اتصال Supabase...');
            
            try {
                if (!initSupabase()) {
                    throw new Error('فشل في تهيئة Supabase');
                }
                
                // Test basic connection
                const { data, error } = await supabase.from('site_settings').select('count').limit(1);
                
                if (error) {
                    throw error;
                }
                
                updateStatus('supabaseStatus', 'success', 'Supabase متصل');
                logResult('تم الاتصال بـ Supabase بنجاح', 'success');
                return true;
                
            } catch (error) {
                updateStatus('supabaseStatus', 'error', 'خطأ في Supabase');
                logResult(`خطأ في اتصال Supabase: ${error.message}`, 'error');
                return false;
            }
        }

        // Test timeline stats data
        async function testTimelineStatsData() {
            logResult('بدء اختبار بيانات إحصائيات الخط الزمني...');
            
            try {
                const { data: settings, error } = await supabase
                    .from('site_settings')
                    .select('setting_key, setting_value')
                    .in('setting_key', [
                        'timeline_stats_enabled',
                        'timeline_stat_years',
                        'timeline_stat_customers', 
                        'timeline_stat_products',
                        'timeline_stat_brands'
                    ]);

                if (error) {
                    throw error;
                }

                const timelineSettings = {};
                if (settings && Array.isArray(settings)) {
                    settings.forEach(setting => {
                        timelineSettings[setting.setting_key] = setting.setting_value;
                    });
                }

                logResult(`تم العثور على ${settings.length} إعدادات للخط الزمني`, 'success');
                logResult(`الإعدادات: ${JSON.stringify(timelineSettings, null, 2)}`);
                
                updateStatus('dataStatus', 'success', 'البيانات متوفرة');
                return timelineSettings;
                
            } catch (error) {
                updateStatus('dataStatus', 'error', 'خطأ في البيانات');
                logResult(`خطأ في تحميل بيانات الخط الزمني: ${error.message}`, 'error');
                return null;
            }
        }

        // Test timeline stats functions
        function testTimelineStatsFunctions() {
            logResult('بدء اختبار وظائف إحصائيات الخط الزمني...');
            
            const functions = [
                'getSupabaseClient',
                'updateStatus',
                'logResult'
            ];
            
            let allFunctionsWork = true;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    logResult(`✓ الوظيفة ${funcName} متوفرة`, 'success');
                } else {
                    logResult(`✗ الوظيفة ${funcName} غير متوفرة`, 'error');
                    allFunctionsWork = false;
                }
            });
            
            if (allFunctionsWork) {
                updateStatus('functionsStatus', 'success', 'الوظائف تعمل');
                logResult('جميع الوظائف الأساسية تعمل بشكل صحيح', 'success');
            } else {
                updateStatus('functionsStatus', 'error', 'خطأ في الوظائف');
                logResult('بعض الوظائف لا تعمل بشكل صحيح', 'error');
            }
            
            return allFunctionsWork;
        }

        // Run all tests
        async function runTests() {
            logResult('=== بدء الاختبارات الشاملة ===');
            
            // Reset status indicators
            updateStatus('supabaseStatus', 'loading', 'جاري فحص Supabase');
            updateStatus('dataStatus', 'loading', 'جاري فحص البيانات');
            updateStatus('functionsStatus', 'loading', 'جاري فحص الوظائف');
            
            // Test 1: Supabase Connection
            const supabaseOk = await testSupabaseConnection();
            
            // Test 2: Timeline Stats Data
            let timelineData = null;
            if (supabaseOk) {
                timelineData = await testTimelineStatsData();
            }
            
            // Test 3: Functions
            const functionsOk = testTimelineStatsFunctions();
            
            // Summary
            logResult('=== ملخص النتائج ===');
            logResult(`Supabase: ${supabaseOk ? '✅ يعمل' : '❌ لا يعمل'}`);
            logResult(`البيانات: ${timelineData ? '✅ متوفرة' : '❌ غير متوفرة'}`);
            logResult(`الوظائف: ${functionsOk ? '✅ تعمل' : '❌ لا تعمل'}`);
            
            if (supabaseOk && timelineData && functionsOk) {
                logResult('🎉 جميع الاختبارات نجحت! إحصائيات الخط الزمني جاهزة للاستخدام', 'success');
            } else {
                logResult('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه', 'warning');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            logResult('تم تحميل صفحة الاختبار بنجاح');
            logResult('اضغط على "تشغيل الاختبارات" لبدء الفحص');
        });
    </script>
</body>
</html>
