<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>اختبار إصلاح RLS - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 2rem;
            direction: rtl;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #4a90a4;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }
        
        .btn-primary {
            background: #4a90a4;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-info {
            color: #17a2b8;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-shield-alt"></i> اختبار إصلاح RLS Policies</h1>
            <p>اختبار شامل للتأكد من أن المدراء يمكنهم إدارة البيانات بعد إصلاح سياسات الأمان</p>
        </div>
        
        <!-- Tips Table Test -->
        <div class="test-section">
            <h3><i class="fas fa-lightbulb"></i> اختبار جدول النصائح (Tips)</h3>
            <button class="btn btn-primary" onclick="testTipsOperations()">اختبار عمليات النصائح</button>
            <button class="btn btn-success" onclick="testTipsRead()">اختبار قراءة النصائح</button>
            <div class="test-results" id="tipsResults">جاهز للاختبار...</div>
        </div>
        
        <!-- Guidelines Table Test -->
        <div class="test-section">
            <h3><i class="fas fa-book"></i> اختبار جدول الإرشادات (Guidelines)</h3>
            <button class="btn btn-primary" onclick="testGuidelinesOperations()">اختبار عمليات الإرشادات</button>
            <button class="btn btn-success" onclick="testGuidelinesRead()">اختبار قراءة الإرشادات</button>
            <div class="test-results" id="guidelinesResults">جاهز للاختبار...</div>
        </div>
        
        <!-- Contact Submissions Test -->
        <div class="test-section">
            <h3><i class="fas fa-envelope"></i> اختبار جدول رسائل التواصل (Contact Submissions)</h3>
            <button class="btn btn-primary" onclick="testContactOperations()">اختبار عمليات الرسائل</button>
            <button class="btn btn-success" onclick="testContactRead()">اختبار قراءة الرسائل</button>
            <div class="test-results" id="contactResults">جاهز للاختبار...</div>
        </div>
        
        <!-- Overall Test -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> اختبار شامل</h3>
            <button class="btn btn-success" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <button class="btn btn-danger" onclick="clearAllResults()">مسح النتائج</button>
            <div class="test-results" id="overallResults">اضغط على "تشغيل جميع الاختبارات" لبدء الفحص الشامل</div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Utility functions
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type === 'success' ? 'status-success' : type === 'error' ? 'status-error' : 'status-info';
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            
            element.innerHTML += `<div class="${className}">[${timestamp}] ${icon} ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        function clearResults(elementId) {
            document.getElementById(elementId).innerHTML = 'تم مسح النتائج...';
        }

        // Test Tips operations
        async function testTipsOperations() {
            const resultId = 'tipsResults';
            clearResults(resultId);
            logResult(resultId, 'بدء اختبار عمليات النصائح...', 'info');

            try {
                // Test INSERT
                logResult(resultId, 'اختبار إضافة نصيحة جديدة...', 'info');
                const { data: insertData, error: insertError } = await supabase
                    .from('tips')
                    .insert({
                        title: 'نصيحة اختبار RLS',
                        content: 'هذه نصيحة لاختبار سياسات الأمان المحدثة',
                        category: 'اختبار',
                        icon: 'fas fa-test',
                        order_index: 999,
                        is_active: false
                    })
                    .select();

                if (insertError) {
                    logResult(resultId, `خطأ في الإضافة: ${insertError.message}`, 'error');
                    return;
                }

                const testId = insertData[0].id;
                logResult(resultId, `تم إضافة النصيحة بنجاح! ID: ${testId}`, 'success');

                // Test UPDATE
                logResult(resultId, 'اختبار تحديث النصيحة...', 'info');
                const { error: updateError } = await supabase
                    .from('tips')
                    .update({ title: 'نصيحة محدثة - اختبار RLS' })
                    .eq('id', testId);

                if (updateError) {
                    logResult(resultId, `خطأ في التحديث: ${updateError.message}`, 'error');
                } else {
                    logResult(resultId, 'تم تحديث النصيحة بنجاح!', 'success');
                }

                // Test DELETE
                logResult(resultId, 'اختبار حذف النصيحة...', 'info');
                const { error: deleteError } = await supabase
                    .from('tips')
                    .delete()
                    .eq('id', testId);

                if (deleteError) {
                    logResult(resultId, `خطأ في الحذف: ${deleteError.message}`, 'error');
                } else {
                    logResult(resultId, 'تم حذف النصيحة بنجاح!', 'success');
                }

                logResult(resultId, '🎉 جميع عمليات النصائح نجحت!', 'success');

            } catch (error) {
                logResult(resultId, `خطأ عام: ${error.message}`, 'error');
            }
        }

        // Test Tips read operations
        async function testTipsRead() {
            const resultId = 'tipsResults';
            logResult(resultId, 'اختبار قراءة النصائح...', 'info');

            try {
                const { data, error } = await supabase
                    .from('tips')
                    .select('*')
                    .limit(5);

                if (error) {
                    logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
                } else {
                    logResult(resultId, `تم قراءة ${data.length} نصيحة بنجاح!`, 'success');
                }
            } catch (error) {
                logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
            }
        }

        // Test Guidelines operations
        async function testGuidelinesOperations() {
            const resultId = 'guidelinesResults';
            clearResults(resultId);
            logResult(resultId, 'بدء اختبار عمليات الإرشادات...', 'info');

            try {
                // Test INSERT
                const { data: insertData, error: insertError } = await supabase
                    .from('guidelines')
                    .insert({
                        title: 'إرشاد اختبار RLS',
                        content: 'هذا إرشاد لاختبار سياسات الأمان',
                        category: 'اختبار',
                        icon: 'fas fa-test',
                        order_index: 999,
                        is_active: false
                    })
                    .select();

                if (insertError) {
                    logResult(resultId, `خطأ في إضافة الإرشاد: ${insertError.message}`, 'error');
                    return;
                }

                const testId = insertData[0].id;
                logResult(resultId, 'تم إضافة الإرشاد بنجاح!', 'success');

                // Clean up
                await supabase.from('guidelines').delete().eq('id', testId);
                logResult(resultId, 'تم حذف الإرشاد التجريبي', 'success');

            } catch (error) {
                logResult(resultId, `خطأ عام: ${error.message}`, 'error');
            }
        }

        // Test Guidelines read
        async function testGuidelinesRead() {
            const resultId = 'guidelinesResults';
            logResult(resultId, 'اختبار قراءة الإرشادات...', 'info');

            try {
                const { data, error } = await supabase
                    .from('guidelines')
                    .select('*')
                    .limit(5);

                if (error) {
                    logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
                } else {
                    logResult(resultId, `تم قراءة ${data.length} إرشاد بنجاح!`, 'success');
                }
            } catch (error) {
                logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
            }
        }

        // Test Contact operations
        async function testContactOperations() {
            const resultId = 'contactResults';
            clearResults(resultId);
            logResult(resultId, 'بدء اختبار عمليات رسائل التواصل...', 'info');

            try {
                // Test reading contact submissions
                const { data, error } = await supabase
                    .from('contact_submissions')
                    .select('*')
                    .limit(5);

                if (error) {
                    logResult(resultId, `خطأ في قراءة الرسائل: ${error.message}`, 'error');
                } else {
                    logResult(resultId, `تم قراءة ${data.length} رسالة بنجاح!`, 'success');
                }
            } catch (error) {
                logResult(resultId, `خطأ عام: ${error.message}`, 'error');
            }
        }

        // Test Contact read
        async function testContactRead() {
            const resultId = 'contactResults';
            logResult(resultId, 'اختبار قراءة رسائل التواصل...', 'info');

            try {
                const { data, error } = await supabase
                    .from('contact_submissions')
                    .select('count')
                    .limit(1);

                if (error) {
                    logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
                } else {
                    logResult(resultId, 'تم الوصول لجدول رسائل التواصل بنجاح!', 'success');
                }
            } catch (error) {
                logResult(resultId, `خطأ في القراءة: ${error.message}`, 'error');
            }
        }

        // Run all tests
        async function runAllTests() {
            const resultId = 'overallResults';
            clearResults(resultId);
            logResult(resultId, '🚀 بدء الاختبار الشامل لجميع الجداول...', 'info');

            let successCount = 0;
            let totalTests = 0;

            // Test Tips
            try {
                totalTests++;
                await testTipsOperations();
                successCount++;
                logResult(resultId, '✅ اختبار النصائح: نجح', 'success');
            } catch (error) {
                logResult(resultId, '❌ اختبار النصائح: فشل', 'error');
            }

            // Test Guidelines
            try {
                totalTests++;
                await testGuidelinesOperations();
                successCount++;
                logResult(resultId, '✅ اختبار الإرشادات: نجح', 'success');
            } catch (error) {
                logResult(resultId, '❌ اختبار الإرشادات: فشل', 'error');
            }

            // Test Contact
            try {
                totalTests++;
                await testContactOperations();
                successCount++;
                logResult(resultId, '✅ اختبار رسائل التواصل: نجح', 'success');
            } catch (error) {
                logResult(resultId, '❌ اختبار رسائل التواصل: فشل', 'error');
            }

            // Summary
            logResult(resultId, `📊 النتيجة النهائية: ${successCount}/${totalTests} اختبارات نجحت`, 
                successCount === totalTests ? 'success' : 'error');

            if (successCount === totalTests) {
                logResult(resultId, '🎉 جميع اختبارات RLS نجحت! المدراء يمكنهم إدارة البيانات بنجاح', 'success');
            } else {
                logResult(resultId, '⚠️ بعض الاختبارات فشلت. يرجى مراجعة سياسات الأمان', 'error');
            }
        }

        // Clear all results
        function clearAllResults() {
            ['tipsResults', 'guidelinesResults', 'contactResults', 'overallResults'].forEach(id => {
                clearResults(id);
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('RLS Test Page loaded successfully');
        });
    </script>
</body>
</html>
