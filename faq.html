<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>الأسئلة الشائعة - Care</title>
    <meta name="description" content="الأسئلة الشائعة حول منتجات العناية بالبشرة والشعر - إجابات شاملة على جميع استفساراتكم">
    <meta name="keywords" content="أسئلة شائعة, منتجات العناية, البشرة, الشعر, العراق, استفسارات">
    <meta name="author" content="Care">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#4a90a4">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Header - Matching Homepage Design */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            font-size: var(--font-size-xl);
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-count {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            font-weight: bold;
        }

        /* Page Header - Professional Design System Compliant */
        .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #4a90a4 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            margin-bottom: 2rem;
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .page-header p {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* FAQ Section - Optimized Spacing */
        .faq-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }

        .faq-container {
            max-width: 900px;
            margin: 0 auto;
        }

        /* Search Bar - Professional Design */
        .faq-search-container {
            margin-bottom: 3rem;
            text-align: center;
        }

        .faq-search-wrapper {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .faq-search {
            width: 100%;
            padding: 1.5rem 4rem 1.5rem 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: var(--font-size-base);
            font-family: 'Cairo', sans-serif;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            color: #000000;
        }

        .faq-search:focus {
            outline: none;
            border-color: #4a90a4;
            box-shadow: 0 6px 20px rgba(74, 144, 164, 0.2);
        }

        .faq-search-icon {
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: #4a90a4;
            font-size: var(--font-size-lg);
            pointer-events: none;
        }

        /* FAQ Items - Clean Professional Design */
        .faq-item {
            background: white;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .faq-item:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            transform: translateY(-2px);
            border-color: #4a90a4;
        }

        /* FAQ Question - Professional Design */
        .faq-question {
            background: white;
            padding: 2rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: right;
            font-family: 'Cairo', sans-serif;
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: #000000;
        }

        .faq-question:hover {
            background: rgba(74, 144, 164, 0.05);
            color: #4a90a4;
        }

        .faq-question.active {
            background: rgba(74, 144, 164, 0.1);
            color: #4a90a4;
        }

        /* FAQ Icon */
        .faq-icon {
            font-size: var(--font-size-lg);
            transition: all 0.3s ease;
            color: #4a90a4;
            margin-right: 1.5rem;
        }

        .faq-question:hover .faq-icon {
            color: #4a90a4;
        }

        .faq-question.active .faq-icon {
            transform: rotate(180deg);
            color: #4a90a4;
        }

        /* FAQ Answer */
        .faq-answer {
            padding: 0 2rem;
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s ease;
            background: #f8f9fa;
        }

        .faq-answer.active {
            padding: 2rem;
            max-height: 500px;
            border-top: 1px solid #e9ecef;
        }

        .faq-answer p {
            color: #000000;
            line-height: 1.8;
            margin-bottom: 1.5rem;
            font-size: var(--font-size-base);
            font-weight: 400;
            font-family: 'Cairo', sans-serif;
        }

        .faq-answer p:last-child {
            margin-bottom: 0;
        }

        .faq-answer ul {
            margin: var(--spacing-md) 0;
            padding-right: var(--spacing-xl);
        }

        .faq-answer li {
            margin-bottom: var(--spacing-sm);
            color: var(--text-secondary);
            font-size: var(--font-size-base);
            line-height: 1.6;
        }

        /* Section Divider */
        .section-divider {
            width: 100%;
            height: 1px;
            background: #e9ecef;
            margin: 3rem 0;
        }

        /* FAQ Categories */
        .faq-categories {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .category-filter-wrapper {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
            display: inline-flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        .category-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: var(--font-size-sm);
            color: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;
        }

        .category-btn:hover,
        .category-btn.active {
            background: #4a90a4;
            border-color: #4a90a4;
            color: white;
            transform: translateY(-2px);
        }

        /* Contact Section */
        .contact-section {
            background: #f8f9fa;
            padding: 4rem 0;
            text-align: center;
        }

        .contact-section h2 {
            color: #4a90a4;
            margin-bottom: 1.5rem;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
        }

        .contact-section p {
            color: #000000;
            margin-bottom: 2rem;
            font-size: var(--font-size-lg);
            font-family: 'Cairo', sans-serif;
        }

        .contact-buttons {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
        }

        .contact-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #4a90a4;
            color: white;
            padding: 1.5rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .contact-btn:hover {
            background: #2c3e50;
            transform: translateY(-2px);
        }

        .whatsapp-btn {
            background: #25d366;
        }

        .whatsapp-btn:hover {
            background: #1da851;
        }

        /* Footer - Matching Homepage Design */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: 1.1rem;
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Print styles */
        @media print {
            header,
            .cart-icon,
            .faq-search-container,
            .faq-categories,
            .contact-section {
                display: none;
            }

            .page-header {
                padding: var(--spacing-xl) 0;
                background: none;
                color: var(--text-primary);
            }

            .faq-item {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
                border: 1px solid var(--text-primary);
            }

            .faq-question {
                background: none;
                color: var(--text-primary);
            }

            .faq-answer {
                max-height: none;
                padding: var(--spacing-md);
                background: none;
            }
        }
    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html" class="active">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>
    </header>

    <section class="page-header faq-bg" id="faqPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">الأسئلة الشائعة</span>
                </nav>
                <h1>الأسئلة الشائعة</h1>
                <p class="faq-page-description" data-setting="faq_page_description">إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا</p>
            </div>
        </div>
    </section>

    <main class="faq-section">
        <div class="container">
            <div class="faq-container">
                <!-- Enhanced Search Bar -->
                <div class="faq-search-container">
                    <div class="faq-search-wrapper">
                        <input type="text" class="faq-search" id="faqSearch" placeholder="ابحث في الأسئلة الشائعة..." onkeyup="searchFAQ()">
                        <i class="fas fa-search faq-search-icon"></i>
                    </div>
                </div>

                <!-- FAQ Categories -->
                <div class="faq-categories" id="faqCategories">
                    <div class="category-filter-wrapper">
                        <button class="category-btn active" onclick="filterFAQ('all')">
                            <i class="fas fa-list-ul" style="margin-left: var(--spacing-xs);"></i>
                            جميع الأسئلة
                        </button>
                        <button class="category-btn" onclick="filterFAQ('عام')">
                            <i class="fas fa-info-circle" style="margin-left: var(--spacing-xs);"></i>
                            عام
                        </button>
                        <button class="category-btn" onclick="filterFAQ('منتجات')">
                            <i class="fas fa-box" style="margin-left: var(--spacing-xs);"></i>
                            منتجات
                        </button>
                        <button class="category-btn" onclick="filterFAQ('طلبات')">
                            <i class="fas fa-shopping-cart" style="margin-left: var(--spacing-xs);"></i>
                            طلبات
                        </button>
                        <button class="category-btn" onclick="filterFAQ('توصيل')">
                            <i class="fas fa-truck" style="margin-left: var(--spacing-xs);"></i>
                            توصيل
                        </button>
                    </div>
                </div>

                <!-- Loading indicator -->
                <div id="faqLoading" style="text-align: center; padding: 3rem; color: #4a90a4;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-bottom: 1.5rem;"></i>
                    <p style="font-family: 'Cairo', sans-serif;">جاري تحميل الأسئلة الشائعة...</p>
                </div>

                <!-- FAQ content will be loaded here -->
                <div id="faqContent"></div>

                <!-- No FAQ message -->
                <div id="noFaqMessage" style="text-align: center; padding: 3rem; color: #000000; display: none;">
                    <i class="fas fa-question-circle" style="font-size: 2rem; margin-bottom: 1.5rem; opacity: 0.3; color: #4a90a4;"></i>
                    <h3 style="font-family: 'Cairo', sans-serif; color: #4a90a4; margin-bottom: 1rem;">لا توجد أسئلة شائعة متاحة حالياً</h3>
                    <p style="font-family: 'Cairo', sans-serif;">نعمل على إضافة المزيد من الأسئلة والإجابات قريباً</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Section Divider -->
    <div class="section-divider"></div>

    <section class="contact-section">
        <div class="container">
            <h2>لم تجد إجابة لسؤالك؟</h2>
            <p>تواصل معنا وسنكون سعداء لمساعدتك</p>
            <div class="contact-buttons">
                <a href="https://wa.me/9647713688302" class="contact-btn whatsapp-btn whatsapp-link" target="_blank">
                    <i class="fab fa-whatsapp"></i>
                    واتساب
                </a>
                <a href="contact.html" class="contact-btn">
                    <i class="fas fa-envelope"></i>
                    اتصل بنا
                </a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="https://wa.me/9647713688302" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تليغرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تليغرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="tiktok-link" target="_blank" title="تيك توك" style="color: #ff0050; display: none;" aria-label="تابعنا على تيك توك">
                            <i class="fab fa-tiktok" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="youtube-link" target="_blank" title="يوتيوب" style="color: #ff0000; display: none;" aria-label="تابعنا على يوتيوب">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="snapchat-link" target="_blank" title="سناب شات" style="color: #fffc00; display: none;" aria-label="تابعنا على سناب شات">
                            <i class="fab fa-snapchat-ghost" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>
        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        // FAQ functionality
        function toggleFAQ(button) {
            const answer = button.nextElementSibling;
            const icon = button.querySelector('.faq-icon');
            
            // Close all other FAQs
            document.querySelectorAll('.faq-question').forEach(q => {
                if (q !== button) {
                    q.classList.remove('active');
                    q.nextElementSibling.classList.remove('active');
                }
            });
            
            // Toggle current FAQ
            button.classList.toggle('active');
            answer.classList.toggle('active');
        }

        function filterFAQ(category) {
            const items = document.querySelectorAll('.faq-item');
            const buttons = document.querySelectorAll('.category-btn');
            
            // Update active button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide items
            items.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
                
                // Close all answers when filtering
                const question = item.querySelector('.faq-question');
                const answer = item.querySelector('.faq-answer');
                question.classList.remove('active');
                answer.classList.remove('active');
            });
        }

        // Keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close any open FAQ items
                document.querySelectorAll('.faq-question.active').forEach(question => {
                    question.classList.remove('active');
                    question.nextElementSibling.classList.remove('active');
                });
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
            loadFAQ();
        });

        // Get Supabase client
        function getSupabaseClient() {
            // Use shared configuration (singleton pattern)
            if (window.SupabaseConfig) {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            // Last resort: create client directly (should not happen if supabase-config.js is loaded)
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.warn('⚠️ Creating Supabase client directly in faq.html - supabase-config.js may not be loaded');
                const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

                window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return window.globalSupabaseClient;
            }

            return null;
        }

        let allFAQs = [];

        // Load FAQ from database
        async function loadFAQ() {
            const loading = document.getElementById('faqLoading');
            const content = document.getElementById('faqContent');
            const noMessage = document.getElementById('noFaqMessage');

            if (!loading || !content || !noMessage) return;

            loading.style.display = 'block';
            content.innerHTML = '';
            noMessage.style.display = 'none';

            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                const { data: faqs, error } = await supabase
                    .from('faq')
                    .select('*')
                    .eq('is_active', true)
                    .order('order_index', { ascending: true });

                if (error) throw error;

                loading.style.display = 'none';
                allFAQs = faqs || [];

                if (allFAQs.length === 0) {
                    noMessage.style.display = 'block';
                    return;
                }

                displayFAQs(allFAQs);

            } catch (error) {
                loading.style.display = 'none';
                noMessage.style.display = 'block';
                noMessage.innerHTML = `
                    <i class="fas fa-exclamation-triangle" style="font-size: var(--font-size-3xl); margin-bottom: var(--spacing-lg); color: #e74c3c;"></i>
                    <h3>خطأ في تحميل الأسئلة الشائعة</h3>
                    <p>يرجى المحاولة مرة أخرى</p>
                    <button onclick="loadFAQ()" style="margin-top: var(--spacing-lg); padding: var(--spacing-sm) var(--spacing-lg); background: var(--primary-color); color: white; border: none; border-radius: 8px; cursor: pointer;">
                        إعادة المحاولة
                    </button>
                `;
            }
        }

        // Display FAQs
        function displayFAQs(faqs) {
            const content = document.getElementById('faqContent');
            let html = '';

            faqs.forEach(faq => {
                html += `
                    <div class="faq-item" data-category="${faq.category}">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>${faq.question}</span>
                            <i class="fas fa-chevron-down faq-icon"></i>
                        </button>
                        <div class="faq-answer">
                            <p>${faq.answer}</p>
                        </div>
                    </div>
                `;
            });

            content.innerHTML = html;
        }

        // Filter FAQ function
        function filterFAQ(category) {
            const buttons = document.querySelectorAll('.category-btn');

            // Update active button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Store current category for search function
            currentCategory = category;

            // Filter FAQs
            let filteredFAQs = allFAQs;
            if (category !== 'all') {
                filteredFAQs = allFAQs.filter(faq => faq.category === category);
            }

            displayFAQs(filteredFAQs);
        }

        // Search Function
        let currentCategory = 'all';

        function searchFAQ() {
            const searchTerm = document.getElementById('faqSearch').value.toLowerCase().trim();
            let filteredFAQs = allFAQs;

            // Apply category filter first
            if (currentCategory !== 'all') {
                filteredFAQs = filteredFAQs.filter(faq => faq.category === currentCategory);
            }

            // Apply search filter
            if (searchTerm) {
                filteredFAQs = filteredFAQs.filter(faq =>
                    faq.question.toLowerCase().includes(searchTerm) ||
                    faq.answer.toLowerCase().includes(searchTerm)
                );
            }

            displayFAQs(filteredFAQs);

            // Show no results message if needed
            if (filteredFAQs.length === 0) {
                const content = document.getElementById('faqContent');
                content.innerHTML = `
                    <div style="text-align: center; padding: var(--spacing-xxl); color: var(--text-secondary);">
                        <i class="fas fa-search" style="font-size: var(--font-size-3xl); margin-bottom: var(--spacing-lg); opacity: 0.3;"></i>
                        <h3 style="margin-bottom: var(--spacing-lg); color: var(--primary-color);">لم يتم العثور على نتائج</h3>
                        <p>لم نجد أي أسئلة تطابق بحثك "${searchTerm}"</p>
                        <p style="margin-top: var(--spacing-md);">جرب استخدام كلمات مختلفة أو تصفح الفئات المختلفة</p>
                    </div>
                `;
            }
        }

        // Initialize FAQ when Supabase is ready
        function initializeFAQ() {
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                loadFAQ();
            } else {
                setTimeout(initializeFAQ, 100);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeFAQ);
    </script>
</body>
</html>
