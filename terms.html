<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>الشروط والأحكام - Care</title>
    <meta name="description" content="الشروط والأحكام الخاصة بموقع Care للعناية بالبشرة والشعر - اطلع على جميع الشروط والسياسات">
    <meta name="keywords" content="شروط وأحكام, سياسة الخصوصية, العناية بالبشرة, الشعر, العراق">
    <meta name="author" content="Care">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#4a90a4">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/responsive-framework.css" rel="stylesheet">
    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background: linear-gradient(135deg,
                rgba(248, 249, 250, 0.3) 0%,
                rgba(255, 255, 255, 1) 50%,
                rgba(248, 249, 250, 0.3) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Header - Matching Homepage Design */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: 1.5rem;
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }





        /* Main Content Section */
        .main-content {
            padding: var(--spacing-xxl) 0;
            background: linear-gradient(135deg,
                rgba(248, 249, 250, 0.8) 0%,
                rgba(255, 255, 255, 0.9) 50%,
                rgba(248, 249, 250, 0.8) 100%);
            min-height: 60vh;
        }

        /* Page Header - Professional Design System Compliant */
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 3.5rem;
            margin-bottom: 2rem;
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .page-header p {
            font-size: 1.25rem;
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Terms Section - Main Content Area */
        .terms-section {
            padding: 4rem 0;
            background: var(--bg-secondary);
        }

        /* Terms Content Cards */
        .terms-content {
            display: grid;
            gap: var(--spacing-xl);
        }

        /* Professional Terms Cards with Expandable/Collapsible Functionality */
        .terms-card {
            background: white;
            border-radius: 16px;
            padding: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid var(--border-color);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .terms-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: 16px 16px 0 0;
        }

        .terms-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(74, 144, 164, 0.02) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            border-radius: 16px;
        }

        .terms-card:hover::after {
            opacity: 1;
        }

        .terms-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
            border-color: var(--primary-light);
        }

        .terms-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-xl);
            cursor: pointer;
            background: linear-gradient(135deg,
                rgba(74, 144, 164, 0.02) 0%,
                rgba(74, 144, 164, 0.05) 100%);
            border-bottom: 1px solid rgba(74, 144, 164, 0.1);
            transition: all 0.3s ease;
        }

        .terms-card-header:hover {
            background: linear-gradient(135deg,
                rgba(74, 144, 164, 0.05) 0%,
                rgba(74, 144, 164, 0.08) 100%);
        }

        .terms-card-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .terms-card-title h2 {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            transition: color 0.3s ease;
        }

        .terms-card-header:hover .terms-card-title h2 {
            color: var(--primary-color);
        }

        .terms-card-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
            padding: var(--spacing-sm);
            background: rgba(74, 144, 164, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .terms-card-header:hover .terms-card-title i {
            background: rgba(74, 144, 164, 0.2);
            transform: scale(1.1);
        }

        .toggle-icon {
            color: var(--primary-color);
            font-size: 1.2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: var(--spacing-sm);
            border-radius: 50%;
            background: rgba(74, 144, 164, 0.1);
        }

        .toggle-icon.expanded {
            transform: rotate(180deg);
            background: var(--primary-color);
            color: white;
        }

        .terms-card-content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
        }

        .terms-card-content.expanded {
            max-height: 2000px;
            padding: var(--spacing-xl);
        }

        .terms-card-content p {
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.8;
            margin-bottom: var(--spacing-md);
            white-space: pre-line;
        }

        .terms-card-content ul {
            padding-right: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .terms-card-content li {
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.6;
            margin-bottom: var(--spacing-sm);
            position: relative;
        }

        .terms-card-content li::before {
            content: '✓';
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            right: -1.5rem;
            background: rgba(74, 144, 164, 0.1);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        /* Terms Control Buttons */
        .terms-controls {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: 8px;
            font-size: var(--font-size-base);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--text-secondary);
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: var(--text-secondary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 102, 102, 0.3);
        }

        /* Loading State - Enhanced */
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xxl);
            color: var(--text-secondary);
            text-align: center;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--spacing-md);
        }

        .loading-placeholder p {
            font-size: var(--font-size-lg);
            font-weight: 500;
            margin: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* No Content State */
        .no-content-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xxl);
            color: var(--text-secondary);
            text-align: center;
        }

        .no-content-state i {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            opacity: 0.3;
        }

        .no-content-state h3 {
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .no-content-state p {
            font-size: var(--font-size-base);
            margin: 0;
        }

        /* Professional Terms Navigation - Enhanced Design System Compliant */
        .terms-nav {
            background: linear-gradient(135deg,
                var(--bg-primary) 0%,
                rgba(248, 249, 250, 0.95) 30%,
                var(--bg-primary) 100%);
            border-radius: 16px;
            padding: var(--spacing-xl) var(--spacing-xl);
            margin-top: var(--spacing-xxl);
            margin-bottom: var(--spacing-xxl);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 4px 16px rgba(74, 144, 164, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(74, 144, 164, 0.15);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .terms-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--primary-light) 50%,
                var(--primary-color) 100%);
            border-radius: 16px 16px 0 0;
        }

        .terms-nav::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(74, 144, 164, 0.02) 50%,
                transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            border-radius: 16px;
        }

        .terms-nav:hover::after {
            opacity: 1;
        }

        .terms-nav:hover {
            transform: translateY(-4px);
            box-shadow:
                0 16px 48px rgba(0, 0, 0, 0.12),
                0 8px 24px rgba(74, 144, 164, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: rgba(74, 144, 164, 0.25);
        }

        /* Navigation Header */
        .terms-nav h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-xl);
            font-size: var(--font-size-2xl);
            font-weight: 700;
            font-family: var(--font-family);
            position: relative;
            padding-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .terms-nav h3::before {
            content: '📋';
            font-size: 1.5rem;
            margin-left: var(--spacing-sm);
        }

        .terms-nav h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--primary-light) 100%);
            border-radius: 2px;
        }

        /* Navigation List */
        .terms-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .terms-nav li {
            margin-bottom: var(--spacing-sm);
            position: relative;
        }

        .terms-nav li:last-child {
            margin-bottom: 0;
        }

        /* Navigation Links - Professional Enhancement */
        .terms-nav a {
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 500;
            font-size: var(--font-size-base);
            font-family: var(--font-family);
            position: relative;
            background: transparent;
            border: 1px solid transparent;
            overflow: hidden;
        }

        .terms-nav a::before {
            content: '▶';
            font-size: 0.8rem;
            color: var(--primary-color);
            transition: all 0.3s ease;
            opacity: 0.7;
            margin-left: var(--spacing-xs);
        }

        .terms-nav a::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(74, 144, 164, 0.05) 50%,
                transparent 100%);
            transition: left 0.3s ease;
        }

        /* Hover and Active States */
        .terms-nav a:hover {
            color: var(--primary-color);
            background: linear-gradient(135deg,
                rgba(74, 144, 164, 0.08) 0%,
                rgba(74, 144, 164, 0.12) 100%);
            border-color: rgba(74, 144, 164, 0.2);
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.15);
        }

        .terms-nav a:hover::before {
            opacity: 1;
            transform: scale(1.2);
            color: var(--primary-color);
        }

        .terms-nav a:hover::after {
            left: 0;
        }

        .terms-nav a:active {
            transform: translateX(6px) scale(0.98);
        }

        /* Focus state for accessibility */
        .terms-nav a:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
            background: rgba(74, 144, 164, 0.1);
        }

        /* Current/Active section indicator */
        .terms-nav a.current {
            color: var(--primary-color);
            background: linear-gradient(135deg,
                rgba(74, 144, 164, 0.12) 0%,
                rgba(74, 144, 164, 0.08) 100%);
            border-color: var(--primary-color);
            font-weight: 600;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(74, 144, 164, 0.2);
        }

        .terms-nav a.current::before {
            content: '●';
            color: var(--primary-color);
            opacity: 1;
            animation: pulse 2s infinite;
        }

        /* Subtle pulse animation for current section */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        /* Navigation loading state */
        .terms-nav.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .terms-nav.loading::after {
            opacity: 1;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(74, 144, 164, 0.1) 50%,
                transparent 70%);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Enhanced navigation counter */
        .terms-nav h3 .nav-counter {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-weight: 400;
            margin-right: var(--spacing-sm);
        }

        /* Smooth entrance animation for navigation items */
        .terms-nav li {
            opacity: 0;
            transform: translateX(-20px);
            animation: slideInRight 0.3s ease forwards;
        }

        .terms-nav li:nth-child(1) { animation-delay: 0.1s; }
        .terms-nav li:nth-child(2) { animation-delay: 0.2s; }
        .terms-nav li:nth-child(3) { animation-delay: 0.3s; }
        .terms-nav li:nth-child(4) { animation-delay: 0.4s; }
        .terms-nav li:nth-child(5) { animation-delay: 0.5s; }
        .terms-nav li:nth-child(n+6) { animation-delay: 0.6s; }

        @keyframes slideInRight {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced Content Container */
        .terms-content {
            background: linear-gradient(145deg,
                rgba(255,255,255,0.98) 0%,
                rgba(248,249,250,0.95) 30%,
                rgba(255,255,255,0.98) 100%);
            border-radius: 30px;
            padding: 4rem 3rem;
            box-shadow:
                0 20px 50px rgba(0,0,0,0.12),
                0 10px 25px rgba(130, 135, 122, 0.15),
                0 4px 12px rgba(0,0,0,0.08);
            border: 3px solid rgba(130, 135, 122, 0.15);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            transition: all 0.4s ease;
        }

        .terms-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg,
                #82877a 0%,
                #6b7062 50%,
                #82877a 100%);
            box-shadow: 0 2px 8px rgba(130, 135, 122, 0.3);
        }

        .terms-content::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(130, 135, 122, 0.03) 50%,
                transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .terms-content:hover::after {
            opacity: 1;
        }

        /* Professional Section Styling */
        .section {
            margin-bottom: 4rem;
            padding-bottom: 3rem;
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
            position: relative;
        }

        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(130, 135, 122, 0.3), transparent);
            border-radius: 2px;
        }

        .section h2 {
            color: #121414;
            margin-bottom: 2rem;
            font-size: 2.2rem;
            font-weight: 800;
            position: relative;
            padding-bottom: 1rem;
            line-height: 1.3;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #82877a, #6b7062);
            border-radius: 2px;
        }

        .section h3 {
            color: #82877a;
            margin-bottom: 1.5rem;
            margin-top: 2rem;
            font-size: 1.6rem;
            font-weight: 700;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, #82877a, transparent);
        }

        .section p {
            margin-bottom: 1.5rem;
            color: #444;
            font-size: 1.1rem;
            line-height: 1.9;
            font-weight: 400;
        }

        .section ul {
            margin-bottom: 1.5rem;
            padding-right: 2.5rem;
        }

        .section li {
            margin-bottom: 0.8rem;
            color: #444;
            font-size: 1.1rem;
            line-height: 1.8;
            position: relative;
        }

        .section li::before {
            content: '•';
            color: #82877a;
            font-weight: bold;
            position: absolute;
            right: -1.5rem;
            font-size: 1.2rem;
        }

        /* Enhanced Highlight Boxes */
        .highlight {
            background: linear-gradient(135deg, #fff3cd, #fef9e7);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.1);
        }

        .highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ffc107, #ffb300);
        }

        .highlight h4 {
            color: #856404;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .highlight h4::before {
            content: '⚠️';
            font-size: 1.2rem;
        }

        .highlight p {
            color: #856404;
            margin: 0;
            font-size: 1.1rem;
            line-height: 1.7;
        }

        /* Enhanced Contact Info */
        .contact-info {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 2px solid rgba(40, 167, 69, 0.3);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.1);
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .contact-info h4 {
            color: #155724;
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .contact-info h4::before {
            content: '📞';
            font-size: 1.2rem;
        }

        .contact-info p {
            color: #155724;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
            line-height: 1.7;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Footer - Matching Homepage Design Exactly */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: 1.1rem;
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: 1.2rem;
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }



        /* Cross-Browser Compatibility and Accessibility Enhancements */

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .nav-menu {
                background: #000000;
                border: 2px solid #ffffff;
            }

            .nav-menu a {
                color: #ffffff;
                border-bottom: 1px solid #ffffff;
            }

            .nav-menu a:hover,
            .nav-menu a:focus {
                background: #ffffff;
                color: #000000;
            }



            .terms-nav,
            .terms-content {
                background: #000000;
                border: 2px solid #ffffff;
                color: #ffffff;
            }

            .terms-nav h3 {
                color: #ffffff;
            }

            .terms-nav a {
                color: #ffffff;
                border: 1px solid #ffffff;
            }

            .terms-nav a:hover,
            .terms-nav a:focus,
            .terms-nav a.current {
                background: #ffffff;
                color: #000000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                -webkit-animation-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                -webkit-animation-iteration-count: 1 !important;
                animation-iteration-count: 1 !important;
                -webkit-transition-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }

            .nav-menu,
            .terms-nav,
            .terms-content {
                -webkit-transition: none !important;
                transition: none !important;
            }

            .terms-nav a::before,
            .terms-nav li {
                animation: none !important;
            }

            .terms-nav a:hover {
                transform: none !important;
            }
        }



        /* Print styles */
        @media print {
            header,
            .nav-menu,
            .cart-icon,
            .terms-nav {
                display: none;
            }

            .page-header {
                padding: 2rem 0;
                background: none;
                color: #000000;
            }

            .terms-content {
                box-shadow: none;
                border: 1px solid #000000;
            }

            .section {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" data-setting="business_name">Care</a>
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="terms.html" class="active">الشروط والأحكام</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>
    </header>

    <section class="page-header terms-bg" id="termsPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">الشروط والأحكام</span>
                </nav>
                <h1>الشروط والأحكام</h1>
                <p class="terms-page-description" data-setting="terms_page_description">يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا</p>
            </div>
        </div>
    </section>

    <main class="terms-section">
        <div class="container">
            <!-- Database Status Alert -->
            <div id="dbStatusAlert" style="display: none; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 1rem; margin-bottom: 2rem; color: #856404;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong>
                </div>
                <p style="margin: 0.5rem 0 0 0;">يتم عرض الشروط والأحكام الافتراضية حالياً. لعرض المحتوى المحدث من قاعدة البيانات، يرجى التأكد من إنشاء جدول الشروط والأحكام في Supabase.</p>
            </div>

            <div class="terms-nav">
                <h3>المحتويات</h3>
                <ul id="termsNavigation">
                    <!-- Navigation will be loaded here -->
                </ul>
            </div>

            <!-- Loading indicator -->
            <div id="termsLoading" class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>جاري تحميل الشروط والأحكام...</p>
            </div>

            <!-- Terms Control Buttons -->
            <div class="terms-controls" id="termsControls" style="display: none; text-align: center; margin-bottom: var(--spacing-xl);">
                <button type="button" class="btn btn-outline-primary" onclick="expandAllTerms()">
                    <i class="fas fa-expand-arrows-alt"></i>
                    توسيع جميع الأقسام
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="collapseAllTerms()">
                    <i class="fas fa-compress-arrows-alt"></i>
                    طي جميع الأقسام
                </button>
            </div>

            <!-- Terms content will be loaded here -->
            <div class="terms-content" id="termsContent" style="display: none;">
                <!-- Content will be loaded here -->
            </div>

            <!-- No terms message -->
            <div id="noTermsMessage" class="no-content-state" style="display: none;">
                <i class="fas fa-file-contract"></i>
                <h3>لا توجد شروط وأحكام متاحة حالياً</h3>
                <p>نعمل على إضافة الشروط والأحكام قريباً</p>
            </div>


        </div>
    </main>

    <!-- Section Divider -->
    <div class="section-divider"></div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="https://wa.me/9647713688302" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تليغرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تليغرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>
        // Get shared Supabase client using singleton pattern
        function getSupabaseClient() {
            // Use the singleton pattern from supabase-config.js
            if (window.SupabaseConfig && typeof window.SupabaseConfig.getClient === 'function') {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global function if available
            if (window.getSupabaseClient && typeof window.getSupabaseClient === 'function') {
                return window.getSupabaseClient();
            }

            // Legacy fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            console.warn('⚠️ Supabase client not available. Make sure supabase-config.js is loaded.');
            return null;
        }

        let allTerms = [];

        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        // Load Terms & Conditions from database
        async function loadTerms() {
            const loading = document.getElementById('termsLoading');
            const content = document.getElementById('termsContent');
            const navigation = document.getElementById('termsNavigation');
            const noMessage = document.getElementById('noTermsMessage');

            if (!loading || !content || !navigation || !noMessage) {
                console.error('Required terms elements not found');
                return;
            }

            loading.style.display = 'block';
            content.style.display = 'none';
            content.innerHTML = '';
            navigation.innerHTML = '';
            noMessage.style.display = 'none';

            try {
                console.log('Attempting to load terms from Supabase...');

                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                // Try direct API call if Supabase client fails
                let terms, error;
                try {
                    const response = await supabase
                        .from('terms_conditions')
                        .select('*')
                        .eq('is_active', true)
                        .order('order_index', { ascending: true });
                    terms = response.data;
                    error = response.error;
                } catch (clientError) {
                    console.log('Supabase client failed, trying direct API call...');
                    const apiResponse = await fetch('https://krqijjttwllohulmdwgs.supabase.co/rest/v1/terms_conditions?is_active=eq.true&order=order_index.asc', {
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Content-Type': 'application/json'
                        }
                    });
                    if (apiResponse.ok) {
                        terms = await apiResponse.json();
                        error = null;
                    } else {
                        throw new Error(`API call failed: ${apiResponse.status}`);
                    }
                }

                console.log('Supabase terms response:', { data: terms, error });

                if (error) {
                    console.error('Supabase error:', error);
                    throw error;
                }

                loading.style.display = 'none';
                allTerms = terms || [];

                console.log(`Loaded ${allTerms.length} terms`);

                if (allTerms.length === 0) {
                    noMessage.style.display = 'block';
                    return;
                }

                displayTerms(allTerms);
                createNavigation(allTerms);

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading terms:', error);

                // Check if it's a table not found error
                if (error.message.includes('relation "terms_conditions" does not exist') ||
                    error.message.includes('table') ||
                    error.message.includes('404') ||
                    error.message.includes('relation') ||
                    error.message.includes('does not exist')) {
                    // Show default terms content
                    console.log('Table not found, showing default terms');
                    displayDefaultTerms();
                } else {
                    noMessage.style.display = 'block';
                    noMessage.innerHTML = `
                        <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 2rem; color: #e74c3c;"></i>
                        <h3>خطأ في تحميل الشروط والأحكام</h3>
                        <p style="margin-bottom: 1rem;">تفاصيل الخطأ: ${error.message}</p>
                        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                            <button onclick="loadTerms()" style="padding: 0.5rem 1rem; background: #82877a; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                إعادة المحاولة
                            </button>
                            <button onclick="displayDefaultTerms()" style="padding: 0.5rem 1rem; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                عرض الشروط الافتراضية
                            </button>
                        </div>
                    `;
                }
            }
        }

        // Display terms with expandable/collapsible cards
        function displayTerms(terms) {
            const loading = document.getElementById('termsLoading');
            const content = document.getElementById('termsContent');
            const controls = document.getElementById('termsControls');
            const noTermsMessage = document.getElementById('noTermsMessage');

            // Hide loading and no terms message
            if (loading) loading.style.display = 'none';
            if (noTermsMessage) noTermsMessage.style.display = 'none';

            let html = '';

            terms.forEach((term, index) => {
                const cardId = `terms-card-${index}`;
                const contentId = `terms-content-${index}`;
                const toggleId = `toggle-${index}`;
                const sectionId = `section-${index}`;

                html += `
                    <div class="terms-card" id="${cardId}" data-setting="terms_section_${index}_enabled">
                        <div class="terms-card-header" onclick="toggleTermsCard('${contentId}', '${toggleId}')">
                            <div class="terms-card-title">
                                <i class="fas fa-file-contract"></i>
                                <h2>${term.section_title}</h2>
                            </div>
                            <i class="fas fa-chevron-down toggle-icon" id="${toggleId}"></i>
                        </div>
                        <div class="terms-card-content" id="${contentId}">
                            <div id="${sectionId}">
                                <p>${term.section_content}</p>
                            </div>
                        </div>
                    </div>
                `;
            });

            content.innerHTML = html;
            content.style.display = 'block';

            // Show control buttons
            if (controls) {
                controls.style.display = 'block';
            }
        }

        // Toggle Terms Card Expansion
        function toggleTermsCard(contentId, toggleId) {
            const content = document.getElementById(contentId);
            const toggle = document.getElementById(toggleId);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                toggle.classList.add('expanded');
            }
        }

        // Expand all terms cards
        function expandAllTerms() {
            const contents = document.querySelectorAll('.terms-card-content');
            const toggles = document.querySelectorAll('.toggle-icon');

            contents.forEach(content => content.classList.add('expanded'));
            toggles.forEach(toggle => toggle.classList.add('expanded'));
        }

        // Collapse all terms cards
        function collapseAllTerms() {
            const contents = document.querySelectorAll('.terms-card-content');
            const toggles = document.querySelectorAll('.toggle-icon');

            contents.forEach(content => content.classList.remove('expanded'));
            toggles.forEach(toggle => toggle.classList.remove('expanded'));
        }

        // Create enhanced navigation with professional styling
        function createNavigation(terms) {
            const navigation = document.getElementById('termsNavigation');
            const navContainer = document.querySelector('.terms-nav');
            const navTitle = document.querySelector('.terms-nav h3');

            // Add loading state
            if (navContainer) {
                navContainer.classList.add('loading');
            }

            // Update title with counter
            if (navTitle && terms.length > 0) {
                navTitle.innerHTML = `📋 المحتويات <span class="nav-counter">(${terms.length} أقسام)</span>`;
            }

            let html = '';

            terms.forEach((term, index) => {
                const sectionId = `section-${index}`;
                const sectionNumber = index + 1;
                html += `
                    <li>
                        <a href="#${sectionId}"
                           onclick="scrollToSection('${sectionId}', this)"
                           data-section="${sectionId}"
                           class="nav-link"
                           title="انتقل إلى ${term.section_title}"
                           aria-label="القسم ${sectionNumber}: ${term.section_title}">
                            ${term.section_title}
                        </a>
                    </li>
                `;
            });

            navigation.innerHTML = html;

            // Remove loading state after content is loaded
            setTimeout(() => {
                if (navContainer) {
                    navContainer.classList.remove('loading');
                }

                // Initialize intersection observer for active section tracking
                initializeActiveNavigation();
            }, 300);
        }

        // Enhanced scroll to section with active state management
        function scrollToSection(sectionId, linkElement) {
            const element = document.getElementById(sectionId);
            if (element) {
                // Remove current class from all navigation links
                document.querySelectorAll('.terms-nav a').forEach(link => {
                    link.classList.remove('current');
                });

                // Add current class to clicked link
                if (linkElement) {
                    linkElement.classList.add('current');
                }

                // Smooth scroll to element with offset for fixed header
                const headerOffset = 120; // Account for fixed header
                const elementPosition = element.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        }

        // Initialize active navigation tracking with Intersection Observer
        function initializeActiveNavigation() {
            const sections = document.querySelectorAll('[id^="section-"]');
            const navLinks = document.querySelectorAll('.terms-nav a[data-section]');

            if (sections.length === 0 || navLinks.length === 0) return;

            const observerOptions = {
                root: null,
                rootMargin: '-120px 0px -50% 0px', // Account for header and better UX
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Remove current class from all links
                        navLinks.forEach(link => link.classList.remove('current'));

                        // Add current class to corresponding navigation link
                        const correspondingLink = document.querySelector(
                            `.terms-nav a[data-section="${entry.target.id}"]`
                        );
                        if (correspondingLink) {
                            correspondingLink.classList.add('current');
                        }
                    }
                });
            }, observerOptions);

            // Observe all sections
            sections.forEach(section => observer.observe(section));
        }

        // Display default terms when database is not available
        function displayDefaultTerms() {
            // Show database status alert
            const alert = document.getElementById('dbStatusAlert');
            if (alert) {
                alert.style.display = 'block';
            }
            const defaultTerms = [
                {
                    section_title: 'شروط الاستخدام العامة',
                    section_content: `مرحباً بكم في موقع Care للعناية بالبشرة والشعر. باستخدامكم لهذا الموقع، فإنكم توافقون على الالتزام بهذه الشروط والأحكام. يرجى قراءة هذه الشروط بعناية قبل استخدام الموقع أو شراء أي منتجات.

نحتفظ بالحق في تعديل هذه الشروط في أي وقت دون إشعار مسبق. استمراركم في استخدام الموقع يعني موافقتكم على أي تعديلات.

جميع المنتجات المعروضة على الموقع أصلية ومضمونة الجودة. نحن ملتزمون بتقديم أفضل منتجات العناية بالبشرة والشعر لعملائنا الكرام.`,
                    section_type: 'الشروط العامة'
                },
                {
                    section_title: 'آلية الطلب وطرق الدفع',
                    section_content: `يمكنكم طلب المنتجات من خلال الموقع الإلكتروني أو التواصل معنا عبر واتساب. نوفر حالياً طريقة الدفع عند الاستلام لجميع المحافظات العراقية.

خطوات الطلب:
1. اختيار المنتجات المطلوبة وإضافتها للسلة
2. ملء بيانات التوصيل (الاسم، رقم الهاتف، العنوان)
3. تأكيد الطلب
4. سيتم التواصل معكم خلال 24 ساعة لتأكيد الطلب
5. الدفع عند الاستلام

نحتفظ بالحق في إلغاء أي طلب في حالة عدم توفر المنتج أو وجود مشكلة في بيانات التوصيل.`,
                    section_type: 'الطلبات والدفع'
                },
                {
                    section_title: 'مناطق وأوقات التوصيل',
                    section_content: `نوفر خدمة التوصيل لجميع المحافظات العراقية مع اختلاف في أوقات ورسوم التوصيل:

بغداد:
- مدة التوصيل: 1-2 يوم عمل
- رسوم التوصيل: 5,000 دينار عراقي
- أوقات التوصيل: من 9 صباحاً حتى 8 مساءً

المحافظات الأخرى:
- مدة التوصيل: 2-4 أيام عمل
- رسوم التوصيل: 10,000 دينار عراقي
- أوقات التوصيل: حسب توفر خدمة التوصيل في المنطقة

سيتم التواصل معكم لتحديد موعد التسليم المناسب. في حالة عدم تواجدكم في الموعد المحدد، سيتم إعادة جدولة التوصيل.`,
                    section_type: 'التوصيل'
                },
                {
                    section_title: 'شروط الإرجاع والاستبدال',
                    section_content: `نوفر إمكانية إرجاع أو استبدال المنتجات وفقاً للشروط التالية:

شروط الإرجاع:
- يجب أن يكون المنتج في حالته الأصلية وغير مستخدم
- مدة الإرجاع: 7 أيام من تاريخ الاستلام
- يجب الاحتفاظ بالعبوة الأصلية والفاتورة
- لا يمكن إرجاع المنتجات المفتوحة أو المستخدمة لأسباب صحية

شروط الاستبدال:
- في حالة وجود عيب في المنتج
- في حالة وصول منتج مختلف عن المطلوب
- يتم الاستبدال مجاناً في هذه الحالات

لطلب الإرجاع أو الاستبدال، يرجى التواصل معنا خلال المدة المحددة.`,
                    section_type: 'الإرجاع والاستبدال'
                },
                {
                    section_title: 'سياسة الخصوصية وحماية البيانات',
                    section_content: `نحن ملتزمون بحماية خصوصيتكم وبياناتكم الشخصية. هذه السياسة توضح كيفية جمع واستخدام وحماية معلوماتكم:

البيانات التي نجمعها:
- الاسم ورقم الهاتف وعنوان التوصيل
- تفاصيل الطلبات والمشتريات
- بيانات التصفح على الموقع (cookies)

استخدام البيانات:
- معالجة وتنفيذ الطلبات
- التواصل معكم بخصوص الطلبات
- تحسين خدماتنا ومنتجاتنا
- إرسال عروض وتحديثات (بموافقتكم)

حماية البيانات:
- لا نشارك بياناتكم مع أطراف ثالثة
- نستخدم تقنيات أمان متقدمة لحماية البيانات
- يمكنكم طلب حذف بياناتكم في أي وقت`,
                    section_type: 'سياسة الخصوصية'
                },
                {
                    section_title: 'معلومات التواصل وخدمة العملاء',
                    section_content: `نحن هنا لخدمتكم ومساعدتكم في أي استفسار أو مشكلة:

طرق التواصل:
- واتساب: [رقم الواتساب]
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.care-iraq.com

أوقات العمل:
- السبت إلى الخميس: 9:00 ص - 8:00 م
- الجمعة: 2:00 م - 8:00 م
- العطل الرسمية: مغلق

خدمة العملاء:
- الرد على الاستفسارات خلال 24 ساعة
- متابعة الطلبات والشكاوى
- تقديم النصائح والإرشادات للعناية
- دعم فني للموقع الإلكتروني

نسعى دائماً لتقديم أفضل خدمة لعملائنا الكرام.`,
                    section_type: 'معلومات التواصل'
                }
            ];

            displayTerms(defaultTerms);
            createNavigation(defaultTerms);
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });





        // Toggle Terms Card Expansion
        function toggleTermsCard(contentId, toggleId) {
            const content = document.getElementById(contentId);
            const toggle = document.getElementById(toggleId);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                toggle.classList.add('expanded');
            }
        }

        // Show No Content State
        function showNoContentState() {
            const loadingState = document.getElementById('loadingState');
            const termsContent = document.getElementById('termsContent');
            const noContentState = document.getElementById('noContentState');

            loadingState.style.display = 'none';
            termsContent.style.display = 'none';
            noContentState.style.display = 'flex';
        }

        // Update Cart Count
        function updateCartCount() {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            const cartCountElement = document.getElementById('cartCount');
            if (cartCountElement) {
                cartCountElement.textContent = totalItems;
            }
        }





        // Initialize Page - Fixed to prevent multiple Supabase instances
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();

            // Use the main loadTerms function to prevent duplicate client creation
            console.log('🚀 Terms page: Initializing...');

            // Check if Supabase is already loaded
            if (window.supabase) {
                console.log('✅ Terms page: Supabase available, loading terms...');
                loadTerms();
            } else {
                console.log('⏳ Terms page: Waiting for Supabase to load...');
                // Wait for Supabase to load with timeout
                let attempts = 0;
                const maxAttempts = 30; // 3 seconds max wait

                const checkSupabase = () => {
                    attempts++;
                    if (window.supabase) {
                        console.log('✅ Terms page: Supabase loaded, loading terms...');
                        loadTerms();
                    } else if (attempts < maxAttempts) {
                        setTimeout(checkSupabase, 100);
                    } else {
                        console.warn('⚠️ Terms page: Supabase failed to load, showing default terms');
                        displayDefaultTerms();
                    }
                };

                setTimeout(checkSupabase, 100);
            }
        });
    </script>
</body>
</html>
