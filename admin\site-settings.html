<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>إعدادات الموقع - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Load shared Supabase configuration (singleton pattern) -->
    <script src="../js/supabase-config.js"></script>
    <!-- Removed conflicting sidebar scripts - using unified system -->
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Enhanced Site Settings specific styles - base styles are in enhanced-sidebar.css */

        /* Collapsible Section Styles */
        .form-section {
            margin-bottom: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8ecef;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-section:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        /* Collapsible Section Header */
        .collapsible-section .section-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            color: white;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: none;
        }

        .collapsible-section .section-header:hover {
            background: linear-gradient(135deg, #3d7a8c 0%, #243342 100%);
        }

        .collapsible-section .section-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .collapsible-section .section-header h3 i {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section-toggle-icon {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
            opacity: 0.8;
        }

        .section-toggle-icon.expanded {
            transform: rotate(180deg);
        }

        /* Section Content */
        .section-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), padding 0.4s ease;
            padding: 0 2rem;
        }

        .section-content.expanded {
            max-height: 5000px;
            padding: 2rem;
        }

        /* RTL Support for Collapsible Sections */
        .collapsible-section .section-header {
            direction: rtl;
            text-align: right;
        }

        .collapsible-section .section-header h3 {
            flex-direction: row-reverse;
        }

        /* Enhanced Benefit Item Header Styling */
        .benefit-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.2rem;
            padding: 1.2rem;
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.15) 0%, rgba(44, 62, 80, 0.08) 100%);
            border-radius: 10px;
            border: 2px solid rgba(74, 144, 164, 0.25);
            position: relative;
            overflow: hidden;
        }

        .benefit-item-header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, #2c3e50 100%);
        }

        .benefit-item-header h5 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .benefit-item-header h5::before {
            content: "⚙️";
            font-size: 1.1rem;
        }

        .benefit-item-header .form-group {
            margin: 0;
        }

        .benefit-item-header label {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            font-size: 0.95rem;
            color: #4a90a4;
            font-weight: 600;
            cursor: pointer;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .benefit-item-header label:hover {
            background: rgba(74, 144, 164, 0.1);
            color: #2c3e50;
        }

        .benefit-item-header input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        /* Benefits Summary Section Styling */
        .benefits-summary-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 3px solid var(--primary-color);
        }

        .benefits-summary-section h4 {
            position: relative;
        }

        .benefits-summary-section h4::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #2c3e50 100%);
            border-radius: 2px;
        }

        /* Performance optimization - Use CSS classes instead of direct style manipulation */
        .hidden {
            display: none !important;
        }

        .visible {
            display: block !important;
        }

        .section-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
        }

        .section-header:hover {
            background: linear-gradient(135deg, #3a7a8a 0%, #1c2e40 100%);
        }

        .section-header h2 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section-header i {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .section-toggle-icon {
            font-size: 1.2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }

        .section-toggle-icon.expanded {
            transform: rotate(180deg);
            background: rgba(255, 255, 255, 0.2);
        }

        .section-content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
        }

        .section-content.expanded {
            max-height: 5000px;
            padding: 2rem;
        }

        /* Feature Header Styles */
        .feature-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #dee2e6;
        }

        .feature-header h4 {
            margin: 0;
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .feature-header .toggle-container {
            margin: 0;
        }

        .feature-header .toggle-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #4a90a4;
        }

        /* Welcome Section */
        .welcome-section .card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .welcome-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        /* Enhanced Messages */
        .messages-container {
            margin-bottom: var(--spacing-lg);
        }

        .message {
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            transition: var(--transition-base);
        }

        .message.success {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
            color: var(--color-success);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .message.error {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
            color: var(--color-danger);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .message.warning {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .message::before {
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .message.success::before {
            content: '\f058'; /* fa-check-circle */
        }

        .message.error::before {
            content: '\f06a'; /* fa-exclamation-circle */
        }

        .message.warning::before {
            content: '\f071'; /* fa-exclamation-triangle */
        }

        /* Enhanced Success Notification System */
        .enhanced-success-notification {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 10000;
            max-width: 450px;
            width: calc(100% - 40px);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            box-shadow:
                0 20px 40px rgba(40, 167, 69, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(40, 167, 69, 0.1);
            transform: translateX(500px) scale(0.8);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        .enhanced-success-notification.show {
            transform: translateX(0) scale(1);
            opacity: 1;
        }

        .enhanced-success-notification.hide {
            transform: translateX(500px) scale(0.8);
            opacity: 0;
        }

        .notification-content {
            padding: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            width: 55px;
            height: 55px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            animation: successPulse 0.8s ease-out;
        }

        @keyframes successPulse {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .notification-text {
            flex: 1;
            color: #2d3436;
        }

        .notification-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #28a745;
            line-height: 1.3;
        }

        .notification-message {
            font-size: 1rem;
            color: #636e72;
            line-height: 1.5;
            margin-bottom: 0;
        }

        .notification-close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #636e72;
            font-size: 1.2rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .notification-close:hover {
            background: rgba(0, 0, 0, 0.1);
            opacity: 1;
            transform: scale(1.1);
        }

        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border-radius: 0 0 15px 15px;
            animation: progressBar 5s linear forwards;
        }

        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* Responsive design for enhanced notifications */
        @media (max-width: 768px) {
            .enhanced-success-notification {
                top: 80px;
                right: 10px;
                left: 10px;
                max-width: none;
                width: calc(100% - 20px);
            }

            .enhanced-success-notification .notification-content {
                padding: 1rem;
                border-radius: 10px;
            }

            .enhanced-success-notification .notification-icon {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
            }

            .enhanced-success-notification .notification-title {
                font-size: 1rem;
            }

            .enhanced-success-notification .notification-message {
                font-size: 0.9rem;
            }
        }

        /* Enhanced Settings Navigation Tabs */
        .settings-nav {
            /* Enhanced background with subtle texture */
            background: linear-gradient(135deg,
                var(--color-bg-primary) 0%,
                #fafbfc 50%,
                #f8f9fa 100%);
            border-radius: var(--radius-lg);
            /* Enhanced multi-layer shadow system */
            box-shadow:
                var(--shadow-md),
                0 1px 3px rgba(130, 135, 122, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(130, 135, 122, 0.1);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
            position: relative;
            /* Performance optimizations */
            will-change: transform, box-shadow;
            contain: layout style;
        }

        /* Add subtle top accent line */
        .settings-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            /* Enhanced gradient with more stops */
            background: linear-gradient(90deg,
                var(--color-primary) 0%,
                var(--color-primary-dark) 25%,
                var(--color-primary-light) 50%,
                var(--color-primary-dark) 75%,
                var(--color-primary) 100%);
            opacity: 0.8;
            /* Subtle animation */
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .settings-nav:hover {
            transform: translateY(-2px);
            /* Enhanced hover shadow with multiple layers */
            box-shadow:
                var(--shadow-lg),
                0 8px 32px rgba(130, 135, 122, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
            border-color: rgba(130, 135, 122, 0.2);
        }

        .settings-nav:hover::before {
            opacity: 1;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            /* Enhanced background with subtle gradient */
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.02) 0%,
                rgba(130, 135, 122, 0.01) 50%,
                rgba(130, 135, 122, 0.03) 100%);
            padding: var(--spacing-xs);
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            position: relative;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            padding: var(--spacing-lg) var(--spacing-xl);
            border: none;
            background: transparent;
            color: var(--color-text-secondary);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            min-width: 140px;
            justify-content: center;
            border-bottom: 3px solid transparent;
            border-radius: var(--radius-md);
            margin: var(--spacing-xs);
            position: relative;
            /* Enhanced styling */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* Performance optimizations */
            will-change: transform, background-color;
            contain: layout style;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.1) 0%,
                rgba(130, 135, 122, 0.05) 100%);
            opacity: 0;
            transition: var(--transition-base);
            pointer-events: none;
        }

        .nav-tab:hover::before {
            opacity: 1;
        }

        .nav-tab.active {
            /* Enhanced gradient with more depth */
            background: linear-gradient(135deg,
                var(--color-primary) 0%,
                var(--color-primary-dark) 50%,
                var(--color-primary) 100%);
            color: white;
            transform: translateY(-3px) scale(1.02);
            /* Enhanced shadow with multiple layers */
            box-shadow:
                var(--shadow-lg),
                0 8px 25px rgba(130, 135, 122, 0.3),
                0 4px 15px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-bottom-color: var(--color-primary-light);
            border: 1px solid rgba(255, 255, 255, 0.1);
            /* Add subtle glow effect */
            position: relative;
            z-index: 2;
        }

        /* Add glow effect for active tab */
        .nav-tab.active::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg,
                var(--color-primary-light) 0%,
                var(--color-primary) 50%,
                var(--color-primary-dark) 100%);
            border-radius: var(--radius-md);
            z-index: -1;
            opacity: 0.3;
            filter: blur(4px);
        }

        .nav-tab.active::before {
            display: none;
        }

        .nav-tab:hover:not(.active) {
            color: var(--color-primary);
            transform: translateY(-2px) scale(1.01);
            /* Enhanced hover background */
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.08) 0%,
                rgba(130, 135, 122, 0.05) 50%,
                rgba(130, 135, 122, 0.08) 100%);
            box-shadow:
                var(--shadow-sm),
                0 4px 12px rgba(130, 135, 122, 0.1);
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .nav-tab i {
            font-size: 1.1rem;
            transition: var(--transition-base);
        }

        .nav-tab:hover i {
            transform: scale(1.1);
        }

        .nav-tab.active i {
            transform: scale(1.05);
        }

        /* Enhanced Settings Container */
        .settings-container {
            display: grid;
            gap: var(--spacing-xl);
        }

        .settings-section {
            /* Enhanced background with subtle texture */
            background: linear-gradient(135deg,
                var(--color-bg-primary) 0%,
                #fafbfc 50%,
                #f8f9fa 100%);
            border-radius: var(--radius-lg);
            /* Enhanced multi-layer shadow system */
            box-shadow:
                var(--shadow-md),
                0 1px 3px rgba(130, 135, 122, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(130, 135, 122, 0.1);
            padding: var(--spacing-xl);
            display: none;
            position: relative;
            overflow: hidden;
            /* Performance optimizations */
            will-change: transform, box-shadow;
            contain: layout style;
        }

        /* Add subtle top accent line for active sections */
        .settings-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            /* Enhanced gradient with more stops */
            background: linear-gradient(90deg,
                var(--color-primary) 0%,
                var(--color-primary-dark) 25%,
                var(--color-primary-light) 50%,
                var(--color-primary-dark) 75%,
                var(--color-primary) 100%);
            opacity: 0;
            transition: var(--transition-base);
            /* Subtle animation */
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        .settings-section.active {
            display: block;
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateY(-2px);
            /* Enhanced active shadow with multiple layers */
            box-shadow:
                var(--shadow-lg),
                0 8px 32px rgba(130, 135, 122, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
            border-color: rgba(130, 135, 122, 0.2);
        }

        .settings-section.active::before {
            opacity: 0.8;
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
                filter: blur(2px);
            }
            50% {
                opacity: 0.7;
                transform: translateY(10px) scale(0.98);
                filter: blur(1px);
            }
            100% {
                opacity: 1;
                transform: translateY(-2px) scale(1);
                filter: blur(0);
            }
        }

        /* ===== ENHANCED ARABIC TYPOGRAPHY SYSTEM ===== */

        /* Enhanced Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-xl);
            border-bottom: 3px solid rgba(130, 135, 122, 0.1);
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            bottom: -3px;
            right: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            border-radius: 2px;
        }

        .section-header i {
            font-size: 1.8rem;
            color: var(--color-primary);
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.1) 0%,
                rgba(130, 135, 122, 0.05) 100%);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-base);
        }

        .section-header:hover i {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        .section-header h2 {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--color-text-primary);
            line-height: 1.3;
            letter-spacing: 0.02em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        /* Card Headers Enhancement */
        .card-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-xl);
            border-bottom: 3px solid rgba(130, 135, 122, 0.1);
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: -3px;
            right: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            border-radius: 2px;
        }

        .card-header i {
            font-size: 1.8rem;
            color: var(--color-primary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-base);
        }

        .card-header:hover i {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        .card-header h1,
        .card-header h2,
        .card-header h3 {
            color: var(--color-text-primary);
            font-weight: 800;
            margin: 0;
            flex: 1;
            line-height: 1.3;
            letter-spacing: 0.02em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .card-header h1 {
            font-size: 2.2rem;
        }

        .card-header h2 {
            font-size: 1.8rem;
        }

        .card-header h3 {
            font-size: 1.5rem;
        }

        /* Enhanced Form Sections */
        .form-section {
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.02) 0%,
                rgba(130, 135, 122, 0.01) 100%);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            border: 1px solid rgba(130, 135, 122, 0.1);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-base);
        }

        .form-section:hover {
            box-shadow: var(--shadow-md);
            border-color: rgba(130, 135, 122, 0.15);
        }

        .form-section h3 {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            font-size: 1.4rem;
            font-weight: 800;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
            line-height: 1.3;
            letter-spacing: 0.02em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .form-section h3::after {
            content: '';
            position: absolute;
            bottom: -2px;
            right: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            border-radius: 1px;
        }

        .form-section h3 i {
            font-size: 1.3rem;
            color: var(--color-primary);
            background: rgba(130, 135, 122, 0.1);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            transition: var(--transition-base);
        }

        .form-section:hover h3 i {
            transform: scale(1.05);
            background: rgba(130, 135, 122, 0.15);
        }

        /* ===== ENHANCED PROFESSIONAL COLOR SYSTEM ===== */

        /* Professional Color Enhancements */
        :root {
            /* Enhanced Primary Color Variations */
            --color-primary-50: #f8f9f7;
            --color-primary-100: #f1f3ef;
            --color-primary-200: #e3e7de;
            --color-primary-300: #d5dbcd;
            --color-primary-400: #b8c2ab;
            --color-primary-500: var(--color-primary); /* #82877a */
            --color-primary-600: #6b7062;
            --color-primary-700: #5a5f54;
            --color-primary-800: #484c45;
            --color-primary-900: #363937;

            /* Enhanced Success Color Variations */
            --color-success-50: #f0fff4;
            --color-success-100: #dcfce7;
            --color-success-200: #bbf7d0;
            --color-success-300: #86efac;
            --color-success-400: #4ade80;
            --color-success-500: var(--color-success); /* #28a745 */
            --color-success-600: #16a34a;
            --color-success-700: #15803d;
            --color-success-800: #166534;
            --color-success-900: #14532d;

            /* Enhanced Danger Color Variations */
            --color-danger-50: #fef2f2;
            --color-danger-100: #fee2e2;
            --color-danger-200: #fecaca;
            --color-danger-300: #fca5a5;
            --color-danger-400: #f87171;
            --color-danger-500: var(--color-danger); /* #dc3545 */
            --color-danger-600: #dc2626;
            --color-danger-700: #b91c1c;
            --color-danger-800: #991b1b;
            --color-danger-900: #7f1d1d;

            /* Enhanced Warning Color Variations */
            --color-warning-50: #fffbeb;
            --color-warning-100: #fef3c7;
            --color-warning-200: #fde68a;
            --color-warning-300: #fcd34d;
            --color-warning-400: #fbbf24;
            --color-warning-500: var(--color-warning); /* #ffc107 */
            --color-warning-600: #d97706;
            --color-warning-700: #b45309;
            --color-warning-800: #92400e;
            --color-warning-900: #78350f;

            /* Enhanced Info Color Variations */
            --color-info-50: #f0f9ff;
            --color-info-100: #e0f2fe;
            --color-info-200: #bae6fd;
            --color-info-300: #7dd3fc;
            --color-info-400: #38bdf8;
            --color-info-500: var(--color-info); /* #17a2b8 */
            --color-info-600: #0284c7;
            --color-info-700: #0369a1;
            --color-info-800: #075985;
            --color-info-900: #0c4a6e;

            /* Enhanced Neutral Colors */
            --color-gray-50: #fafafa;
            --color-gray-100: #f5f5f5;
            --color-gray-200: #e5e5e5;
            --color-gray-300: #d4d4d4;
            --color-gray-400: #a3a3a3;
            --color-gray-500: #737373;
            --color-gray-600: #525252;
            --color-gray-700: #404040;
            --color-gray-800: #262626;
            --color-gray-900: #171717;
        }

        /* ===== ENHANCED PROFESSIONAL FORM SYSTEM ===== */

        /* ===== ENHANCED LAYOUT AND SPACING SYSTEM ===== */

        /* Enhanced Form Grid Layout */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xxl);
            align-items: start;
        }

        /* Enhanced Settings Container */
        .settings-container {
            display: grid;
            gap: var(--spacing-xxl);
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        /* Enhanced Main Content Layout */
        .main-content {
            padding: var(--spacing-xl);
            min-height: 100vh;
            background: linear-gradient(135deg,
                var(--color-gray-50) 0%,
                var(--color-bg-primary) 50%,
                var(--color-gray-50) 100%);
        }

        /* Enhanced Content Wrapper */
        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        /* Enhanced Form Groups */
        .form-group {
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-md);
            font-size: 1rem;
            line-height: 1.4;
            /* Enhanced label styling */
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            letter-spacing: 0.02em;
        }

        .form-group label i {
            font-size: 1.1rem;
            opacity: 0.8;
            transition: var(--transition-base);
        }

        .form-group:focus-within label i {
            opacity: 1;
            transform: scale(1.1);
        }

        /* Enhanced Input Fields */
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: var(--spacing-lg) var(--spacing-md);
            border: 2px solid var(--color-gray-200);
            border-radius: var(--radius-md);
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.5;
            color: var(--color-gray-800);
            transition: var(--transition-base);
            /* Enhanced background with subtle gradient */
            background: linear-gradient(135deg,
                var(--color-gray-50) 0%,
                var(--color-bg-primary) 100%);
            box-shadow:
                inset 0 1px 3px rgba(0, 0, 0, 0.05),
                0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
            /* Performance optimizations */
            will-change: border-color, box-shadow, transform;
        }

        /* Enhanced Focus States */
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--color-primary-500);
            color: var(--color-gray-900);
            /* Enhanced focus shadow with multiple layers */
            box-shadow:
                0 0 0 4px var(--color-primary-100),
                0 4px 12px var(--color-primary-200),
                inset 0 1px 3px rgba(0, 0, 0, 0.05),
                0 1px 0 rgba(255, 255, 255, 0.8);
            transform: translateY(-1px);
            background: linear-gradient(135deg,
                var(--color-bg-primary) 0%,
                var(--color-gray-50) 100%);
        }

        /* Enhanced Hover States */
        .form-group input:hover:not(:focus),
        .form-group textarea:hover:not(:focus),
        .form-group select:hover:not(:focus) {
            border-color: var(--color-gray-300);
            box-shadow:
                0 2px 8px var(--color-gray-200),
                inset 0 1px 3px rgba(0, 0, 0, 0.05),
                0 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* Enhanced Textarea Styling */
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.6;
        }

        /* Enhanced Select Styling */
        .form-group select {
            cursor: pointer;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 12px center;
            background-repeat: no-repeat;
            background-size: 16px 12px;
            padding-left: 40px;
        }

        /* Enhanced Form Row Layout */
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-xl);
            align-items: start;
            margin-bottom: var(--spacing-xl);
        }

        /* Enhanced Form Group Spacing */
        .form-group {
            margin-bottom: var(--spacing-xl);
            position: relative;
        }

        /* Enhanced Visual Hierarchy */
        .settings-section + .settings-section {
            margin-top: var(--spacing-xxl);
        }

        .form-section + .form-section {
            margin-top: var(--spacing-xl);
        }

        /* Enhanced Content Spacing */
        .page-content {
            padding: var(--spacing-xl) 0;
        }

        .section-content {
            padding: var(--spacing-xl);
        }

        /* Form Help Text */
        .form-help {
            display: block;
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--color-text-muted);
            line-height: 1.4;
            font-style: italic;
        }

        /* Enhanced Validation States */
        .form-group.has-error input,
        .form-group.has-error textarea,
        .form-group.has-error select {
            border-color: var(--color-danger-500);
            color: var(--color-danger-800);
            background: linear-gradient(135deg,
                var(--color-danger-50) 0%,
                var(--color-danger-100) 100%);
            box-shadow:
                0 0 0 3px var(--color-danger-100),
                inset 0 1px 3px var(--color-danger-200);
        }

        .form-group.has-success input,
        .form-group.has-success textarea,
        .form-group.has-success select {
            border-color: var(--color-success-500);
            color: var(--color-success-800);
            background: linear-gradient(135deg,
                var(--color-success-50) 0%,
                var(--color-success-100) 100%);
            box-shadow:
                0 0 0 3px var(--color-success-100),
                inset 0 1px 3px var(--color-success-200);
        }

        /* Validation Messages */
        .form-error,
        .form-success {
            display: block;
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.4;
        }

        .form-error {
            color: var(--color-danger);
        }

        .form-success {
            color: var(--color-success);
        }

        /* Required Field Indicator */
        .form-group label.required::after {
            content: ' *';
            color: var(--color-danger);
            font-weight: 700;
        }

        /* Placeholder Styling */
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: var(--color-text-muted);
            opacity: 0.7;
            font-style: italic;
        }

        /* ===== ENHANCED INTERACTIVE ELEMENTS SYSTEM ===== */

        /* Enhanced Form Actions */
        .form-actions {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xxl);
            padding-top: var(--spacing-xxl);
            border-top: 3px solid var(--color-gray-200);
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;
            position: relative;
        }

        .form-actions::before {
            content: '';
            position: absolute;
            top: -3px;
            right: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            border-radius: 2px;
        }

        /* Enhanced Button Styling */
        .form-actions .btn {
            min-width: 180px;
            font-weight: 700;
            letter-spacing: 0.02em;
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--radius-lg);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-actions .btn:first-child {
            margin-left: 0;
        }

        /* Enhanced Button Hover Effects */
        .form-actions .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.5s;
        }

        .form-actions .btn:hover::before {
            left: 100%;
        }

        /* Enhanced Primary Button */
        .btn-primary {
            background: linear-gradient(135deg,
                var(--color-primary-500) 0%,
                var(--color-primary-600) 50%,
                var(--color-primary-500) 100%);
            border: 2px solid var(--color-primary-600);
            color: white;
            box-shadow:
                0 4px 15px var(--color-primary-200),
                0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 8px 25px var(--color-primary-300),
                0 4px 15px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg,
                var(--color-primary-400) 0%,
                var(--color-primary-500) 50%,
                var(--color-primary-600) 100%);
        }

        .btn-primary:active {
            transform: translateY(0) scale(0.98);
        }

        /* Enhanced Secondary Button */
        .btn-secondary {
            background: linear-gradient(135deg,
                var(--color-gray-500) 0%,
                var(--color-gray-600) 50%,
                var(--color-gray-500) 100%);
            border: 2px solid var(--color-gray-600);
            color: white;
            box-shadow:
                0 4px 15px var(--color-gray-200),
                0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 8px 25px var(--color-gray-300),
                0 4px 15px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg,
                var(--color-gray-400) 0%,
                var(--color-gray-500) 50%,
                var(--color-gray-600) 100%);
        }

        /* Enhanced Outline Button */
        .btn-outline-primary {
            background: transparent;
            border: 2px solid var(--color-primary-500);
            color: var(--color-primary-600);
            box-shadow:
                0 2px 8px var(--color-primary-100),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .btn-outline-primary:hover {
            background: var(--color-primary-50);
            border-color: var(--color-primary-600);
            color: var(--color-primary-700);
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 8px 25px var(--color-primary-200),
                0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Loading States and Animations */
        .btn.loading {
            position: relative;
            color: transparent;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Enhanced Form Validation Feedback */
        .form-feedback {
            margin-top: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            animation: slideInUp 0.3s ease;
        }

        .form-feedback.success {
            background: var(--color-success-50);
            color: var(--color-success-700);
            border: 1px solid var(--color-success-200);
        }

        .form-feedback.error {
            background: var(--color-danger-50);
            color: var(--color-danger-700);
            border: 1px solid var(--color-danger-200);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-xl);
        }

        .feature-item {
            background: var(--color-bg-primary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: var(--transition-base);
        }

        /* ===== ENHANCED RTL ARABIC LAYOUT OPTIMIZATION ===== */

        /* Arabic Typography Enhancements */
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            font-feature-settings: 'liga' 1, 'kern' 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Enhanced Arabic Text Rendering */
        .form-group label,
        .section-header h2,
        .card-header h2,
        .form-section h3,
        .nav-tab {
            font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1;
            text-rendering: optimizeLegibility;
            line-height: 1.6;
            word-spacing: 0.1em;
        }

        /* RTL-Specific Form Enhancements */
        .form-group input,
        .form-group textarea,
        .form-group select {
            direction: rtl;
            text-align: right;
            unicode-bidi: plaintext;
        }

        .form-group input[type="email"],
        .form-group input[type="url"],
        .form-group input[type="tel"] {
            direction: ltr;
            text-align: left;
        }

        /* Enhanced RTL Icon Positioning */
        .form-group label i,
        .section-header i,
        .card-header i,
        .form-section h3 i {
            margin-left: var(--spacing-sm);
            margin-right: 0;
        }

        /* RTL Button Icon Positioning */
        .btn i {
            margin-left: var(--spacing-sm);
            margin-right: 0;
        }

        /* Enhanced RTL Navigation */
        .nav-tab i {
            margin-left: var(--spacing-sm);
            margin-right: 0;
        }

        /* RTL Form Actions Alignment */
        .form-actions {
            justify-content: flex-start;
        }

        /* Enhanced Arabic Number Formatting */
        .arabic-numbers {
            font-variant-numeric: tabular-nums;
            direction: ltr;
            unicode-bidi: embed;
        }

        /* RTL Gradient Positioning */
        .section-header::after,
        .card-header::after,
        .form-section h3::after,
        .form-actions::before {
            right: 0;
            left: auto;
        }

        .feature-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .feature-item h4 {
            color: var(--color-primary);
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
        }

        /* Enhanced Social Preview */
        .social-preview {
            background: rgba(130, 135, 122, 0.02);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .social-preview h4 {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--color-primary);
            margin-bottom: var(--spacing-md);
        }

        .social-links-preview {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .social-link-preview {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--color-bg-primary);
            border-radius: var(--radius-md);
            border: 1px solid rgba(130, 135, 122, 0.1);
            text-decoration: none;
            transition: var(--transition-base);
        }

        .social-link-preview:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        /* ===== ENHANCED COMPREHENSIVE RESPONSIVE DESIGN ===== */

        /* Large Desktop (1400px+) */
        @media (min-width: 1400px) {
            .settings-container {
                max-width: 1600px;
                padding: 0 var(--spacing-xl);
            }

            .form-grid {
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: var(--spacing-xxl);
            }

            .main-content {
                padding: var(--spacing-xxl);
            }
        }

        /* Desktop (1200px - 1399px) */
        @media (min-width: 1200px) and (max-width: 1399px) {
            .settings-container {
                max-width: 1200px;
            }

            .form-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        /* Tablet Landscape (992px - 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-content {
                padding: var(--spacing-lg);
            }

            .settings-container {
                padding: 0 var(--spacing-sm);
            }

            .form-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: var(--spacing-lg);
            }

            .form-actions .btn {
                min-width: 150px;
                padding: var(--spacing-md) var(--spacing-lg);
            }
        }

        /* Tablet Portrait (768px - 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .main-content {
                padding: var(--spacing-md);
            }

            .settings-container {
                gap: var(--spacing-xl);
            }

            .form-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: var(--spacing-md);
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .section-header h2,
            .card-header h2 {
                font-size: 1.5rem;
            }

            .form-section h3 {
                font-size: 1.2rem;
            }
        }

        /* Enhanced Mobile Design (max-width: 767px) */
        @media (max-width: 768px) {
            .welcome-section .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .welcome-actions {
                width: 100%;
                justify-content: stretch;
            }

            .welcome-actions .btn {
                flex: 1;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                border-bottom: none;
                border-right: 3px solid transparent;
                min-width: auto;
            }

            .nav-tab.active {
                border-right-color: var(--color-primary);
                transform: translateX(2px);
            }

            .nav-tab:hover:not(.active) {
                transform: translateX(1px);
            }

            .settings-section {
                padding: var(--spacing-md);
            }

            .form-section {
                padding: var(--spacing-md);
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .form-actions .btn {
                width: 100%;
            }

            .social-links-preview {
                flex-direction: column;
            }

            .social-link-preview {
                width: 100%;
                justify-content: center;
            }

            /* Enhanced Mobile Form Styling */
            .form-group input,
            .form-group textarea,
            .form-group select {
                padding: var(--spacing-md);
                font-size: 1rem;
                border-radius: var(--radius-md);
            }

            .form-group label {
                font-size: 0.95rem;
                margin-bottom: var(--spacing-sm);
            }

            .section-header,
            .card-header {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-md);
                padding-bottom: var(--spacing-md);
            }

            .section-header h2,
            .card-header h2 {
                font-size: 1.4rem;
            }

            .form-section h3 {
                font-size: 1.1rem;
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-sm);
            }

            .welcome-section .welcome-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .feature-item {
                padding: var(--spacing-md);
                text-align: center;
            }
        }

        /* Extra Small Mobile (max-width: 480px) */
        @media (max-width: 480px) {
            .main-content {
                padding: var(--spacing-xs);
            }

            .settings-container {
                gap: var(--spacing-md);
            }

            .form-section {
                padding: var(--spacing-md);
                border-radius: var(--radius-md);
            }

            .section-header i,
            .card-header i {
                font-size: 1.5rem;
                padding: var(--spacing-md);
            }

            .section-header h2,
            .card-header h2 {
                font-size: 1.2rem;
            }

            .form-section h3 {
                font-size: 1rem;
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 0.95rem;
            }

            .form-actions .btn {
                padding: var(--spacing-md);
                font-size: 0.9rem;
                min-width: auto;
            }

            .nav-tab {
                padding: var(--spacing-md);
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .settings-section {
                padding: var(--spacing-sm);
            }

            .form-section {
                padding: var(--spacing-sm);
            }

            .section-header {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-sm);
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                padding: var(--spacing-sm);
            }

            .feature-item {
                padding: var(--spacing-md);
            }
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .settings-section {
            animation: fadeInUp 0.6s ease forwards;
        }

        .feature-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .feature-item:nth-child(1) { animation-delay: 0.1s; }
        .feature-item:nth-child(2) { animation-delay: 0.2s; }
        .feature-item:nth-child(3) { animation-delay: 0.3s; }
        .feature-item:nth-child(4) { animation-delay: 0.4s; }

        /* Print styles */
        @media print {
            .welcome-actions,
            .form-actions,
            .nav-tabs {
                display: none !important;
            }

            .settings-section {
                display: block !important;
                break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                margin-bottom: var(--spacing-lg) !important;
            }

            .form-section {
                break-inside: avoid;
            }
        }

        /* Enhanced Site Settings Styling */

        /* Form Help Text */
        .form-help {
            display: block;
            margin-top: 0.3rem;
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
        }

        /* Social Media Preview */
        .social-preview {
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            border: 2px solid rgba(130, 135, 122, 0.1);
        }

        .social-preview h4 {
            color: #121414;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .social-links-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .social-link-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            background: white;
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            font-weight: 500;
        }

        .social-link-item:hover {
            border-color: #82877a;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.2);
            color: #82877a;
        }

        .social-link-item i {
            font-size: 1.2rem;
        }

        /* Enhanced Tab Navigation */
        .nav-tabs {
            display: flex;
            gap: 0.5rem;
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
            padding-bottom: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .nav-tab {
            padding: 1rem 1.8rem;
            background: none;
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: #666;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.8rem;
            min-height: 44px;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(130, 135, 122, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-tab:hover::before {
            left: 100%;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
            color: white;
            border-color: #82877a;
            box-shadow: 0 4px 15px rgba(130, 135, 122, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover {
            background: rgba(130, 135, 122, 0.1);
            color: #82877a;
            border-color: rgba(130, 135, 122, 0.3);
            transform: translateY(-1px);
        }

        .nav-tab.active:hover {
            background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
            color: white;
            transform: translateY(-2px);
        }

        .nav-tab i {
            font-size: 1.1rem;
        }

        /* Real-time Preview */
        .preview-container {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 300px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(130, 135, 122, 0.2);
            z-index: 1000;
            display: none;
            max-height: 80vh;
            overflow-y: auto;
        }

        .preview-header {
            padding: 1rem;
            background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-content {
            padding: 1rem;
        }

        .preview-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .preview-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }









        /* Audit Table Styling */
        .audit-table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Cairo', sans-serif;
        }

        .audit-table thead {
            background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
            color: white;
        }

        .audit-table th {
            padding: 1.2rem 1rem;
            text-align: right;
            font-weight: 700;
            font-size: 0.95rem;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .audit-table tbody tr {
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .audit-table tbody tr:hover {
            background: rgba(130, 135, 122, 0.05);
        }

        .audit-table td {
            padding: 1rem;
            vertical-align: top;
        }

        .audit-action {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .audit-action.create {
            background: #28a745;
        }

        .audit-action.update {
            background: #17a2b8;
        }

        .audit-action.delete {
            background: #dc3545;
        }

        .audit-action.export {
            background: #6f42c1;
        }

        .audit-action.import {
            background: #fd7e14;
        }

        .audit-details {
            max-width: 200px;
            word-wrap: break-word;
            color: #666;
            font-size: 0.9rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .form-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-section h3 {
            color: #121414;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #82877a;
            font-size: 1.2rem;
        }

        .form-section h3 i {
            margin-left: 0.5rem;
            color: #82877a;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .feature-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
        }

        .feature-item h4 {
            color: #82877a;
            margin-bottom: 1rem;
            font-size: 1rem;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .form-row,
            .features-grid {
                grid-template-columns: 1fr;
            }

            .form-section {
                padding: 1rem;
            }
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.8rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(130, 135, 122, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .success-message,
        .error-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                min-width: auto;
                flex: 1;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Store Branding Toggle Styles */
        .toggle-group {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .toggle-option {
            flex: 1;
        }

        .toggle-option input[type="radio"] {
            display: none;
        }

        .toggle-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #666;
            font-weight: 500;
        }

        .toggle-label:hover {
            border-color: #007bff;
            background: #f0f8ff;
            color: #007bff;
        }

        .toggle-option input[type="radio"]:checked + .toggle-label {
            border-color: #007bff;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .logo-preview {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            text-align: center;
        }

        .logo-preview img {
            max-width: 150px;
            max-height: 60px;
            object-fit: contain;
        }

        @media (max-width: 768px) {
            .toggle-group {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Unified Navigation Section -->
        <div class="sidebar-section unified-navigation">
            <!-- Seamless Navigation List -->
            <nav class="sidebar-nav">
                <!-- Dashboard Navigation Links -->
                <a href="dashboard.html" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="orders.html" class="sidebar-link">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link active">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-cog"></i>
                        إعدادات الموقع
                    </h1>
                </div>

                <div class="top-bar-actions">
                    <!-- Retry Failed Saves Button -->
                    <button class="btn btn-outline-warning" onclick="retryFailedSaves()" title="إعادة محاولة حفظ الإعدادات المؤجلة" id="retryBtn" style="display: none;">
                        <i class="fas fa-redo"></i>
                        إعادة المحاولة
                    </button>

                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>


                </div>
            </div>
        </div>

        <!-- Messages -->
        <div class="success-message" id="successMessage"></div>
        <div class="error-message" id="errorMessage"></div>

        <!-- Enhanced Settings Navigation -->
        <div class="card settings-nav">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchTab('contact')" data-tab="contact">
                    <i class="fas fa-address-book"></i>
                    معلومات التواصل
                </button>
                <button class="nav-tab" onclick="switchTab('hours')" data-tab="hours">
                    <i class="fas fa-clock"></i>
                    أوقات العمل
                </button>
                <button class="nav-tab" onclick="switchTab('social')" data-tab="social">
                    <i class="fas fa-share-alt"></i>
                    وسائل التواصل
                </button>
                <button class="nav-tab" onclick="switchTab('about')" data-tab="about">
                    <i class="fas fa-info-circle"></i>
                    وصف الصفحات
                </button>
                <button class="nav-tab" onclick="switchTab('homepage')" data-tab="homepage">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </button>

            </div>


        </div>

        <!-- Settings Container -->
        <div class="settings-container">
            <!-- Contact Information Section -->
            <div class="settings-section card active" id="contact-section">
                <div class="card-header">
                    <i class="fas fa-address-book"></i>
                    <h2>معلومات التواصل</h2>
                </div>

                <form id="contactForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="businessName">اسم المتجر</label>
                            <input type="text" id="businessName" name="business_name" required>
                        </div>



                        <div class="form-group">
                            <label for="businessPhone">رقم الهاتف</label>
                            <input type="tel" id="businessPhone" name="business_phone" required>
                        </div>

                        <div class="form-group">
                            <label for="businessEmail">البريد الإلكتروني</label>
                            <input type="email" id="businessEmail" name="business_email" required>
                        </div>

                        <div class="form-group">
                            <label for="businessAddress">العنوان</label>
                            <textarea id="businessAddress" name="business_address" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="copyrightText">نص حقوق الطبع والنشر</label>
                            <input type="text" id="copyrightText" name="copyright_text" placeholder="جميع الحقوق محفوظة" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ معلومات التواصل
                    </button>
                </form>
            </div>

            <!-- Business Hours Section -->
            <div class="settings-section" id="hours-section">
                <div class="section-header">
                    <i class="fas fa-clock"></i>
                    <h2>أوقات العمل</h2>
                </div>

                <form id="hoursForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="workingDays">أيام العمل</label>
                            <input type="text" id="workingDays" name="working_days" placeholder="السبت - الخميس" required>
                        </div>

                        <div class="form-group">
                            <label for="workingHours">ساعات العمل</label>
                            <input type="text" id="workingHours" name="working_hours" placeholder="10 صباحاً - 5 مساءً" required>
                        </div>

                        <div class="form-group">
                            <label for="closedDay">يوم الإجازة</label>
                            <input type="text" id="closedDay" name="closed_day" placeholder="الجمعة" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ أوقات العمل
                    </button>
                </form>
            </div>

            <!-- Enhanced Social Media Section -->
            <div class="settings-section card" id="social-section">
                <div class="card-header">
                    <i class="fas fa-share-alt"></i>
                    <h2>وسائل التواصل الاجتماعي</h2>
                </div>

                <form id="socialForm" class="form-section">
                    <div class="form-grid">
                        <!-- WhatsApp -->
                        <div class="form-group">
                            <label for="whatsappNumber">
                                <i class="fab fa-whatsapp" style="color: #25D366; margin-left: 0.5rem;"></i>
                                رقم الواتساب
                            </label>
                            <input type="tel" id="whatsappNumber" name="whatsapp_number" placeholder="9647713688302" pattern="[0-9]{10,15}">
                            <small class="form-help">أدخل الرقم مع رمز الدولة (مثال: 9647713688302)</small>
                        </div>

                        <!-- Facebook -->
                        <div class="form-group">
                            <label for="facebookUrl">
                                <i class="fab fa-facebook" style="color: #1877F2; margin-left: 0.5rem;"></i>
                                رابط الفيسبوك
                            </label>
                            <input type="url" id="facebookUrl" name="facebook_url" placeholder="https://facebook.com/yourpage">
                        </div>

                        <!-- Instagram -->
                        <div class="form-group">
                            <label for="instagramUrl">
                                <i class="fab fa-instagram" style="color: #E4405F; margin-left: 0.5rem;"></i>
                                رابط الإنستغرام
                            </label>
                            <input type="url" id="instagramUrl" name="instagram_url" placeholder="https://instagram.com/yourpage">
                        </div>

                        <!-- Twitter -->
                        <div class="form-group">
                            <label for="twitterUrl">
                                <i class="fab fa-twitter" style="color: #1DA1F2; margin-left: 0.5rem;"></i>
                                رابط تويتر
                            </label>
                            <input type="url" id="twitterUrl" name="twitter_url" placeholder="https://twitter.com/yourpage">
                        </div>

                        <!-- Telegram -->
                        <div class="form-group">
                            <label for="telegramUrl">
                                <i class="fab fa-telegram" style="color: #0088CC; margin-left: 0.5rem;"></i>
                                رابط التليغرام
                            </label>
                            <input type="url" id="telegramUrl" name="telegram_url" placeholder="https://t.me/yourpage">
                        </div>

                        <!-- LinkedIn -->
                        <div class="form-group">
                            <label for="linkedinUrl">
                                <i class="fab fa-linkedin" style="color: #0A66C2; margin-left: 0.5rem;"></i>
                                رابط لينكد إن
                            </label>
                            <input type="url" id="linkedinUrl" name="linkedin_url" placeholder="https://linkedin.com/company/yourpage">
                        </div>

                        <!-- TikTok -->
                        <div class="form-group">
                            <label for="tiktokUrl">
                                <i class="fab fa-tiktok" style="color: #000000; margin-left: 0.5rem;"></i>
                                رابط تيك توك
                            </label>
                            <input type="url" id="tiktokUrl" name="tiktok_url" placeholder="https://tiktok.com/@yourpage">
                        </div>

                        <!-- YouTube -->
                        <div class="form-group">
                            <label for="youtubeUrl">
                                <i class="fab fa-youtube" style="color: #FF0000; margin-left: 0.5rem;"></i>
                                رابط يوتيوب
                            </label>
                            <input type="url" id="youtubeUrl" name="youtube_url" placeholder="https://youtube.com/c/yourpage">
                        </div>

                        <!-- Snapchat -->
                        <div class="form-group">
                            <label for="snapchatUrl">
                                <i class="fab fa-snapchat" style="color: #FFFC00; margin-left: 0.5rem;"></i>
                                رابط سناب شات
                            </label>
                            <input type="url" id="snapchatUrl" name="snapchat_url" placeholder="https://snapchat.com/add/yourpage">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ روابط التواصل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="previewSocialLinks()">
                            <i class="fas fa-eye"></i>
                            معاينة الروابط
                        </button>

                    </div>
                </form>

                <!-- Social Media Preview -->
                <div class="social-preview" id="socialPreview" style="display: none;">
                    <h4><i class="fas fa-eye"></i> معاينة روابط التواصل الاجتماعي</h4>
                    <div class="social-links-preview" id="socialLinksPreview">
                        <!-- Preview will be generated here -->
                    </div>
                </div>
            </div>

            <!-- About Store Section -->
            <div class="settings-section" id="about-section">
                <div class="section-header">
                    <i class="fas fa-info-circle"></i>
                    <h2>نبذة عن المتجر</h2>
                </div>













                <!-- Page Descriptions -->
                <div class="form-section">
                    <h3><i class="fas fa-file-alt"></i> نصوص وصف الصفحات</h3>
                    <form id="pageDescriptionsForm">
                        <div class="form-group">
                            <label for="productsPageDesc">وصف صفحة المنتجات</label>
                            <input type="text" id="productsPageDesc" name="products_page_description" placeholder="اكتشف مجموعتنا الكاملة من منتجات العناية بالبشرة والشعر" required>
                        </div>

                        <div class="form-group">
                            <label for="offersPageDesc">وصف صفحة العروض</label>
                            <input type="text" id="offersPageDesc" name="offers_page_description" placeholder="اكتشف أفضل العروض على منتجات العناية بالبشرة والشعر" required>
                        </div>

                        <div class="form-group">
                            <label for="contactPageDesc">وصف صفحة اتصل بنا</label>
                            <input type="text" id="contactPageDesc" name="contact_page_description" placeholder="نحن هنا لمساعدتك والإجابة على جميع استفساراتك" required>
                        </div>

                        <div class="form-group">
                            <label for="faqPageDesc">وصف صفحة الأسئلة الشائعة</label>
                            <input type="text" id="faqPageDesc" name="faq_page_description" placeholder="إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا" required>
                        </div>



                        <div class="form-group">
                            <label for="guidelinesPageDesc">وصف صفحة الإرشادات</label>
                            <input type="text" id="guidelinesPageDesc" name="guidelines_page_description" placeholder="تعلم كيفية استخدام منتجات العناية بالبشرة والشعر بالطريقة الصحيحة" required>
                        </div>

                        <div class="form-group">
                            <label for="termsPageDesc">وصف صفحة الشروط والأحكام</label>
                            <input type="text" id="termsPageDesc" name="terms_page_description" placeholder="يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا" required>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ نصوص الصفحات
                        </button>
                    </form>
                </div>

                <!-- Page Header Backgrounds Management -->
                <div class="form-section">
                    <h3><i class="fas fa-image"></i> خلفيات رؤوس الصفحات</h3>
                    <p class="section-description">إدارة صور الخلفية لرؤوس الصفحات - يتم تحميل الصور عبر روابط URL</p>



                    <!-- Products Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-box"></i> صفحة المنتجات</h4>
                        <form id="productsHeaderBgForm">
                            <div class="form-group">
                                <label for="productsHeaderBgEnabled">تفعيل خلفية رأس صفحة المنتجات</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="productsHeaderBgEnabled" name="products_header_bg_enabled" data-setting="products_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="productsHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="productsHeaderBgUrl" name="products_header_bg_url" data-setting="products_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية المنتجات
                            </button>
                        </form>
                    </div>

                    <!-- Contact Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-phone"></i> صفحة اتصل بنا</h4>
                        <form id="contactHeaderBgForm">
                            <div class="form-group">
                                <label for="contactHeaderBgEnabled">تفعيل خلفية رأس صفحة اتصل بنا</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="contactHeaderBgEnabled" name="contact_header_bg_enabled" data-setting="contact_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contactHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="contactHeaderBgUrl" name="contact_header_bg_url" data-setting="contact_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية اتصل بنا
                            </button>
                        </form>
                    </div>

                    <!-- FAQ Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-question-circle"></i> صفحة الأسئلة الشائعة</h4>
                        <form id="faqHeaderBgForm">
                            <div class="form-group">
                                <label for="faqHeaderBgEnabled">تفعيل خلفية رأس صفحة الأسئلة الشائعة</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="faqHeaderBgEnabled" name="faq_header_bg_enabled" data-setting="faq_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="faqHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="faqHeaderBgUrl" name="faq_header_bg_url" data-setting="faq_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية الأسئلة الشائعة
                            </button>
                        </form>
                    </div>

                    <!-- Guidelines Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-book"></i> صفحة الإرشادات</h4>
                        <form id="guidelinesHeaderBgForm">
                            <div class="form-group">
                                <label for="guidelinesHeaderBgEnabled">تفعيل خلفية رأس صفحة الإرشادات</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="guidelinesHeaderBgEnabled" name="guidelines_header_bg_enabled" data-setting="guidelines_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="guidelinesHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="guidelinesHeaderBgUrl" name="guidelines_header_bg_url" data-setting="guidelines_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية الإرشادات
                            </button>
                        </form>
                    </div>

                    <!-- Terms Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-file-contract"></i> صفحة الشروط والأحكام</h4>
                        <form id="termsHeaderBgForm">
                            <div class="form-group">
                                <label for="termsHeaderBgEnabled">تفعيل خلفية رأس صفحة الشروط والأحكام</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="termsHeaderBgEnabled" name="terms_header_bg_enabled" data-setting="terms_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="termsHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="termsHeaderBgUrl" name="terms_header_bg_url" data-setting="terms_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية الشروط والأحكام
                            </button>
                        </form>
                    </div>

                    <!-- Offers Page Header Background -->
                    <div class="form-subsection">
                        <h4><i class="fas fa-tags"></i> صفحة العروض</h4>
                        <form id="offersHeaderBgForm">
                            <div class="form-group">
                                <label for="offersHeaderBgEnabled">تفعيل خلفية رأس صفحة العروض</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="offersHeaderBgEnabled" name="offers_header_bg_enabled" data-setting="offers_header_bg_enabled">
                                    <div class="toggle-slider"></div>
                                    <span class="toggle-label">تفعيل الخلفية</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="offersHeaderBgUrl">رابط صورة الخلفية</label>
                                <input type="url" id="offersHeaderBgUrl" name="offers_header_bg_url" data-setting="offers_header_bg_url" placeholder="https://example.com/image.jpg">
                                <small class="form-help">أدخل رابط صورة عالية الجودة (يُفضل 1920x600 بكسل أو أكبر)</small>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> حفظ خلفية العروض
                            </button>
                        </form>
                    </div>
                </div>

            </div>

            <!-- Terms and Conditions Management Section -->
            <div class="settings-section" id="terms-section">
                <div class="section-header">
                    <i class="fas fa-file-contract"></i>
                    <h2>إدارة الشروط والأحكام</h2>
                </div>

                <!-- Terms Management Controls -->
                <div class="form-section">
                    <h3><i class="fas fa-cogs"></i> إعدادات عامة</h3>
                    <form id="termsGeneralForm">
                        <div class="form-group">
                            <label for="termsPageEnabled">تفعيل صفحة الشروط والأحكام</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="termsPageEnabled" name="terms_page_enabled" data-setting="terms_page_enabled">
                                <label for="termsPageEnabled" class="toggle-label">
                                    <span class="toggle-inner"></span>
                                    <span class="toggle-switch-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="termsPageTitle">عنوان الصفحة</label>
                            <input type="text" id="termsPageTitle" name="terms_page_title" data-setting="terms_page_title" placeholder="الشروط والأحكام" required>
                        </div>

                        <div class="form-group">
                            <label for="termsPageDescription">وصف الصفحة</label>
                            <textarea id="termsPageDescription" name="terms_page_description" data-setting="terms_page_description" rows="3" placeholder="يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا" required></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات العامة
                        </button>
                    </form>
                </div>

                <!-- Terms Sections Management -->
                <div class="form-section">
                    <h3><i class="fas fa-list"></i> إدارة أقسام الشروط والأحكام</h3>

                    <div class="terms-sections-container">
                        <div id="termsSectionsList">
                            <!-- Terms sections will be loaded here -->
                        </div>

                        <button type="button" class="btn btn-success" onclick="addNewTermsSection()">
                            <i class="fas fa-plus"></i>
                            إضافة قسم جديد
                        </button>
                    </div>
                </div>

                <!-- Terms Section Template (Hidden) -->
                <div id="termsSectionTemplate" style="display: none;">
                    <div class="terms-section-item">
                        <div class="section-header-controls">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>عنوان القسم</label>
                                    <input type="text" class="section-title" placeholder="عنوان القسم" required>
                                </div>
                                <div class="form-group">
                                    <label>نوع القسم</label>
                                    <select class="section-type">
                                        <option value="الشروط العامة">الشروط العامة</option>
                                        <option value="الطلبات والدفع">الطلبات والدفع</option>
                                        <option value="التوصيل">التوصيل</option>
                                        <option value="الإرجاع والاستبدال">الإرجاع والاستبدال</option>
                                        <option value="سياسة الخصوصية">سياسة الخصوصية</option>
                                        <option value="معلومات التواصل">معلومات التواصل</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>ترتيب العرض</label>
                                    <input type="number" class="section-order" min="0" value="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>تفعيل القسم</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" class="section-enabled" checked>
                                    <label class="toggle-label">
                                        <span class="toggle-inner"></span>
                                        <span class="toggle-switch-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>محتوى القسم</label>
                            <textarea class="section-content" rows="8" placeholder="اكتب محتوى القسم هنا..." required></textarea>
                        </div>

                        <div class="section-actions">
                            <button type="button" class="btn btn-primary" onclick="saveTermsSection(this)">
                                <i class="fas fa-save"></i>
                                حفظ القسم
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteTermsSection(this)">
                                <i class="fas fa-trash"></i>
                                حذف القسم
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Homepage Settings Section -->
            <div class="settings-section" id="homepage-section">
                <div class="section-header">
                    <i class="fas fa-home"></i>
                    <h2>إعدادات الصفحة الرئيسية</h2>
                </div>

                <!-- Hero Section Settings -->
                <div class="form-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h2><i class="fas fa-star"></i> البانر الرئيسي (Hero Section)</h2>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <form id="heroSectionForm">
                        <div class="form-group">
                            <label for="heroTitleLine1">السطر الأول من العنوان</label>
                            <input type="text" id="heroTitleLine1" name="hero_title_line1" placeholder="اكتشف عالم الجمال الحقيقي" required>
                        </div>
                        <div class="form-group">
                            <label for="heroTitleLine2">السطر الثاني من العنوان</label>
                            <input type="text" id="heroTitleLine2" name="hero_title_line2" placeholder="مع متجر Care" required>
                        </div>
                        <div class="form-group">
                            <label for="heroSubtitle">النص التوضيحي</label>
                            <textarea id="heroSubtitle" name="hero_subtitle" rows="3" placeholder="منتجات عناية فاخرة من أفضل العلامات التجارية العالمية - جودة استثنائية وخدمة لا مثيل لها في جميع أنحاء العراق" required></textarea>
                        </div>

                        <!-- Trust Indicators -->
                        <h4>مؤشرات الثقة</h4>

                        <!-- Trust Indicators Enable/Disable Toggle -->
                        <div class="form-group">
                            <div class="toggle-container">
                                <input type="checkbox" id="trustIndicatorsEnabled" name="trust_indicators_enabled" value="1" class="toggle-input">
                                <div class="toggle-slider"></div>
                                <label for="trustIndicatorsEnabled" class="toggle-label">تفعيل مؤشرات الثقة</label>
                            </div>
                            <small class="form-help">قم بتفعيل هذا الخيار لإظهار مؤشرات الثقة في البانر الرئيسي</small>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="trustIndicator1">المؤشر الأول</label>
                                <input type="text" id="trustIndicator1" name="trust_indicator_1" placeholder="منتجات أصلية 100%" required>
                            </div>
                            <div class="form-group">
                                <label for="trustIndicator1Icon">الأيقونة (Font Awesome)</label>
                                <input type="text" id="trustIndicator1Icon" name="trust_indicator_1_icon" placeholder="fas fa-certificate" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="trustIndicator2">المؤشر الثاني</label>
                                <input type="text" id="trustIndicator2" name="trust_indicator_2" placeholder="توصيل مجاني للطلبات +50 ألف" required>
                            </div>
                            <div class="form-group">
                                <label for="trustIndicator2Icon">الأيقونة (Font Awesome)</label>
                                <input type="text" id="trustIndicator2Icon" name="trust_indicator_2_icon" placeholder="fas fa-shipping-fast" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="trustIndicator3">المؤشر الثالث</label>
                                <input type="text" id="trustIndicator3" name="trust_indicator_3" placeholder="ضمان الاسترداد 30 يوم" required>
                            </div>
                            <div class="form-group">
                                <label for="trustIndicator3Icon">الأيقونة (Font Awesome)</label>
                                <input type="text" id="trustIndicator3Icon" name="trust_indicator_3_icon" placeholder="fas fa-undo-alt" required>
                            </div>
                        </div>

                        <!-- Featured Product in Hero -->
                        <h4>المنتج المميز في البانر</h4>

                        <!-- Featured Product Enable/Disable Toggle -->
                        <div class="form-group">
                            <div class="toggle-container">
                                <input type="checkbox" id="featuredProductEnabled" name="featured_product_enabled" value="1" class="toggle-input">
                                <div class="toggle-slider"></div>
                                <label for="featuredProductEnabled" class="toggle-label">تفعيل المنتج المميز في البانر</label>
                            </div>
                            <small class="form-help">قم بتفعيل هذا الخيار لإظهار المنتج المميز في البانر الرئيسي</small>
                        </div>

                        <div class="form-group">
                            <label for="featuredProductName">اسم المنتج المميز</label>
                            <input type="text" id="featuredProductName" name="featured_product_name" placeholder="سيروم فيتامين سي المتقدم" required>
                        </div>
                        <div class="form-group">
                            <label for="featuredProductPrice">السعر الحالي</label>
                            <input type="text" id="featuredProductPrice" name="featured_product_price" placeholder="45,000 د.ع" required>
                        </div>
                        <div class="form-group">
                            <label for="featuredProductOriginalPrice">السعر الأصلي</label>
                            <input type="text" id="featuredProductOriginalPrice" name="featured_product_original_price" placeholder="60,000 د.ع">
                        </div>

                        <!-- Hero Background Image Settings -->
                        <h4>إعدادات خلفية البانر</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="heroBackgroundEnabled" name="hero_background_enabled" value="1">
                                تفعيل صورة خلفية مخصصة
                            </label>
                            <small class="form-help">إذا لم يتم التفعيل، سيتم استخدام الخلفية الافتراضية</small>
                        </div>
                        <div class="form-group" id="heroBackgroundUrlGroup" style="display: none;">
                            <label for="heroBackgroundUrl">رابط صورة الخلفية</label>
                            <input type="url" id="heroBackgroundUrl" name="hero_background_url" placeholder="https://example.com/hero-background.jpg">
                            <small class="form-help">أدخل رابط صورة عالية الجودة (يفضل 1920x1080 أو أكبر)</small>
                            <div class="image-preview" id="heroBackgroundPreview" style="display: none; margin-top: 10px;">
                                <img id="heroBackgroundPreviewImg" src="" alt="معاينة خلفية البانر" style="max-width: 300px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>

                        <!-- Hero Showcase Image Settings -->
                        <h4>إعدادات صورة المنتج المميز</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="heroShowcaseEnabled" name="hero_showcase_enabled" value="1">
                                تفعيل صورة مخصصة للمنتج المميز
                            </label>
                            <small class="form-help">إذا لم يتم التفعيل، سيتم استخدام الصورة الافتراضية</small>
                        </div>
                        <div class="form-group" id="heroShowcaseUrlGroup" style="display: none;">
                            <label for="heroShowcaseUrl">رابط صورة المنتج المميز</label>
                            <input type="url" id="heroShowcaseUrl" name="hero_showcase_url" placeholder="https://example.com/product-image.jpg">
                            <small class="form-help">أدخل رابط صورة المنتج (يفضل 300x300 أو مربعة)</small>
                            <div class="image-preview" id="heroShowcasePreview" style="display: none; margin-top: 10px;">
                                <img id="heroShowcasePreviewImg" src="" alt="معاينة صورة المنتج المميز" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ إعدادات البانر الرئيسي
                        </button>
                    </form>
                    </div>
                </div>

                <!-- Featured Products Section Settings -->
                <div class="form-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h2><i class="fas fa-star"></i> قسم المنتجات المميزة</h2>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <form id="featuredProductsSectionForm">
                        <div class="form-group">
                            <label for="featuredProductsBadge">شارة القسم</label>
                            <input type="text" id="featuredProductsBadge" name="featured_products_badge" placeholder="منتجات مختارة بعناية" required>
                        </div>
                        <div class="form-group">
                            <label for="featuredProductsTitle">عنوان القسم</label>
                            <input type="text" id="featuredProductsTitle" name="featured_products_title" placeholder="المنتجات المميزة" required>
                        </div>
                        <div class="form-group">
                            <label for="featuredProductsSubtitle">وصف القسم</label>
                            <textarea id="featuredProductsSubtitle" name="featured_products_subtitle" rows="3" placeholder="اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر من العلامات التجارية الموثوقة" required></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ إعدادات المنتجات المميزة
                        </button>
                    </form>
                    </div>
                </div>

                <!-- Features Section Settings -->
                <div class="form-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h2><i class="fas fa-gem"></i> قسم الميزات والخدمات</h2>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <form id="featuresSectionForm">
                        <div class="form-group">
                            <label for="featuresBadge">شارة القسم</label>
                            <input type="text" id="featuresBadge" name="features_badge" placeholder="خدماتنا المميزة" required>
                        </div>
                        <div class="form-group">
                            <label for="featuresTitle">عنوان القسم</label>
                            <input type="text" id="featuresTitle" name="features_title" placeholder="لماذا تختار متجر Care؟" required>
                        </div>
                        <div class="form-group">
                            <label for="featuresSubtitle">وصف القسم</label>
                            <textarea id="featuresSubtitle" name="features_subtitle" rows="3" placeholder="نقدم لك تجربة تسوق استثنائية مع خدمات متطورة وضمانات شاملة تضمن رضاك التام" required></textarea>
                        </div>

                        <!-- Individual Features -->
                        <div class="features-grid">
                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة الأولى</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature1Enabled" name="feature_1_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature1Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature1Title">العنوان</label>
                                    <input type="text" id="feature1Title" name="feature_1_title" placeholder="منتجات أصلية مضمونة" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature1Description">الوصف</label>
                                    <textarea id="feature1Description" name="feature_1_description" rows="2" placeholder="جميع منتجاتنا أصلية 100% مع ضمان الجودة والأصالة من المصدر المباشر" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature1Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature1Icon" name="feature_1_icon" placeholder="fas fa-certificate" required>
                                </div>
                            </div>

                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة الثانية</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature2Enabled" name="feature_2_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature2Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature2Title">العنوان</label>
                                    <input type="text" id="feature2Title" name="feature_2_title" placeholder="توصيل سريع ومجاني" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature2Description">الوصف</label>
                                    <textarea id="feature2Description" name="feature_2_description" rows="2" placeholder="توصيل مجاني لجميع أنحاء العراق للطلبات أكثر من 50 ألف دينار خلال 24-48 ساعة" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature2Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature2Icon" name="feature_2_icon" placeholder="fas fa-shipping-fast" required>
                                </div>
                            </div>

                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة الثالثة</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature3Enabled" name="feature_3_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature3Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature3Title">العنوان</label>
                                    <input type="text" id="feature3Title" name="feature_3_title" placeholder="دعم عملاء متميز" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature3Description">الوصف</label>
                                    <textarea id="feature3Description" name="feature_3_description" rows="2" placeholder="فريق دعم متخصص ومدرب لمساعدتك في اختيار المنتجات المناسبة والإجابة على استفساراتك" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature3Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature3Icon" name="feature_3_icon" placeholder="fas fa-headset" required>
                                </div>
                            </div>

                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة الرابعة</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature4Enabled" name="feature_4_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature4Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature4Title">العنوان</label>
                                    <input type="text" id="feature4Title" name="feature_4_title" placeholder="ضمان الجودة والأمان" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature4Description">الوصف</label>
                                    <textarea id="feature4Description" name="feature_4_description" rows="2" placeholder="نظام دفع آمن ومشفر مع ضمان حماية كاملة لبياناتك الشخصية والمالية" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature4Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature4Icon" name="feature_4_icon" placeholder="fas fa-shield-check" required>
                                </div>
                            </div>

                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة الخامسة</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature5Enabled" name="feature_5_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature5Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature5Title">العنوان</label>
                                    <input type="text" id="feature5Title" name="feature_5_title" placeholder="ضمان الاسترداد" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature5Description">الوصف</label>
                                    <textarea id="feature5Description" name="feature_5_description" rows="2" placeholder="إمكانية إرجاع المنتج خلال 30 يوم مع ضمان استرداد كامل للمبلغ دون أي شروط معقدة" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature5Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature5Icon" name="feature_5_icon" placeholder="fas fa-undo-alt" required>
                                </div>
                            </div>

                            <div class="feature-item">
                                <div class="feature-header">
                                    <h4>الميزة السادسة</h4>
                                    <div class="toggle-container">
                                        <input type="checkbox" id="feature6Enabled" name="feature_6_enabled" value="1" class="toggle-input">
                                        <div class="toggle-slider"></div>
                                        <label for="feature6Enabled" class="toggle-label">تفعيل</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feature6Title">العنوان</label>
                                    <input type="text" id="feature6Title" name="feature_6_title" placeholder="استشارة مجانية" required>
                                </div>
                                <div class="form-group">
                                    <label for="feature6Description">الوصف</label>
                                    <textarea id="feature6Description" name="feature_6_description" rows="2" placeholder="استشارة مجانية من خبراء العناية المعتمدين لاختيار المنتجات الأنسب لنوع بشرتك وشعرك" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="feature6Icon">الأيقونة (Font Awesome)</label>
                                    <input type="text" id="feature6Icon" name="feature_6_icon" placeholder="fas fa-user-md" required>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ إعدادات الميزات والخدمات
                        </button>
                    </form>
                    </div>
                </div>

                <!-- About Section Settings -->
                <div class="form-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h2><i class="fas fa-heart"></i> قسم "من نحن"</h2>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                    <form id="aboutSectionForm">
                        <div class="form-group">
                            <label for="aboutBadge">شارة القسم</label>
                            <input type="text" id="aboutBadge" name="about_badge" placeholder="قصتنا" required>
                        </div>
                        <div class="form-group">
                            <label for="aboutTitle">عنوان القسم</label>
                            <input type="text" id="aboutTitle" name="about_title" placeholder="من نحن" required>
                        </div>
                        <div class="form-group">
                            <label for="aboutSubtitle">وصف القسم</label>
                            <textarea id="aboutSubtitle" name="about_subtitle" rows="3" placeholder="رحلتنا في عالم الجمال والعناية بدأت من شغف حقيقي بتقديم الأفضل لعملائنا الكرام في جميع أنحاء العراق" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="aboutDescription1">الفقرة الأولى</label>
                            <textarea id="aboutDescription1" name="about_description_1" rows="4" placeholder="منذ تأسيسنا عام 2018، نسعى لتقديم أفضل منتجات العناية بالبشرة والشعر من أرقى العلامات التجارية العالمية. نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة والمنتجات عالية الجودة." required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="aboutDescription2">الفقرة الثانية</label>
                            <textarea id="aboutDescription2" name="about_description_2" rows="4" placeholder="فريقنا المتخصص يعمل بجد لاختيار كل منتج بعناية فائقة، مع التأكد من أصالته وجودته العالية. نحن لسنا مجرد متجر، بل شريكك الموثوق في رحلة العناية والجمال." required></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ إعدادات قسم "من نحن"
                        </button>
                    </form>
                </div>

                <!-- Statistics Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-chart-bar"></i> إحصائيات الصفحة الرئيسية</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <form id="statsSectionForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="statCustomers">عدد العملاء</label>
                                <input type="text" id="statCustomers" name="stat_customers" placeholder="12,500+" required>
                            </div>
                            <div class="form-group">
                                <label for="statOrders">عدد الطلبات</label>
                                <input type="text" id="statOrders" name="stat_orders" placeholder="25,000+" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="statYears">سنوات الخبرة</label>
                                <input type="text" id="statYears" name="stat_years" placeholder="7+" required>
                            </div>
                            <div class="form-group">
                                <label for="statRating">التقييم المتوسط</label>
                                <input type="text" id="statRating" name="stat_rating" placeholder="4.9" required>
                            </div>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الإحصائيات
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Testimonials Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-comments"></i> قسم آراء العملاء</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <!-- Section Enable/Disable -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="testimonialsEnabled" name="testimonials_enabled" value="1">
                            تفعيل قسم آراء العملاء
                        </label>
                        <small class="form-help">قم بتفعيل هذا الخيار لإظهار قسم آراء العملاء في الصفحة الرئيسية</small>
                    </div>

                    <form id="testimonialsSectionForm">
                        <!-- Section Header -->
                        <h4>إعدادات عامة للقسم</h4>
                        <div class="form-group">
                            <label for="testimonialsBadge">شارة القسم</label>
                            <input type="text" id="testimonialsBadge" name="testimonials_badge" placeholder="آراء عملائنا" required>
                        </div>
                        <div class="form-group">
                            <label for="testimonialsTitle">عنوان القسم</label>
                            <input type="text" id="testimonialsTitle" name="testimonials_title" placeholder="ماذا يقول عملاؤنا عنا؟" required>
                        </div>
                        <div class="form-group">
                            <label for="testimonialsSubtitle">وصف القسم</label>
                            <textarea id="testimonialsSubtitle" name="testimonials_subtitle" rows="2" placeholder="تجارب حقيقية من عملائنا الكرام الذين جربوا منتجاتنا وخدماتنا المتميزة" required></textarea>
                        </div>

                        <!-- Dynamic Testimonials -->
                        <div class="testimonials-header">
                            <h4>آراء العملاء</h4>
                        </div>

                        <div id="testimonialsContainer">
                            <!-- Testimonials will be dynamically added here -->
                        </div>

                        <!-- Testimonials Statistics -->
                        <div class="statistics-section-header">
                            <h4>إحصائيات آراء العملاء</h4>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="testimonialsStatsEnabled" name="testimonials_stats_enabled" value="1">
                                    تفعيل إحصائيات آراء العملاء
                                </label>
                                <small class="form-help">قم بتفعيل هذا الخيار لإظهار إحصائيات آراء العملاء في الصفحة الرئيسية</small>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="testimonialsTotalReviews">إجمالي التقييمات</label>
                                <input type="text" id="testimonialsTotalReviews" name="testimonials_total_reviews" placeholder="2,500+" required>
                            </div>
                            <div class="form-group">
                                <label for="testimonialsAverageRating">متوسط التقييم</label>
                                <input type="text" id="testimonialsAverageRating" name="testimonials_average_rating" placeholder="4.9" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="testimonialsSatisfaction">نسبة الرضا</label>
                            <input type="text" id="testimonialsSatisfaction" name="testimonials_satisfaction" placeholder="98%" required>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ إعدادات آراء العملاء
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Newsletter Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-envelope"></i> قسم النشرة الإخبارية</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <!-- Section Enable/Disable -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="newsletterEnabled" name="newsletter_enabled" value="1">
                            تفعيل قسم النشرة الإخبارية
                        </label>
                        <small class="form-help">قم بتفعيل هذا الخيار لإظهار قسم النشرة الإخبارية في الصفحة الرئيسية</small>
                    </div>

                    <form id="newsletterSectionForm">
                        <!-- Section Content -->
                        <h4>محتوى القسم</h4>
                        <div class="form-group">
                            <label for="newsletterTitle">عنوان القسم</label>
                            <input type="text" id="newsletterTitle" name="newsletter_title" placeholder="اشترك في نشرتنا الإخبارية" required>
                        </div>
                        <div class="form-group">
                            <label for="newsletterSubtitle">وصف القسم</label>
                            <textarea id="newsletterSubtitle" name="newsletter_subtitle" rows="2" placeholder="احصل على أحدث العروض والنصائح للعناية بالبشرة والشعر مباشرة في بريدك الإلكتروني" required></textarea>
                        </div>

                        <!-- Benefits -->
                        <h4>مميزات الاشتراك</h4>
                        <div class="form-group">
                            <label for="newsletterBenefit1">الميزة الأولى</label>
                            <input type="text" id="newsletterBenefit1" name="newsletter_benefit_1" placeholder="عروض حصرية للمشتركين" required>
                        </div>
                        <div class="form-group">
                            <label for="newsletterBenefit2">الميزة الثانية</label>
                            <input type="text" id="newsletterBenefit2" name="newsletter_benefit_2" placeholder="نصائح جمالية أسبوعية" required>
                        </div>
                        <div class="form-group">
                            <label for="newsletterBenefit3">الميزة الثالثة</label>
                            <input type="text" id="newsletterBenefit3" name="newsletter_benefit_3" placeholder="أول من يعرف المنتجات الجديدة" required>
                        </div>

                        <!-- Privacy Text -->
                        <div class="form-group">
                            <label for="newsletterPrivacyText">نص الخصوصية</label>
                            <textarea id="newsletterPrivacyText" name="newsletter_privacy_text" rows="2" placeholder="نحن نحترم خصوصيتك ولن نشارك بريدك الإلكتروني مع أي طرف ثالث" required></textarea>
                        </div>

                        <!-- Statistics -->
                        <h4>إحصائيات النشرة الإخبارية</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="newsletterSubscribersCount">عدد المشتركين</label>
                                <input type="text" id="newsletterSubscribersCount" name="newsletter_subscribers_count" placeholder="5,000+" required>
                            </div>
                            <div class="form-group">
                                <label for="newsletterSatisfactionRate">نسبة الرضا</label>
                                <input type="text" id="newsletterSatisfactionRate" name="newsletter_satisfaction_rate" placeholder="95%" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newsletterWeeklyTips">النصائح الأسبوعية</label>
                            <input type="text" id="newsletterWeeklyTips" name="newsletter_weekly_tips" placeholder="3" required>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ إعدادات النشرة الإخبارية
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Timeline Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-history"></i> قسم الخط الزمني</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <!-- Section Enable/Disable -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="timelineEnabled" name="timeline_enabled" value="1">
                            تفعيل قسم الخط الزمني
                        </label>
                        <small class="form-help">قم بتفعيل هذا الخيار لإظهار قسم الخط الزمني في الصفحة الرئيسية</small>
                    </div>

                    <form id="timelineSectionForm">
                        <!-- Section Header -->
                        <h4>إعدادات عامة للقسم</h4>
                        <div class="form-group">
                            <label for="timelineBadge">شارة القسم</label>
                            <input type="text" id="timelineBadge" name="timeline_badge" placeholder="رحلتنا" required>
                        </div>
                        <div class="form-group">
                            <label for="timelineTitle">عنوان القسم</label>
                            <input type="text" id="timelineTitle" name="timeline_title" placeholder="قصة نجاح متجر Care" required>
                        </div>
                        <div class="form-group">
                            <label for="timelineSubtitle">وصف القسم</label>
                            <textarea id="timelineSubtitle" name="timeline_subtitle" rows="2" placeholder="رحلة من الشغف إلى التميز في عالم الجمال والعناية" required></textarea>
                        </div>

                        <!-- Timeline Items -->
                        <h4>عناصر الخط الزمني</h4>

                        <!-- Timeline Item 1 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر الأول</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline1Enabled" name="timeline_1_enabled" value="1">
                                        تفعيل العنصر الأول
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline1Year">السنة</label>
                                    <input type="text" id="timeline1Year" name="timeline_1_year" placeholder="2018" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline1Icon">الأيقونة</label>
                                    <input type="text" id="timeline1Icon" name="timeline_1_icon" placeholder="fas fa-lightbulb" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline1Title">العنوان</label>
                                <input type="text" id="timeline1Title" name="timeline_1_title" placeholder="بداية الحلم" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline1Description">الوصف</label>
                                <textarea id="timeline1Description" name="timeline_1_description" rows="3" placeholder="انطلقت فكرة متجر Care من شغف حقيقي..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Item 2 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر الثاني</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline2Enabled" name="timeline_2_enabled" value="1">
                                        تفعيل العنصر الثاني
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline2Year">السنة</label>
                                    <input type="text" id="timeline2Year" name="timeline_2_year" placeholder="2019" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline2Icon">الأيقونة</label>
                                    <input type="text" id="timeline2Icon" name="timeline_2_icon" placeholder="fas fa-store" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline2Title">العنوان</label>
                                <input type="text" id="timeline2Title" name="timeline_2_title" placeholder="افتتاح المتجر" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline2Description">الوصف</label>
                                <textarea id="timeline2Description" name="timeline_2_description" rows="3" placeholder="افتتحنا أول متجر لنا في بغداد..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Item 3 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر الثالث</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline3Enabled" name="timeline_3_enabled" value="1">
                                        تفعيل العنصر الثالث
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline3Year">السنة</label>
                                    <input type="text" id="timeline3Year" name="timeline_3_year" placeholder="2020" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline3Icon">الأيقونة</label>
                                    <input type="text" id="timeline3Icon" name="timeline_3_icon" placeholder="fas fa-users" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline3Title">العنوان</label>
                                <input type="text" id="timeline3Title" name="timeline_3_title" placeholder="نمو قاعدة العملاء" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline3Description">الوصف</label>
                                <textarea id="timeline3Description" name="timeline_3_description" rows="3" placeholder="وصلنا إلى أكثر من 1000 عميل راضٍ..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Item 4 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر الرابع</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline4Enabled" name="timeline_4_enabled" value="1">
                                        تفعيل العنصر الرابع
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline4Year">السنة</label>
                                    <input type="text" id="timeline4Year" name="timeline_4_year" placeholder="2021" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline4Icon">الأيقونة</label>
                                    <input type="text" id="timeline4Icon" name="timeline_4_icon" placeholder="fas fa-globe" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline4Title">العنوان</label>
                                <input type="text" id="timeline4Title" name="timeline_4_title" placeholder="التوسع الرقمي" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline4Description">الوصف</label>
                                <textarea id="timeline4Description" name="timeline_4_description" rows="3" placeholder="أطلقنا متجرنا الإلكتروني..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Item 5 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر الخامس</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline5Enabled" name="timeline_5_enabled" value="1">
                                        تفعيل العنصر الخامس
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline5Year">السنة</label>
                                    <input type="text" id="timeline5Year" name="timeline_5_year" placeholder="2023" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline5Icon">الأيقونة</label>
                                    <input type="text" id="timeline5Icon" name="timeline_5_icon" placeholder="fas fa-award" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline5Title">العنوان</label>
                                <input type="text" id="timeline5Title" name="timeline_5_title" placeholder="التميز والجودة" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline5Description">الوصف</label>
                                <textarea id="timeline5Description" name="timeline_5_description" rows="3" placeholder="حصلنا على شهادات الجودة..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Item 6 -->
                        <div class="timeline-item-group">
                            <div class="timeline-item-header">
                                <h5>العنصر السادس</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="timeline6Enabled" name="timeline_6_enabled" value="1">
                                        تفعيل العنصر السادس
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeline6Year">السنة</label>
                                    <input type="text" id="timeline6Year" name="timeline_6_year" placeholder="2024" required>
                                </div>
                                <div class="form-group">
                                    <label for="timeline6Icon">الأيقونة</label>
                                    <input type="text" id="timeline6Icon" name="timeline_6_icon" placeholder="fas fa-rocket" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="timeline6Title">العنوان</label>
                                <input type="text" id="timeline6Title" name="timeline_6_title" placeholder="المستقبل المشرق" required>
                            </div>
                            <div class="form-group">
                                <label for="timeline6Description">الوصف</label>
                                <textarea id="timeline6Description" name="timeline_6_description" rows="3" placeholder="نواصل رحلتنا نحو التميز..." required></textarea>
                            </div>
                        </div>

                        <!-- Timeline Stats -->
                        <div class="statistics-section-header">
                            <h4>إحصائيات الخط الزمني</h4>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="timelineStatsEnabled" name="timeline_stats_enabled" value="1">
                                    تفعيل إحصائيات الخط الزمني
                                </label>
                                <small class="form-help">قم بتفعيل هذا الخيار لإظهار إحصائيات الخط الزمني في الصفحة الرئيسية</small>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="timelineStatYears">سنوات الخبرة</label>
                                <input type="text" id="timelineStatYears" name="timeline_stat_years" placeholder="6+" required>
                            </div>
                            <div class="form-group">
                                <label for="timelineStatCustomers">عدد العملاء</label>
                                <input type="text" id="timelineStatCustomers" name="timeline_stat_customers" placeholder="5000+" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="timelineStatProducts">عدد المنتجات</label>
                                <input type="text" id="timelineStatProducts" name="timeline_stat_products" placeholder="500+" required>
                            </div>
                            <div class="form-group">
                                <label for="timelineStatBrands">عدد العلامات التجارية</label>
                                <input type="text" id="timelineStatBrands" name="timeline_stat_brands" placeholder="50+" required>
                            </div>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ إعدادات الخط الزمني
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Benefits Showcase Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-star"></i> قسم عرض الفوائد التفاعلي</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <!-- Section Enable/Disable -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="benefitsShowcaseEnabled" name="benefits_showcase_enabled" value="1">
                            تفعيل قسم عرض الفوائد التفاعلي
                        </label>
                        <small class="form-help">قم بتفعيل هذا الخيار لإظهار قسم عرض الفوائد التفاعلي في الصفحة الرئيسية</small>
                    </div>

                    <form id="benefitsShowcaseSectionForm">
                        <!-- Section Header -->
                        <h4>إعدادات عامة للقسم</h4>
                        <div class="form-group">
                            <label for="benefitsShowcaseBadge">شارة القسم</label>
                            <input type="text" id="benefitsShowcaseBadge" name="benefits_showcase_badge" placeholder="فوائد منتجاتنا" required>
                        </div>
                        <div class="form-group">
                            <label for="benefitsShowcaseTitle">عنوان القسم</label>
                            <input type="text" id="benefitsShowcaseTitle" name="benefits_showcase_title" placeholder="اكتشف الفوائد المذهلة لمنتجاتنا" required>
                        </div>
                        <div class="form-group">
                            <label for="benefitsShowcaseSubtitle">وصف القسم</label>
                            <textarea id="benefitsShowcaseSubtitle" name="benefits_showcase_subtitle" rows="2" placeholder="منتجات مصممة خصيصاً لتلبية احتياجاتك وتحقيق أفضل النتائج" required></textarea>
                        </div>

                        <!-- Benefits Items -->
                        <h4>عناصر الفوائد</h4>

                        <!-- Benefit 1 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة الأولى</h5>
                                <div class="form-group">
                                    <span style="font-size: 0.9rem; color: #28a745; font-weight: 600;">
                                        <i class="fas fa-check-circle" style="margin-left: 0.5rem;"></i>
                                        فائدة أساسية
                                    </span>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit1Icon">الأيقونة</label>
                                    <input type="text" id="benefit1Icon" name="benefit_1_icon" placeholder="fas fa-leaf" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit1Title">العنوان</label>
                                    <input type="text" id="benefit1Title" name="benefit_1_title" placeholder="مكونات طبيعية" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit1Description">الوصف</label>
                                <textarea id="benefit1Description" name="benefit_1_description" rows="2" placeholder="مستخلصات طبيعية 100% آمنة على البشرة..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit1Details">التفاصيل</label>
                                <textarea id="benefit1Details" name="benefit_1_details" rows="2" placeholder="نستخدم أفضل المكونات الطبيعية..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit1StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit1StatValue" name="benefit_1_stat_value" placeholder="95%" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit1StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit1StatLabel" name="benefit_1_stat_label" placeholder="مكونات طبيعية" required>
                                </div>
                            </div>
                        </div>

                        <!-- Benefit 2 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة الثانية</h5>
                                <div class="form-group">
                                    <span style="font-size: 0.9rem; color: #28a745; font-weight: 600;">
                                        <i class="fas fa-check-circle" style="margin-left: 0.5rem;"></i>
                                        فائدة أساسية
                                    </span>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit2Icon">الأيقونة</label>
                                    <input type="text" id="benefit2Icon" name="benefit_2_icon" placeholder="fas fa-shield-check" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit2Title">العنوان</label>
                                    <input type="text" id="benefit2Title" name="benefit_2_title" placeholder="مختبر علمياً" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit2Description">الوصف</label>
                                <textarea id="benefit2Description" name="benefit_2_description" rows="2" placeholder="منتجات مختبرة في أفضل المختبرات العالمية..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit2Details">التفاصيل</label>
                                <textarea id="benefit2Details" name="benefit_2_details" rows="2" placeholder="جميع منتجاتنا تخضع لاختبارات صارمة..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit2StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit2StatValue" name="benefit_2_stat_value" placeholder="100%" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit2StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit2StatLabel" name="benefit_2_stat_label" placeholder="مختبر علمياً" required>
                                </div>
                            </div>
                        </div>

                        <!-- Benefit 3 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة الثالثة</h5>
                                <div class="form-group">
                                    <span style="font-size: 0.9rem; color: #28a745; font-weight: 600;">
                                        <i class="fas fa-check-circle" style="margin-left: 0.5rem;"></i>
                                        فائدة أساسية
                                    </span>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit3Icon">الأيقونة</label>
                                    <input type="text" id="benefit3Icon" name="benefit_3_icon" placeholder="fas fa-clock" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit3Title">العنوان</label>
                                    <input type="text" id="benefit3Title" name="benefit_3_title" placeholder="نتائج سريعة" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit3Description">الوصف</label>
                                <textarea id="benefit3Description" name="benefit_3_description" rows="2" placeholder="نتائج ملحوظة خلال أسابيع قليلة..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit3Details">التفاصيل</label>
                                <textarea id="benefit3Details" name="benefit_3_details" rows="2" placeholder="تركيبات متقدمة تضمن امتصاص سريع..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit3StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit3StatValue" name="benefit_3_stat_value" placeholder="2-4" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit3StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit3StatLabel" name="benefit_3_stat_label" placeholder="أسابيع للنتائج" required>
                                </div>
                            </div>
                        </div>

                        <!-- Benefit 4 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة الرابعة</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="benefit4Enabled" name="benefit_4_enabled" value="1">
                                        تفعيل الفائدة الرابعة
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit4Icon">الأيقونة</label>
                                    <input type="text" id="benefit4Icon" name="benefit_4_icon" placeholder="fas fa-award" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit4Title">العنوان</label>
                                    <input type="text" id="benefit4Title" name="benefit_4_title" placeholder="جودة مضمونة" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit4Description">الوصف</label>
                                <textarea id="benefit4Description" name="benefit_4_description" rows="2" placeholder="منتجات عالية الجودة مع ضمان الرضا التام..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit4Details">التفاصيل</label>
                                <textarea id="benefit4Details" name="benefit_4_details" rows="2" placeholder="نضمن لك أعلى معايير الجودة في جميع منتجاتنا..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit4StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit4StatValue" name="benefit_4_stat_value" placeholder="100%" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit4StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit4StatLabel" name="benefit_4_stat_label" placeholder="ضمان الجودة" required>
                                </div>
                            </div>
                        </div>

                        <!-- Benefit 5 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة الخامسة</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="benefit5Enabled" name="benefit_5_enabled" value="1">
                                        تفعيل الفائدة الخامسة
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit5Icon">الأيقونة</label>
                                    <input type="text" id="benefit5Icon" name="benefit_5_icon" placeholder="fas fa-shipping-fast" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit5Title">العنوان</label>
                                    <input type="text" id="benefit5Title" name="benefit_5_title" placeholder="توصيل سريع" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit5Description">الوصف</label>
                                <textarea id="benefit5Description" name="benefit_5_description" rows="2" placeholder="توصيل سريع وآمن لجميع أنحاء العراق..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit5Details">التفاصيل</label>
                                <textarea id="benefit5Details" name="benefit_5_details" rows="2" placeholder="خدمة توصيل موثوقة مع تتبع الطلبات..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit5StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit5StatValue" name="benefit_5_stat_value" placeholder="24-48" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit5StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit5StatLabel" name="benefit_5_stat_label" placeholder="ساعة للتوصيل" required>
                                </div>
                            </div>
                        </div>

                        <!-- Benefit 6 -->
                        <div class="benefit-item-group">
                            <div class="benefit-item-header">
                                <h5>الفائدة السادسة</h5>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="benefit6Enabled" name="benefit_6_enabled" value="1">
                                        تفعيل الفائدة السادسة
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit6Icon">الأيقونة</label>
                                    <input type="text" id="benefit6Icon" name="benefit_6_icon" placeholder="fas fa-headset" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit6Title">العنوان</label>
                                    <input type="text" id="benefit6Title" name="benefit_6_title" placeholder="دعم متميز" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="benefit6Description">الوصف</label>
                                <textarea id="benefit6Description" name="benefit_6_description" rows="2" placeholder="فريق دعم متخصص متاح على مدار الساعة..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="benefit6Details">التفاصيل</label>
                                <textarea id="benefit6Details" name="benefit_6_details" rows="2" placeholder="خدمة عملاء احترافية لحل جميع استفساراتك..." required></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="benefit6StatValue">قيمة الإحصائية</label>
                                    <input type="text" id="benefit6StatValue" name="benefit_6_stat_value" placeholder="24/7" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefit6StatLabel">تسمية الإحصائية</label>
                                    <input type="text" id="benefit6StatLabel" name="benefit_6_stat_label" placeholder="دعم متاح" required>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Benefits Summary -->
                        <div class="benefits-summary-section">
                            <h4 style="color: var(--primary-color); font-weight: 700; margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-chart-line"></i>
                                ملخص الفوائد
                            </h4>
                            <div style="background: linear-gradient(135deg, rgba(74, 144, 164, 0.05) 0%, rgba(44, 62, 80, 0.02) 100%); padding: 1.5rem; border-radius: 10px; border: 1px solid rgba(74, 144, 164, 0.15);">
                                <div class="form-group">
                                    <label for="benefitsSummaryTitle">عنوان الملخص</label>
                                    <input type="text" id="benefitsSummaryTitle" name="benefits_summary_title" placeholder="لماذا تختار منتجاتنا؟" required>
                                </div>
                                <div class="form-group">
                                    <label for="benefitsSummaryDescription">وصف الملخص</label>
                                    <textarea id="benefitsSummaryDescription" name="benefits_summary_description" rows="3" placeholder="نحن نؤمن بأن الجمال الحقيقي يأتي من منتجات عالية الجودة..." required></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="benefitsSummaryStat1Value">
                                            <i class="fas fa-percentage" style="margin-left: 0.5rem; color: var(--primary-color);"></i>
                                            الإحصائية الأولى - القيمة
                                        </label>
                                        <input type="text" id="benefitsSummaryStat1Value" name="benefits_summary_stat1_value" placeholder="98%" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="benefitsSummaryStat1Label">
                                            <i class="fas fa-tag" style="margin-left: 0.5rem; color: var(--primary-color);"></i>
                                            الإحصائية الأولى - التسمية
                                        </label>
                                        <input type="text" id="benefitsSummaryStat1Label" name="benefits_summary_stat1_label" placeholder="رضا العملاء" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ إعدادات عرض الفوائد التفاعلي
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Product Categories Section Settings -->
                <div class="form-section collapsible-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-th-large"></i> قسم فئات المنتجات</h3>
                        <i class="fas fa-chevron-down section-toggle-icon"></i>
                    </div>
                    <div class="section-content">
                        <!-- Section Enable/Disable -->
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="categoriesEnabled" name="categories_enabled" value="1">
                            تفعيل قسم فئات المنتجات
                        </label>
                        <small class="form-help">قم بتفعيل هذا الخيار لإظهار قسم فئات المنتجات في الصفحة الرئيسية</small>
                    </div>

                    <form id="categoriesSectionForm">
                        <!-- Section Header -->
                        <h4>إعدادات عامة للقسم</h4>
                        <div class="form-group">
                            <label for="categoriesBadge">شارة القسم</label>
                            <input type="text" id="categoriesBadge" name="categories_badge" placeholder="تسوق حسب الفئة" required>
                        </div>
                        <div class="form-group">
                            <label for="categoriesTitle">عنوان القسم</label>
                            <input type="text" id="categoriesTitle" name="categories_title" placeholder="اكتشف مجموعاتنا المتنوعة" required>
                        </div>
                        <div class="form-group">
                            <label for="categoriesSubtitle">وصف القسم</label>
                            <textarea id="categoriesSubtitle" name="categories_subtitle" rows="2" placeholder="تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك" required></textarea>
                        </div>

                        <!-- Category 1 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h4>الفئة الأولى</h4>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="category1Enabled" name="category_1_enabled" value="1">
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-label">تفعيل الفئة</span>
                                </label>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="category1Name">اسم الفئة</label>
                                    <input type="text" id="category1Name" name="category_1_name" placeholder="العناية بالبشرة" required>
                                </div>
                                <div class="form-group">
                                    <label for="category1Count">عدد المنتجات</label>
                                    <input type="text" id="category1Count" name="category_1_count" placeholder="150+ منتج" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="category1Description">وصف الفئة</label>
                                <input type="text" id="category1Description" name="category_1_description" placeholder="منتجات متخصصة للعناية اليومية بالبشرة" required>
                            </div>
                            <div class="form-group">
                                <label for="category1Image">رابط صورة الفئة</label>
                                <input type="url" id="category1Image" name="category_1_image" placeholder="https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=300&fit=crop" required>
                                <small class="form-help">أدخل رابط صورة عالية الجودة للفئة</small>
                            </div>
                        </div>

                        <!-- Category 2 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h4>الفئة الثانية</h4>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="category2Enabled" name="category_2_enabled" value="1">
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-label">تفعيل الفئة</span>
                                </label>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="category2Name">اسم الفئة</label>
                                    <input type="text" id="category2Name" name="category_2_name" placeholder="العناية بالشعر" required>
                                </div>
                                <div class="form-group">
                                    <label for="category2Count">عدد المنتجات</label>
                                    <input type="text" id="category2Count" name="category_2_count" placeholder="120+ منتج" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="category2Description">وصف الفئة</label>
                                <input type="text" id="category2Description" name="category_2_description" placeholder="شامبو وبلسم وعلاجات للشعر" required>
                            </div>
                            <div class="form-group">
                                <label for="category2Image">رابط صورة الفئة</label>
                                <input type="url" id="category2Image" name="category_2_image" placeholder="https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=400&h=300&fit=crop" required>
                                <small class="form-help">أدخل رابط صورة عالية الجودة للفئة</small>
                            </div>
                        </div>

                        <!-- Category 3 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h4>الفئة الثالثة</h4>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="category3Enabled" name="category_3_enabled" value="1">
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-label">تفعيل الفئة</span>
                                </label>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="category3Name">اسم الفئة</label>
                                    <input type="text" id="category3Name" name="category_3_name" placeholder="المكياج" required>
                                </div>
                                <div class="form-group">
                                    <label for="category3Count">عدد المنتجات</label>
                                    <input type="text" id="category3Count" name="category_3_count" placeholder="200+ منتج" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="category3Description">وصف الفئة</label>
                                <input type="text" id="category3Description" name="category_3_description" placeholder="مستحضرات تجميل عالية الجودة" required>
                            </div>
                            <div class="form-group">
                                <label for="category3Image">رابط صورة الفئة</label>
                                <input type="url" id="category3Image" name="category_3_image" placeholder="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop" required>
                                <small class="form-help">أدخل رابط صورة عالية الجودة للفئة</small>
                            </div>
                        </div>

                        <!-- Category 4 -->
                        <div class="category-section">
                            <div class="category-header">
                                <h4>الفئة الرابعة</h4>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="category4Enabled" name="category_4_enabled" value="1">
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-label">تفعيل الفئة</span>
                                </label>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="category4Name">اسم الفئة</label>
                                    <input type="text" id="category4Name" name="category_4_name" placeholder="العطور" required>
                                </div>
                                <div class="form-group">
                                    <label for="category4Count">عدد المنتجات</label>
                                    <input type="text" id="category4Count" name="category_4_count" placeholder="80+ منتج" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="category4Description">وصف الفئة</label>
                                <input type="text" id="category4Description" name="category_4_description" placeholder="عطور فاخرة للرجال والنساء" required>
                            </div>
                            <div class="form-group">
                                <label for="category4Image">رابط صورة الفئة</label>
                                <input type="url" id="category4Image" name="category_4_image" placeholder="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop" required>
                                <small class="form-help">أدخل رابط صورة عالية الجودة للفئة</small>
                            </div>
                        </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ إعدادات فئات المنتجات
                            </button>
                        </form>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script>
        // Note: Supabase client is now managed by the singleton pattern in supabase-config.js
        // All getSupabaseClient() calls will use the shared instance

        // Helper function to check for fallback saves
        async function checkForFallbackSaves() {
            try {
                const fallbackKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('site_setting_')) {
                        const data = JSON.parse(localStorage.getItem(key));
                        if (data && data.source === 'localStorage_fallback') {
                            fallbackKeys.push(key);
                        }
                    }
                }
                return fallbackKeys.length > 0;
            } catch (error) {
                console.error('Error checking fallback saves:', error);
                return false;
            }
        }

        // Helper function to retry failed saves from localStorage
        async function retryFailedSaves() {
            try {
                console.log('🔄 Attempting to retry failed saves...');

                // Update button to show loading state
                const retryBtn = document.getElementById('retryBtn');
                if (retryBtn) {
                    retryBtn.disabled = true;
                    retryBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إعادة المحاولة...';
                }

                const client = getSupabaseClient();
                if (!client) {
                    console.warn('⚠️ Supabase client not available for retry');
                    showMessage('لا يمكن الاتصال بقاعدة البيانات. يرجى التحقق من الاتصال بالإنترنت.', 'error');
                    return false;
                }

                let retryCount = 0;
                const fallbackKeys = [];

                // Collect all fallback saves
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('site_setting_')) {
                        const data = JSON.parse(localStorage.getItem(key));
                        if (data && data.source === 'localStorage_fallback') {
                            fallbackKeys.push({ key, data });
                        }
                    }
                }

                // Retry each fallback save
                for (const { key, data } of fallbackKeys) {
                    try {
                        const { error } = await client
                            .from('site_settings')
                            .upsert({
                                setting_key: data.setting_key,
                                setting_value: data.setting_value,
                                setting_group: data.setting_group,
                                updated_at: new Date().toISOString()
                            }, {
                                onConflict: 'setting_key',
                                ignoreDuplicates: false
                            });

                        if (!error) {
                            // Success - remove from localStorage
                            localStorage.removeItem(key);
                            retryCount++;
                            console.log(`✅ Successfully retried save for ${data.setting_key}`);
                        }
                    } catch (retryError) {
                        console.warn(`⚠️ Failed to retry save for ${data.setting_key}:`, retryError);
                    }
                }

                if (retryCount > 0) {
                    console.log(`✅ Successfully retried ${retryCount} failed saves`);
                    showMessage(`تم حفظ ${retryCount} إعداد مؤجل بنجاح!`);

                    // Update retry button
                    setTimeout(() => {
                        checkAndShowRetryButton();
                    }, 1000);

                    return true;
                } else {
                    showMessage('لا توجد إعدادات مؤجلة للحفظ.', 'warning');
                }

                return false;
            } catch (error) {
                console.error('Error retrying failed saves:', error);
                showMessage('حدث خطأ أثناء إعادة المحاولة. يرجى المحاولة مرة أخرى.', 'error');
                return false;
            } finally {
                // Reset button state
                const retryBtn = document.getElementById('retryBtn');
                if (retryBtn) {
                    retryBtn.disabled = false;
                    retryBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة المحاولة';
                }
            }
        }

        // Check and show retry button if needed
        async function checkAndShowRetryButton() {
            try {
                const hasFallbackSaves = await checkForFallbackSaves();
                const retryBtn = document.getElementById('retryBtn');
                if (retryBtn) {
                    if (hasFallbackSaves) {
                        retryBtn.classList.remove('hidden');
                        retryBtn.classList.add('visible');
                        retryBtn.style.display = 'inline-flex'; // Keep this for specific flex display
                        retryBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة المحاولة';
                    } else {
                        retryBtn.classList.add('hidden');
                        retryBtn.classList.remove('visible');
                    }
                }
            } catch (error) {
                console.error('Error checking retry button:', error);
            }
        }

        // Global variables
        let currentSettings = {};

        // Check authentication
        function checkAuth() {
            try {
                const adminUser = sessionStorage.getItem('adminUser');
                if (!adminUser) {
                    console.log('No admin user found in session storage');
                    window.location.href = 'login.html';
                    return false;
                }

                const user = JSON.parse(adminUser);

                // Check if user object has required properties
                if (!user || !user.loginTime) {
                    console.log('Invalid user data in session storage');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                // Check session expiration (8 hours)
                if (hoursDiff >= 8) {
                    console.log('Session expired after', hoursDiff, 'hours');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                // Update user info in sidebar - always use standardized values
                try {
                    const sidebarUserName = document.getElementById('sidebarUserName');
                    const sidebarUserAvatar = document.getElementById('sidebarUserAvatar');

                    if (sidebarUserName) {
                        sidebarUserName.textContent = 'مدير النظام';
                    }
                    if (sidebarUserAvatar) {
                        sidebarUserAvatar.textContent = 'A';
                        sidebarUserAvatar.setAttribute('title', 'مدير النظام');
                    }
                } catch (uiError) {
                    console.warn('Error updating user info in UI:', uiError);
                    // Don't redirect for UI errors, just log them
                }

                console.log('Authentication successful for user:', user.username);
                return true;
            } catch (error) {
                console.error('Authentication error:', error);
                sessionStorage.removeItem('adminUser');
                window.location.href = 'login.html';
                return false;
            }
        }

        // Enhanced logout function with modal
        function showLogoutModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="color: #dc3545; font-size: 3rem; margin-bottom: 1rem;">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h3 style="color: #333; margin-bottom: 1rem;">تسجيل الخروج</h3>
                    <p style="color: #666; margin-bottom: 2rem;">هل أنت متأكد من تسجيل الخروج من لوحة التحكم؟</p>
                    <div style="display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="confirmLogout()" style="
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-check"></i> نعم، تسجيل الخروج
                        </button>
                        <button onclick="closeLogoutModal()" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentLogoutModal = modal;
        }

        function confirmLogout() {
            sessionStorage.removeItem('adminUser');
            window.location.href = 'login.html';
        }

        function closeLogoutModal() {
            if (window.currentLogoutModal) {
                window.currentLogoutModal.remove();
                window.currentLogoutModal = null;
            }
        }

        // Enhanced tab switching functionality
        function switchTab(tabName) {
            console.log(`🔄 Switching to tab: ${tabName}`);

            // Remove active class from all tabs and sections
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.remove('active');
            });

            // Add active class to clicked tab and corresponding section
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}-section`).classList.add('active');

            // If switching to homepage tab, reload current settings to populate forms
            if (tabName === 'homepage') {
                console.log('🏠 Loading homepage settings...');
                // Add a small delay to ensure tab content is visible
                setTimeout(() => {
                    loadSettings();
                }, 200);
            }
        }

        // Social media functions
        function previewSocialLinks() {
            const preview = document.getElementById('socialPreview');
            const previewContainer = document.getElementById('socialLinksPreview');

            const socialInputs = {
                'whatsapp': { icon: 'fab fa-whatsapp', color: '#25D366', name: 'واتساب' },
                'facebook': { icon: 'fab fa-facebook', color: '#1877F2', name: 'فيسبوك' },
                'instagram': { icon: 'fab fa-instagram', color: '#E4405F', name: 'إنستغرام' },
                'twitter': { icon: 'fab fa-twitter', color: '#1DA1F2', name: 'تويتر' },
                'telegram': { icon: 'fab fa-telegram', color: '#0088CC', name: 'تليغرام' },
                'linkedin': { icon: 'fab fa-linkedin', color: '#0A66C2', name: 'لينكد إن' },
                'tiktok': { icon: 'fab fa-tiktok', color: '#000000', name: 'تيك توك' },
                'youtube': { icon: 'fab fa-youtube', color: '#FF0000', name: 'يوتيوب' },
                'snapchat': { icon: 'fab fa-snapchat', color: '#FFFC00', name: 'سناب شات' }
            };

            let previewHTML = '';

            Object.keys(socialInputs).forEach(platform => {
                const input = document.getElementById(`${platform}${platform === 'whatsapp' ? 'Number' : 'Url'}`);
                if (input && input.value.trim()) {
                    const config = socialInputs[platform];
                    const url = platform === 'whatsapp' ? `https://wa.me/${input.value}` : input.value;

                    previewHTML += `
                        <a href="${url}" target="_blank" class="social-link-item">
                            <i class="${config.icon}" style="color: ${config.color};"></i>
                            ${config.name}
                        </a>
                    `;
                }
            });

            if (previewHTML) {
                previewContainer.innerHTML = previewHTML;
                preview.classList.remove('hidden');
                preview.classList.add('visible');
            } else {
                previewContainer.innerHTML = '<p style="color: #666; text-align: center;">لا توجد روابط للمعاينة</p>';
                preview.classList.remove('hidden');
                preview.classList.add('visible');
            }
        }



        // Enhanced notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            `;

            // Add notification styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 0.8rem;
                font-family: 'Cairo', sans-serif;
                font-weight: 600;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function getNotificationIcon(type) {
            const icons = {
                'success': 'check-circle',
                'warning': 'exclamation-triangle',
                'error': 'times-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function getNotificationColor(type) {
            const colors = {
                'success': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                'warning': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
                'error': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                'info': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
            };
            return colors[type] || colors.info;
        }









        // Enhanced Success Notification System
        function showEnhancedSuccessNotification(title, message) {
            try {
                // Remove any existing notification
                const existingNotification = document.querySelector('.enhanced-success-notification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                // Create new notification element
                const notification = document.createElement('div');
                notification.className = 'enhanced-success-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <div class="notification-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="notification-text">
                            <div class="notification-title">${title || 'تم الحفظ بنجاح!'}</div>
                            <div class="notification-message">${message || ''}</div>
                        </div>
                        <button class="notification-close" onclick="closeEnhancedNotification()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="notification-progress"></div>
                    </div>
                `;

                // Ensure body exists before appending
                if (document.body) {
                    document.body.appendChild(notification);

                    // Trigger show animation
                    setTimeout(() => {
                        notification.classList.add('show');
                    }, 10);

                    // Auto close after 5 seconds
                    setTimeout(() => {
                        closeEnhancedNotification();
                    }, 5000);
                } else {
                    console.error('Document body not available for notification');
                }
            } catch (error) {
                console.error('Error showing enhanced notification:', error);
                // Fallback to simple alert
                alert(message || title || 'تم الحفظ بنجاح!');
            }
        }

        function closeEnhancedNotification() {
            try {
                const notification = document.querySelector('.enhanced-success-notification');
                if (notification) {
                    notification.classList.add('hide');
                    setTimeout(() => {
                        if (notification && notification.parentElement) {
                            notification.parentElement.removeChild(notification);
                        }
                    }, 500);
                }
            } catch (error) {
                console.error('Error closing notification:', error);
            }
        }

        // Enhanced warning notification function
        function showEnhancedWarningNotification(title, message) {
            try {
                // Remove any existing warning notifications
                const existingWarning = document.querySelector('.enhanced-warning-notification');
                if (existingWarning) {
                    existingWarning.remove();
                }

                // Create warning notification
                const notification = document.createElement('div');
                notification.className = 'enhanced-warning-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="notification-text">
                                <div class="notification-title">${title}</div>
                                <div class="notification-message">${message}</div>
                            </div>
                            <button class="notification-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;

                // Add warning-specific styles
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 450px;
                    width: calc(100% - 40px);
                    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                    border-radius: 15px;
                    box-shadow: 0 20px 40px rgba(255, 193, 7, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);
                    transform: translateX(500px) scale(0.8);
                    opacity: 0;
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                    font-family: 'Cairo', sans-serif;
                    border: 1px solid rgba(255, 193, 7, 0.3);
                `;

                document.body.appendChild(notification);

                // Trigger animation
                setTimeout(() => {
                    notification.style.transform = 'translateX(0) scale(1)';
                    notification.style.opacity = '1';
                }, 100);

                // Auto-hide after 6 seconds
                setTimeout(() => {
                    if (notification && notification.parentNode) {
                        notification.style.transform = 'translateX(500px) scale(0.8)';
                        notification.style.opacity = '0';
                        setTimeout(() => {
                            if (notification && notification.parentNode) {
                                notification.remove();
                            }
                        }, 500);
                    }
                }, 6000);

            } catch (error) {
                console.error('Error showing warning notification:', error);
            }
        }

        // Enhanced message function with better error handling
        function showMessage(message, type = 'success') {
            try {
                if (type === 'success') {
                    // Use enhanced notification for success messages
                    showEnhancedSuccessNotification('تم الحفظ بنجاح!', message);
                    return;
                } else if (type === 'warning') {
                    // Use enhanced notification for warning messages
                    showEnhancedWarningNotification('تحذير', message);
                    return;
                }

                // Handle error messages with fallback
                const successDiv = document.getElementById('successMessage');
                const errorDiv = document.getElementById('errorMessage');

                if (!successDiv || !errorDiv) {
                    // Fallback: create temporary notification if elements don't exist
                    console.warn('Message elements not found, using fallback notification');
                    showFallbackNotification(message, type);
                    return;
                }

                if (type === 'error') {
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('hidden');
                    errorDiv.classList.add('visible');
                    successDiv.classList.add('hidden');
                    successDiv.classList.remove('visible');

                    // Hide message after 7 seconds for errors (longer than success)
                    setTimeout(() => {
                        if (errorDiv) {
                            errorDiv.classList.add('hidden');
                            errorDiv.classList.remove('visible');
                        }
                        if (successDiv) {
                            successDiv.classList.add('hidden');
                            successDiv.classList.remove('visible');
                        }
                    }, 7000);
                }
            } catch (error) {
                console.error('Error in showMessage:', error);
                // Ultimate fallback
                alert(message);
            }
        }

        // Fallback notification system
        function showFallbackNotification(message, type = 'success') {
            const notification = document.createElement('div');

            let backgroundColor;
            if (type === 'success') {
                backgroundColor = '#28a745';
            } else if (type === 'warning') {
                backgroundColor = '#ffc107';
            } else {
                backgroundColor = '#dc3545';
            }

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                padding: 1rem 1.5rem;
                background: ${backgroundColor};
                color: ${type === 'warning' ? '#856404' : 'white'};
                border-radius: 8px;
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Show animation
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Auto remove
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        // Load all settings from database
        async function loadSettings() {
            try {
                console.log('🔄 Loading settings from database...');

                // Get Supabase client using singleton pattern
                const supabase = getSupabaseClient();
                if (!supabase) {
                    console.error('❌ Supabase client not available for loading settings');
                    showMessage('خطأ في الاتصال بقاعدة البيانات', 'error');
                    return;
                }

                const { data: settings, error } = await supabase
                    .from('site_settings')
                    .select('setting_key, setting_value, setting_group');

                if (error) throw error;

                console.log(`📊 Loaded ${settings.length} settings from database`);

                // Convert array to object for easier access
                currentSettings = {};
                settings.forEach(setting => {
                    currentSettings[setting.setting_key] = setting.setting_value;
                });

                console.log('📋 Current settings object:', {
                    totalKeys: Object.keys(currentSettings).length,
                    featureKeys: Object.keys(currentSettings).filter(key => key.startsWith('feature_')),
                    aboutKeys: Object.keys(currentSettings).filter(key => key.startsWith('about_')),

                    featuresMainFields: {
                        badge: currentSettings.features_badge,
                        title: currentSettings.features_title,
                        subtitle: currentSettings.features_subtitle
                    },
                    aboutMainFields: {
                        badge: currentSettings.about_badge,
                        title: currentSettings.about_title,
                        subtitle: currentSettings.about_subtitle,
                        desc1: currentSettings.about_description_1,
                        desc2: currentSettings.about_description_2
                    }
                });

                // Populate forms with current settings
                // Add a small delay to ensure DOM is ready
                setTimeout(() => {
                    console.log('🔄 Starting form population after DOM ready...');
                    populateForms();
                }, 100);

            } catch (error) {
                console.error('❌ Error loading settings:', error);
                showMessage('خطأ في تحميل الإعدادات', 'error');
            }
        }

        // Populate forms with current settings
        function populateForms() {
            console.log('🔄 Populating forms with current settings...', currentSettings);
            // Contact Information
            document.getElementById('businessName').value = currentSettings.business_name || '';
            document.getElementById('businessPhone').value = currentSettings.business_phone || '';
            document.getElementById('businessEmail').value = currentSettings.business_email || '';
            document.getElementById('businessAddress').value = currentSettings.business_address || '';
            document.getElementById('copyrightText').value = currentSettings.copyright_text || 'جميع الحقوق محفوظة';



            // Business Hours
            document.getElementById('workingDays').value = currentSettings.working_days || '';
            document.getElementById('workingHours').value = currentSettings.working_hours || '';
            document.getElementById('closedDay').value = currentSettings.closed_day || '';

            // Social Media
            document.getElementById('whatsappNumber').value = currentSettings.whatsapp_number || '';
            document.getElementById('facebookUrl').value = currentSettings.facebook_url || '';
            document.getElementById('instagramUrl').value = currentSettings.instagram_url || '';
            document.getElementById('twitterUrl').value = currentSettings.twitter_url || '';
            document.getElementById('telegramUrl').value = currentSettings.telegram_url || '';
            document.getElementById('linkedinUrl').value = currentSettings.linkedin_url || '';
            document.getElementById('tiktokUrl').value = currentSettings.tiktok_url || '';
            document.getElementById('youtubeUrl').value = currentSettings.youtube_url || '';
            document.getElementById('snapchatUrl').value = currentSettings.snapchat_url || '';






            // Features 5 and 6
            const feature5Title = document.getElementById('feature5Title');
            if (feature5Title) feature5Title.value = currentSettings.feature_5_title || 'ضمان الاسترداد';

            const feature5Description = document.getElementById('feature5Description');
            if (feature5Description) feature5Description.value = currentSettings.feature_5_description || 'إمكانية إرجاع المنتج خلال 30 يوم مع ضمان استرداد كامل للمبلغ دون أي شروط معقدة';

            const feature5Icon = document.getElementById('feature5Icon');
            if (feature5Icon) feature5Icon.value = currentSettings.feature_5_icon || 'fas fa-undo-alt';

            const feature6Title = document.getElementById('feature6Title');
            if (feature6Title) feature6Title.value = currentSettings.feature_6_title || 'استشارة مجانية';

            const feature6Description = document.getElementById('feature6Description');
            if (feature6Description) feature6Description.value = currentSettings.feature_6_description || 'استشارة مجانية من خبراء العناية المعتمدين لاختيار المنتجات الأنسب لنوع بشرتك وشعرك';

            const feature6Icon = document.getElementById('feature6Icon');
            if (feature6Icon) feature6Icon.value = currentSettings.feature_6_icon || 'fas fa-user-md';



            // Page Descriptions
            document.getElementById('productsPageDesc').value = currentSettings.products_page_description || 'اكتشف مجموعتنا الكاملة من منتجات العناية بالبشرة والشعر';
            document.getElementById('offersPageDesc').value = currentSettings.offers_page_description || 'اكتشف أفضل العروض على منتجات العناية بالبشرة والشعر';
            document.getElementById('contactPageDesc').value = currentSettings.contact_page_description || 'نحن هنا لمساعدتك والإجابة على جميع استفساراتك';
            document.getElementById('faqPageDesc').value = currentSettings.faq_page_description || 'إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا';

            document.getElementById('guidelinesPageDesc').value = currentSettings.guidelines_page_description || 'تعلم كيفية استخدام منتجات العناية بالبشرة والشعر بالطريقة الصحيحة';
            document.getElementById('termsPageDesc').value = currentSettings.terms_page_description || 'يرجى قراءة الشروط والأحكام بعناية قبل استخدام خدماتنا';

            // Page Header Backgrounds
            document.getElementById('productsHeaderBgEnabled').checked = currentSettings.products_header_bg_enabled === 'true';
            document.getElementById('productsHeaderBgUrl').value = currentSettings.products_header_bg_url || '';
            document.getElementById('contactHeaderBgEnabled').checked = currentSettings.contact_header_bg_enabled === 'true';
            document.getElementById('contactHeaderBgUrl').value = currentSettings.contact_header_bg_url || '';
            document.getElementById('faqHeaderBgEnabled').checked = currentSettings.faq_header_bg_enabled === 'true';
            document.getElementById('faqHeaderBgUrl').value = currentSettings.faq_header_bg_url || '';
            document.getElementById('guidelinesHeaderBgEnabled').checked = currentSettings.guidelines_header_bg_enabled === 'true';
            document.getElementById('guidelinesHeaderBgUrl').value = currentSettings.guidelines_header_bg_url || '';
            document.getElementById('termsHeaderBgEnabled').checked = currentSettings.terms_header_bg_enabled === 'true';
            document.getElementById('termsHeaderBgUrl').value = currentSettings.terms_header_bg_url || '';
            document.getElementById('offersHeaderBgEnabled').checked = currentSettings.offers_header_bg_enabled === 'true';
            document.getElementById('offersHeaderBgUrl').value = currentSettings.offers_header_bg_url || '';

            // Basic Store Information (handled separately in homepage section)

            // Homepage Hero Section Settings
            const heroTitleLine1 = document.getElementById('heroTitleLine1');
            if (heroTitleLine1) heroTitleLine1.value = currentSettings.hero_title_line1 || 'اكتشف عالم الجمال الحقيقي';

            const heroTitleLine2 = document.getElementById('heroTitleLine2');
            if (heroTitleLine2) heroTitleLine2.value = currentSettings.hero_title_line2 || 'مع متجر Care';

            const heroSubtitle = document.getElementById('heroSubtitle');
            if (heroSubtitle) heroSubtitle.value = currentSettings.hero_subtitle || 'منتجات عناية فاخرة من أفضل العلامات التجارية العالمية - جودة استثنائية وخدمة لا مثيل لها في جميع أنحاء العراق';

            const trustIndicator1 = document.getElementById('trustIndicator1');
            if (trustIndicator1) trustIndicator1.value = currentSettings.trust_indicator_1 || 'منتجات أصلية 100%';

            const trustIndicator1Icon = document.getElementById('trustIndicator1Icon');
            if (trustIndicator1Icon) trustIndicator1Icon.value = currentSettings.trust_indicator_1_icon || 'fas fa-certificate';

            const trustIndicator2 = document.getElementById('trustIndicator2');
            if (trustIndicator2) trustIndicator2.value = currentSettings.trust_indicator_2 || 'توصيل مجاني للطلبات +50 ألف';

            const trustIndicator2Icon = document.getElementById('trustIndicator2Icon');
            if (trustIndicator2Icon) trustIndicator2Icon.value = currentSettings.trust_indicator_2_icon || 'fas fa-shipping-fast';

            const trustIndicator3 = document.getElementById('trustIndicator3');
            if (trustIndicator3) trustIndicator3.value = currentSettings.trust_indicator_3 || 'ضمان الاسترداد 30 يوم';

            const trustIndicator3Icon = document.getElementById('trustIndicator3Icon');
            if (trustIndicator3Icon) trustIndicator3Icon.value = currentSettings.trust_indicator_3_icon || 'fas fa-undo-alt';

            const featuredProductName = document.getElementById('featuredProductName');
            if (featuredProductName) featuredProductName.value = currentSettings.featured_product_name || 'سيروم فيتامين سي المتقدم';

            const featuredProductPrice = document.getElementById('featuredProductPrice');
            if (featuredProductPrice) featuredProductPrice.value = currentSettings.featured_product_price || '45,000 د.ع';

            const featuredProductOriginalPrice = document.getElementById('featuredProductOriginalPrice');
            if (featuredProductOriginalPrice) featuredProductOriginalPrice.value = currentSettings.featured_product_original_price || '60,000 د.ع';

            // Homepage Featured Products Section Settings
            const featuredProductsBadge = document.getElementById('featuredProductsBadge');
            if (featuredProductsBadge) featuredProductsBadge.value = currentSettings.featured_products_badge || 'منتجات مختارة بعناية';

            const featuredProductsTitle = document.getElementById('featuredProductsTitle');
            if (featuredProductsTitle) featuredProductsTitle.value = currentSettings.featured_products_title || 'المنتجات المميزة';

            const featuredProductsSubtitle = document.getElementById('featuredProductsSubtitle');
            if (featuredProductsSubtitle) featuredProductsSubtitle.value = currentSettings.featured_products_subtitle || 'اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر من العلامات التجارية الموثوقة';

            // Homepage Features Section Settings
            console.log('🔧 Populating features main section fields...');
            const featuresBadge = document.getElementById('featuresBadge');
            const featuresTitle = document.getElementById('featuresTitle');
            const featuresSubtitle = document.getElementById('featuresSubtitle');

            console.log('Features main fields found:', {
                badgeField: !!featuresBadge,
                titleField: !!featuresTitle,
                subtitleField: !!featuresSubtitle
            });

            if (featuresBadge) {
                featuresBadge.value = currentSettings.features_badge || 'خدماتنا المميزة';
                console.log('✅ Set featuresBadge value:', currentSettings.features_badge || 'خدماتنا المميزة');
            } else {
                console.warn('❌ featuresBadge field not found!');
            }

            if (featuresTitle) {
                featuresTitle.value = currentSettings.features_title || 'لماذا تختار متجر Care؟';
                console.log('✅ Set featuresTitle value:', currentSettings.features_title || 'لماذا تختار متجر Care؟');
            } else {
                console.warn('❌ featuresTitle field not found!');
            }

            if (featuresSubtitle) {
                featuresSubtitle.value = currentSettings.features_subtitle || 'نقدم لك تجربة تسوق استثنائية مع خدمات متطورة وضمانات شاملة تضمن رضاك التام';
                console.log('✅ Set featuresSubtitle value:', (currentSettings.features_subtitle || 'نقدم لك تجربة تسوق استثنائية مع خدمات متطورة وضمانات شاملة تضمن رضاك التام').substring(0, 50) + '...');
            } else {
                console.warn('❌ featuresSubtitle field not found!');
            }

            // Homepage Features Section - Individual Features (1-6)
            console.log('🔧 Populating features section...');
            console.log('Features section main fields:', {
                badgeValue: currentSettings.features_badge,
                titleValue: currentSettings.features_title,
                subtitleValue: currentSettings.features_subtitle
            });

            for (let i = 1; i <= 6; i++) {
                const featureTitleField = document.getElementById(`feature${i}Title`);
                const featureDescriptionField = document.getElementById(`feature${i}Description`);
                const featureIconField = document.getElementById(`feature${i}Icon`);

                const titleValue = currentSettings[`feature_${i}_title`] || '';
                const descriptionValue = currentSettings[`feature_${i}_description`] || '';
                const iconValue = currentSettings[`feature_${i}_icon`] || '';

                console.log(`Feature ${i}:`, {
                    titleField: !!featureTitleField,
                    descriptionField: !!featureDescriptionField,
                    iconField: !!featureIconField,
                    titleValue,
                    descriptionValue,
                    iconValue,

                    dbTitle: currentSettings[`feature_${i}_title`],
                    dbDescription: currentSettings[`feature_${i}_description`],
                    dbIcon: currentSettings[`feature_${i}_icon`]
                });

                if (featureTitleField) {
                    featureTitleField.value = titleValue;
                    console.log(`✅ Set feature${i}Title value:`, titleValue);
                }
                if (featureDescriptionField) {
                    featureDescriptionField.value = descriptionValue;
                    console.log(`✅ Set feature${i}Description value:`, descriptionValue.substring(0, 30) + '...');
                }
                if (featureIconField) {
                    featureIconField.value = iconValue;
                    console.log(`✅ Set feature${i}Icon value:`, iconValue);
                }
            }

            // Homepage About Section Settings
            console.log('📖 Populating about section...');
            const aboutBadge = document.getElementById('aboutBadge');
            const aboutTitle = document.getElementById('aboutTitle');
            const aboutSubtitle = document.getElementById('aboutSubtitle');
            const aboutDescription1 = document.getElementById('aboutDescription1');
            const aboutDescription2 = document.getElementById('aboutDescription2');

            const aboutBadgeValue = currentSettings.about_badge || 'قصتنا';
            const aboutTitleValue = currentSettings.about_title || 'من نحن';
            const aboutSubtitleValue = currentSettings.about_subtitle || 'رحلتنا في عالم الجمال والعناية بدأت من شغف حقيقي بتقديم الأفضل لعملائنا الكرام في جميع أنحاء العراق';
            const aboutDescription1Value = currentSettings.about_description_1 || 'منذ تأسيسنا عام 2018، نسعى لتقديم أفضل منتجات العناية بالبشرة والشعر من أرقى العلامات التجارية العالمية. نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة والمنتجات عالية الجودة.';
            const aboutDescription2Value = currentSettings.about_description_2 || 'فريقنا المتخصص يعمل بجد لاختيار كل منتج بعناية فائقة، مع التأكد من أصالته وجودته العالية. نحن لسنا مجرد متجر، بل شريكك الموثوق في رحلة العناية والجمال.';

            console.log('About section data:', {
                badgeField: !!aboutBadge,
                titleField: !!aboutTitle,
                subtitleField: !!aboutSubtitle,
                description1Field: !!aboutDescription1,
                description2Field: !!aboutDescription2,
                badgeValue: aboutBadgeValue,
                titleValue: aboutTitleValue,
                subtitleValue: aboutSubtitleValue.substring(0, 50) + '...',
                description1Value: aboutDescription1Value.substring(0, 50) + '...',
                description2Value: aboutDescription2Value.substring(0, 50) + '...',

                dbBadge: currentSettings.about_badge,
                dbTitle: currentSettings.about_title,
                dbSubtitle: currentSettings.about_subtitle,
                dbDesc1: currentSettings.about_description_1,
                dbDesc2: currentSettings.about_description_2
            });

            if (aboutBadge) {
                aboutBadge.value = aboutBadgeValue;
                console.log('✅ Set aboutBadge value:', aboutBadgeValue);
            }
            if (aboutTitle) {
                aboutTitle.value = aboutTitleValue;
                console.log('✅ Set aboutTitle value:', aboutTitleValue);
            }
            if (aboutSubtitle) {
                aboutSubtitle.value = aboutSubtitleValue;
                console.log('✅ Set aboutSubtitle value:', aboutSubtitleValue.substring(0, 50) + '...');
            }
            if (aboutDescription1) {
                aboutDescription1.value = aboutDescription1Value;
                console.log('✅ Set aboutDescription1 value:', aboutDescription1Value.substring(0, 50) + '...');
            }
            if (aboutDescription2) {
                aboutDescription2.value = aboutDescription2Value;
                console.log('✅ Set aboutDescription2 value:', aboutDescription2Value.substring(0, 50) + '...');
            }

            // Homepage Statistics Section Settings
            const statCustomers = document.getElementById('statCustomers');
            if (statCustomers) statCustomers.value = currentSettings.stat_customers || '12,500+';

            const statOrders = document.getElementById('statOrders');
            if (statOrders) statOrders.value = currentSettings.stat_orders || '25,000+';

            const statYears = document.getElementById('statYears');
            if (statYears) statYears.value = currentSettings.stat_years || '7+';

            const statRating = document.getElementById('statRating');
            if (statRating) statRating.value = currentSettings.stat_rating || '4.9/5';

            // Hero Background Image Settings
            const heroBackgroundEnabled = document.getElementById('heroBackgroundEnabled');
            if (heroBackgroundEnabled) {
                heroBackgroundEnabled.checked = currentSettings.hero_background_enabled === '1' || currentSettings.hero_background_enabled === true;
                toggleHeroBackgroundField(heroBackgroundEnabled.checked);
            }

            const heroBackgroundUrl = document.getElementById('heroBackgroundUrl');
            if (heroBackgroundUrl) heroBackgroundUrl.value = currentSettings.hero_background_url || '';

            // Update hero background preview if URL exists
            if (currentSettings.hero_background_url) {
                updateHeroBackgroundPreview(currentSettings.hero_background_url);
            }

            // Hero Showcase Image Settings
            const heroShowcaseEnabled = document.getElementById('heroShowcaseEnabled');
            if (heroShowcaseEnabled) {
                heroShowcaseEnabled.checked = currentSettings.hero_showcase_enabled === '1' || currentSettings.hero_showcase_enabled === true;
                toggleHeroShowcaseField(heroShowcaseEnabled.checked);
            }

            const heroShowcaseUrl = document.getElementById('heroShowcaseUrl');
            if (heroShowcaseUrl) heroShowcaseUrl.value = currentSettings.hero_showcase_url || '';

            // Update hero showcase preview if URL exists
            if (currentSettings.hero_showcase_url) {
                updateHeroShowcasePreview(currentSettings.hero_showcase_url);
            }

            // Trust Indicators Toggle Settings
            const trustIndicatorsEnabled = document.getElementById('trustIndicatorsEnabled');
            if (trustIndicatorsEnabled) {
                trustIndicatorsEnabled.checked = currentSettings.trust_indicators_enabled === '1' || currentSettings.trust_indicators_enabled === true || currentSettings.trust_indicators_enabled === undefined;
            }

            // Featured Product Toggle Settings
            const featuredProductEnabled = document.getElementById('featuredProductEnabled');
            if (featuredProductEnabled) {
                featuredProductEnabled.checked = currentSettings.featured_product_enabled === '1' || currentSettings.featured_product_enabled === true || currentSettings.featured_product_enabled === undefined;
            }

            // Individual Features Toggle Settings
            for (let i = 1; i <= 6; i++) {
                const featureEnabled = document.getElementById(`feature${i}Enabled`);
                if (featureEnabled) {
                    featureEnabled.checked = currentSettings[`feature_${i}_enabled`] === '1' || currentSettings[`feature_${i}_enabled`] === true || currentSettings[`feature_${i}_enabled`] === undefined;
                }
            }

            // Testimonials Section Settings
            const testimonialsEnabled = document.getElementById('testimonialsEnabled');
            if (testimonialsEnabled) {
                testimonialsEnabled.checked = currentSettings.testimonials_enabled === '1' || currentSettings.testimonials_enabled === true;
                toggleTestimonialsSection(testimonialsEnabled.checked);
            }

            const testimonialsBadge = document.getElementById('testimonialsBadge');
            if (testimonialsBadge) testimonialsBadge.value = currentSettings.testimonials_badge || 'آراء عملائنا';

            const testimonialsTitle = document.getElementById('testimonialsTitle');
            if (testimonialsTitle) testimonialsTitle.value = currentSettings.testimonials_title || 'ماذا يقول عملاؤنا عنا؟';

            const testimonialsSubtitle = document.getElementById('testimonialsSubtitle');
            if (testimonialsSubtitle) testimonialsSubtitle.value = currentSettings.testimonials_subtitle || 'تجارب حقيقية من عملائنا الكرام الذين جربوا منتجاتنا وخدماتنا المتميزة';

            // Testimonial 1
            const testimonial1Name = document.getElementById('testimonial1Name');
            if (testimonial1Name) testimonial1Name.value = currentSettings.testimonial_1_name || 'سارة أحمد';

            const testimonial1Location = document.getElementById('testimonial1Location');
            if (testimonial1Location) testimonial1Location.value = currentSettings.testimonial_1_location || 'بغداد';

            const testimonial1Initial = document.getElementById('testimonial1Initial');
            if (testimonial1Initial) testimonial1Initial.value = currentSettings.testimonial_1_initial || 'س';

            const testimonial1Rating = document.getElementById('testimonial1Rating');
            if (testimonial1Rating) testimonial1Rating.value = currentSettings.testimonial_1_rating || '5.0';

            const testimonial1Text = document.getElementById('testimonial1Text');
            if (testimonial1Text) testimonial1Text.value = currentSettings.testimonial_1_text || 'منتجات رائعة وجودة عالية، خاصة سيروم فيتامين سي. لاحظت تحسن كبير في بشرتي خلال أسبوعين فقط. التوصيل سريع والتعامل محترف جداً.';

            // Testimonials Statistics
            const testimonialsTotalReviews = document.getElementById('testimonialsTotalReviews');
            if (testimonialsTotalReviews) testimonialsTotalReviews.value = currentSettings.testimonials_total_reviews || '2,500+';

            const testimonialsAverageRating = document.getElementById('testimonialsAverageRating');
            if (testimonialsAverageRating) testimonialsAverageRating.value = currentSettings.testimonials_average_rating || '4.9';

            const testimonialsSatisfaction = document.getElementById('testimonialsSatisfaction');
            if (testimonialsSatisfaction) testimonialsSatisfaction.value = currentSettings.testimonials_satisfaction || '98%';

            // Load testimonials stats toggle
            const testimonialsStatsEnabled = document.getElementById('testimonialsStatsEnabled');
            if (testimonialsStatsEnabled) {
                testimonialsStatsEnabled.checked = currentSettings.testimonials_stats_enabled === '1' || currentSettings.testimonials_stats_enabled === true;
            }

            // Newsletter Section Settings
            const newsletterEnabled = document.getElementById('newsletterEnabled');
            if (newsletterEnabled) {
                newsletterEnabled.checked = currentSettings.newsletter_enabled === '1' || currentSettings.newsletter_enabled === true;
                toggleNewsletterSection(newsletterEnabled.checked);
            }

            const newsletterTitle = document.getElementById('newsletterTitle');
            if (newsletterTitle) newsletterTitle.value = currentSettings.newsletter_title || 'اشترك في نشرتنا الإخبارية';

            const newsletterSubtitle = document.getElementById('newsletterSubtitle');
            if (newsletterSubtitle) newsletterSubtitle.value = currentSettings.newsletter_subtitle || 'احصل على أحدث العروض والنصائح للعناية بالبشرة والشعر مباشرة في بريدك الإلكتروني';

            const newsletterBenefit1 = document.getElementById('newsletterBenefit1');
            if (newsletterBenefit1) newsletterBenefit1.value = currentSettings.newsletter_benefit_1 || 'عروض حصرية للمشتركين';

            const newsletterBenefit2 = document.getElementById('newsletterBenefit2');
            if (newsletterBenefit2) newsletterBenefit2.value = currentSettings.newsletter_benefit_2 || 'نصائح جمالية أسبوعية';

            const newsletterBenefit3 = document.getElementById('newsletterBenefit3');
            if (newsletterBenefit3) newsletterBenefit3.value = currentSettings.newsletter_benefit_3 || 'أول من يعرف المنتجات الجديدة';

            const newsletterPrivacyText = document.getElementById('newsletterPrivacyText');
            if (newsletterPrivacyText) newsletterPrivacyText.value = currentSettings.newsletter_privacy_text || 'نحن نحترم خصوصيتك ولن نشارك بريدك الإلكتروني مع أي طرف ثالث';

            const newsletterSubscribersCount = document.getElementById('newsletterSubscribersCount');
            if (newsletterSubscribersCount) newsletterSubscribersCount.value = currentSettings.newsletter_subscribers_count || '5,000+';

            const newsletterSatisfactionRate = document.getElementById('newsletterSatisfactionRate');
            if (newsletterSatisfactionRate) newsletterSatisfactionRate.value = currentSettings.newsletter_satisfaction_rate || '95%';

            const newsletterWeeklyTips = document.getElementById('newsletterWeeklyTips');
            if (newsletterWeeklyTips) newsletterWeeklyTips.value = currentSettings.newsletter_weekly_tips || '3';

            // Timeline Section Settings
            const timelineEnabled = document.getElementById('timelineEnabled');
            if (timelineEnabled) {
                timelineEnabled.checked = currentSettings.timeline_enabled === '1' || currentSettings.timeline_enabled === true;
                toggleTimelineSection(timelineEnabled.checked);
            }

            const timelineBadge = document.getElementById('timelineBadge');
            if (timelineBadge) timelineBadge.value = currentSettings.timeline_badge || 'رحلتنا';

            const timelineTitle = document.getElementById('timelineTitle');
            if (timelineTitle) timelineTitle.value = currentSettings.timeline_title || 'قصة نجاح متجر Care';

            const timelineSubtitle = document.getElementById('timelineSubtitle');
            if (timelineSubtitle) timelineSubtitle.value = currentSettings.timeline_subtitle || 'رحلة من الشغف إلى التميز في عالم الجمال والعناية';

            // Load timeline items
            for (let i = 1; i <= 6; i++) {
                const yearField = document.getElementById(`timeline${i}Year`);
                const titleField = document.getElementById(`timeline${i}Title`);
                const descriptionField = document.getElementById(`timeline${i}Description`);
                const iconField = document.getElementById(`timeline${i}Icon`);
                const enabledField = document.getElementById(`timeline${i}Enabled`);

                if (yearField) yearField.value = currentSettings[`timeline_${i}_year`] || '';
                if (titleField) titleField.value = currentSettings[`timeline_${i}_title`] || '';
                if (descriptionField) descriptionField.value = currentSettings[`timeline_${i}_description`] || '';
                if (iconField) iconField.value = currentSettings[`timeline_${i}_icon`] || '';
                if (enabledField) enabledField.checked = currentSettings[`timeline_${i}_enabled`] === '1' || currentSettings[`timeline_${i}_enabled`] === true;
            }

            // Load timeline stats toggle
            const timelineStatsEnabled = document.getElementById('timelineStatsEnabled');
            if (timelineStatsEnabled) {
                timelineStatsEnabled.checked = currentSettings.timeline_stats_enabled === '1' || currentSettings.timeline_stats_enabled === true;
            }

            // Load timeline stats
            const timelineStatYears = document.getElementById('timelineStatYears');
            if (timelineStatYears) timelineStatYears.value = currentSettings.timeline_stat_years || '6+';

            const timelineStatCustomers = document.getElementById('timelineStatCustomers');
            if (timelineStatCustomers) timelineStatCustomers.value = currentSettings.timeline_stat_customers || '5000+';

            const timelineStatProducts = document.getElementById('timelineStatProducts');
            if (timelineStatProducts) timelineStatProducts.value = currentSettings.timeline_stat_products || '500+';

            const timelineStatBrands = document.getElementById('timelineStatBrands');
            if (timelineStatBrands) timelineStatBrands.value = currentSettings.timeline_stat_brands || '50+';

            // Benefits Showcase Section Settings
            const benefitsShowcaseEnabled = document.getElementById('benefitsShowcaseEnabled');
            if (benefitsShowcaseEnabled) {
                benefitsShowcaseEnabled.checked = currentSettings.benefits_showcase_enabled === '1' || currentSettings.benefits_showcase_enabled === true;
                toggleBenefitsShowcaseSection(benefitsShowcaseEnabled.checked);
            }

            const benefitsShowcaseBadge = document.getElementById('benefitsShowcaseBadge');
            if (benefitsShowcaseBadge) benefitsShowcaseBadge.value = currentSettings.benefits_showcase_badge || 'فوائد منتجاتنا';

            const benefitsShowcaseTitle = document.getElementById('benefitsShowcaseTitle');
            if (benefitsShowcaseTitle) benefitsShowcaseTitle.value = currentSettings.benefits_showcase_title || 'اكتشف الفوائد المذهلة لمنتجاتنا';

            const benefitsShowcaseSubtitle = document.getElementById('benefitsShowcaseSubtitle');
            if (benefitsShowcaseSubtitle) benefitsShowcaseSubtitle.value = currentSettings.benefits_showcase_subtitle || 'منتجات مصممة خصيصاً لتلبية احتياجاتك وتحقيق أفضل النتائج';

            // Load benefit items (1-6)
            for (let i = 1; i <= 6; i++) {
                const iconField = document.getElementById(`benefit${i}Icon`);
                const titleField = document.getElementById(`benefit${i}Title`);
                const descriptionField = document.getElementById(`benefit${i}Description`);
                const detailsField = document.getElementById(`benefit${i}Details`);
                const statValueField = document.getElementById(`benefit${i}StatValue`);
                const statLabelField = document.getElementById(`benefit${i}StatLabel`);
                const enabledField = document.getElementById(`benefit${i}Enabled`);

                if (iconField) iconField.value = currentSettings[`benefit_${i}_icon`] || '';
                if (titleField) titleField.value = currentSettings[`benefit_${i}_title`] || '';
                if (descriptionField) descriptionField.value = currentSettings[`benefit_${i}_description`] || '';
                if (detailsField) detailsField.value = currentSettings[`benefit_${i}_details`] || '';
                if (statValueField) statValueField.value = currentSettings[`benefit_${i}_stat_value`] || '';
                if (statLabelField) statLabelField.value = currentSettings[`benefit_${i}_stat_label`] || '';

                // Handle enable/disable toggle for benefits 4-6
                if (enabledField && i >= 4) {
                    enabledField.checked = currentSettings[`benefit_${i}_enabled`] === '1' || currentSettings[`benefit_${i}_enabled`] === true;
                }
            }

            // Load benefits summary
            const benefitsSummaryTitle = document.getElementById('benefitsSummaryTitle');
            if (benefitsSummaryTitle) benefitsSummaryTitle.value = currentSettings.benefits_summary_title || 'لماذا تختار منتجاتنا؟';

            const benefitsSummaryDescription = document.getElementById('benefitsSummaryDescription');
            if (benefitsSummaryDescription) benefitsSummaryDescription.value = currentSettings.benefits_summary_description || 'نحن نؤمن بأن الجمال الحقيقي يأتي من منتجات عالية الجودة وآمنة';

            const benefitsSummaryStat1Value = document.getElementById('benefitsSummaryStat1Value');
            if (benefitsSummaryStat1Value) benefitsSummaryStat1Value.value = currentSettings.benefits_summary_stat1_value || '98%';

            const benefitsSummaryStat1Label = document.getElementById('benefitsSummaryStat1Label');
            if (benefitsSummaryStat1Label) benefitsSummaryStat1Label.value = currentSettings.benefits_summary_stat1_label || 'رضا العملاء';

            // Categories Section Settings
            const categoriesEnabled = document.getElementById('categoriesEnabled');
            if (categoriesEnabled) {
                categoriesEnabled.checked = currentSettings.categories_enabled === '1' || currentSettings.categories_enabled === true;
                toggleCategoriesSection(categoriesEnabled.checked);
            }

            const categoriesBadge = document.getElementById('categoriesBadge');
            if (categoriesBadge) categoriesBadge.value = currentSettings.categories_badge || 'تسوق حسب الفئة';

            const categoriesTitle = document.getElementById('categoriesTitle');
            if (categoriesTitle) categoriesTitle.value = currentSettings.categories_title || 'اكتشف مجموعاتنا المتنوعة';

            const categoriesSubtitle = document.getElementById('categoriesSubtitle');
            if (categoriesSubtitle) categoriesSubtitle.value = currentSettings.categories_subtitle || 'تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك';

            // Load all 4 categories
            for (let i = 1; i <= 4; i++) {
                const categoryEnabled = document.getElementById(`category${i}Enabled`);
                if (categoryEnabled) categoryEnabled.checked = currentSettings[`category_${i}_enabled`] === '1' || currentSettings[`category_${i}_enabled`] === true;

                const categoryName = document.getElementById(`category${i}Name`);
                if (categoryName) categoryName.value = currentSettings[`category_${i}_name`] || '';

                const categoryCount = document.getElementById(`category${i}Count`);
                if (categoryCount) categoryCount.value = currentSettings[`category_${i}_count`] || '';

                const categoryDescription = document.getElementById(`category${i}Description`);
                if (categoryDescription) categoryDescription.value = currentSettings[`category_${i}_description`] || '';

                const categoryImage = document.getElementById(`category${i}Image`);
                if (categoryImage) categoryImage.value = currentSettings[`category_${i}_image`] || '';
            }

            // Load dynamic testimonials
            loadTestimonials();

            console.log('✅ Forms population completed successfully');
        }

        // Save or update setting in database using reliable UPSERT operation
        async function saveSetting(key, value, group, retryCount = 0) {
            const maxRetries = 3;

            try {
                console.log(`🔄 Saving setting: ${key} = ${value} (group: ${group}) [Attempt ${retryCount + 1}]`);

                // Validate input
                if (!key || value === undefined || value === null) {
                    throw new Error('مفتاح الإعداد والقيمة مطلوبان');
                }

                // Get fresh client instance
                const client = getSupabaseClient();
                if (!client) {
                    throw new Error('لا يمكن الاتصال بقاعدة البيانات. يرجى إعادة تحميل الصفحة.');
                }

                // Use UPSERT operation to handle both new and existing records
                console.log(`💾 Upserting setting: ${key}`);
                const { data, error } = await client
                    .from('site_settings')
                    .upsert({
                        setting_key: key,
                        setting_value: value,
                        setting_group: group,
                        updated_at: new Date().toISOString()
                    }, {
                        onConflict: 'setting_key',
                        ignoreDuplicates: false
                    });

                // Handle operation result
                if (error) {
                    console.error(`❌ Error saving setting ${key}:`, error);

                    // Handle specific error types
                    if (error.code === '401' || error.message.includes('401')) {
                        throw new Error('انتهت صلاحية جلسة المدير. يرجى تسجيل الدخول مرة أخرى.');
                    } else if (error.code === '42501' ||
                               error.message.includes('permission') ||
                               error.message.includes('RLS') ||
                               error.message.includes('row-level security')) {

                        // For RLS errors, try alternative approach
                        console.warn(`⚠️ RLS permission error for ${key}, attempting alternative save...`);

                        // Store in localStorage as fallback for demo purposes
                        const fallbackKey = `site_setting_${key}`;
                        localStorage.setItem(fallbackKey, JSON.stringify({
                            setting_key: key,
                            setting_value: value,
                            setting_group: group,
                            updated_at: new Date().toISOString(),
                            source: 'localStorage_fallback'
                        }));

                        console.log(`✅ Setting ${key} saved to localStorage as fallback`);
                        return { success: true, fallback: true };

                    } else if (error.code === '23505' || error.message.includes('duplicate key')) {
                        // This should not happen with proper UPSERT, but handle it just in case
                        console.warn(`⚠️ Duplicate key error for ${key}, retrying with update...`);

                        // Fallback: try direct update with fresh client
                        const { error: updateError } = await client
                            .from('site_settings')
                            .update({
                                setting_value: value,
                                setting_group: group,
                                updated_at: new Date().toISOString()
                            })
                            .eq('setting_key', key);

                        if (updateError) {
                            throw new Error(`خطأ في تحديث الإعداد: ${updateError.message}`);
                        }
                    } else if (error.code === 'PGRST301' || error.message.includes('timeout')) {
                        // Handle timeout errors with retry
                        if (retryCount < maxRetries) {
                            console.log(`⏳ Timeout error, retrying... (${retryCount + 1}/${maxRetries})`);
                            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                            return await saveSetting(key, value, group, retryCount + 1);
                        } else {
                            throw new Error('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
                        }
                    } else {
                        throw new Error(`خطأ في حفظ الإعداد: ${error.message}`);
                    }
                }

                // Update local settings
                currentSettings[key] = value;
                console.log(`✅ Setting ${key} saved successfully`);

                // Check retry button status
                setTimeout(() => {
                    checkAndShowRetryButton();
                }, 500);

                return { success: true, fallback: false };

            } catch (error) {
                console.error('Error saving setting:', error);

                // Handle retry for network errors
                if ((error.message.includes('network') || error.message.includes('fetch')) && retryCount < maxRetries) {
                    console.log(`🔄 Network error, retrying... (${retryCount + 1}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                    return await saveSetting(key, value, group, retryCount + 1);
                }

                // Provide user-friendly error messages
                if (error.message.includes('انتهت صلاحية') || error.message.includes('Authentication')) {
                    throw error;
                } else if (error.message.includes('صلاحية') || error.message.includes('permission')) {
                    throw error;
                } else if (error.message.includes('لا يمكن الاتصال')) {
                    throw error;
                } else {
                    throw new Error(`حدث خطأ أثناء حفظ الإعداد: ${error.message}`);
                }
            }
        }

        // Handle contact form submission
        async function handleContactForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Validate required fields
            const businessName = formData.get('business_name');
            if (!businessName || businessName.trim() === '') {
                showMessage('يرجى إدخال اسم المتجر', 'error');
                return;
            }

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all contact settings
                await saveSetting('business_name', formData.get('business_name'), 'contact');
                await saveSetting('business_phone', formData.get('business_phone'), 'contact');
                await saveSetting('business_email', formData.get('business_email'), 'contact');
                await saveSetting('business_address', formData.get('business_address'), 'contact');
                await saveSetting('copyright_text', formData.get('copyright_text'), 'contact');

                // Show success notification
                showMessage('تم حفظ معلومات التواصل بنجاح!', 'success');

                // Trigger real-time update across the website
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving contact info:', error);
                showMessage('حدث خطأ أثناء حفظ معلومات التواصل', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ معلومات التواصل';
            }
        }

        // Handle hours form submission
        async function handleHoursForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all hours settings
                await saveSetting('working_days', formData.get('working_days'), 'hours');
                await saveSetting('working_hours', formData.get('working_hours'), 'hours');
                await saveSetting('closed_day', formData.get('closed_day'), 'hours');

                showMessage('تم حفظ أوقات العمل بنجاح!');

                // Trigger real-time update across the website
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving hours:', error);
                showMessage('حدث خطأ أثناء حفظ أوقات العمل', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ أوقات العمل';
            }
        }

        // Handle social media form submission
        async function handleSocialForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all social media settings
                await saveSetting('whatsapp_number', formData.get('whatsapp_number'), 'social');
                await saveSetting('facebook_url', formData.get('facebook_url'), 'social');
                await saveSetting('instagram_url', formData.get('instagram_url'), 'social');
                await saveSetting('twitter_url', formData.get('twitter_url'), 'social');
                await saveSetting('telegram_url', formData.get('telegram_url'), 'social');
                await saveSetting('linkedin_url', formData.get('linkedin_url'), 'social');
                await saveSetting('tiktok_url', formData.get('tiktok_url'), 'social');
                await saveSetting('youtube_url', formData.get('youtube_url'), 'social');
                await saveSetting('snapchat_url', formData.get('snapchat_url'), 'social');

                showMessage('تم حفظ روابط التواصل الاجتماعي بنجاح!');

                // Trigger real-time update across the website
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving social media:', error);
                showMessage('حدث خطأ أثناء حفظ روابط التواصل', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ روابط التواصل';
            }
        }









        // Handle page descriptions form submission
        async function handlePageDescriptionsForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                await saveSetting('products_page_description', formData.get('products_page_description'), 'pages');
                await saveSetting('offers_page_description', formData.get('offers_page_description'), 'pages');
                await saveSetting('contact_page_description', formData.get('contact_page_description'), 'pages');
                await saveSetting('faq_page_description', formData.get('faq_page_description'), 'pages');

                await saveSetting('guidelines_page_description', formData.get('guidelines_page_description'), 'pages');
                await saveSetting('terms_page_description', formData.get('terms_page_description'), 'pages');

                showMessage('تم حفظ نصوص الصفحات بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving page descriptions:', error);
                showMessage('حدث خطأ أثناء حفظ نصوص الصفحات', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ نصوص الصفحات';
            }
        }

        // Handle individual toggle changes for real-time updates
        async function handleHeaderBgToggle(pageName, isEnabled) {
            console.log(`🔄 Toggle changed for ${pageName}: ${isEnabled}`);

            try {
                // Save the toggle state immediately
                await saveSetting(`${pageName}_header_bg_enabled`, isEnabled ? 'true' : 'false', 'pages');

                // Get the current URL setting
                const urlInput = document.getElementById(`${pageName}HeaderBgUrl`);
                const bgUrl = urlInput ? urlInput.value : '';

                // Apply the background change immediately if URL exists
                if (isEnabled && bgUrl && bgUrl.trim() !== '') {
                    // Validate URL first
                    if (!isValidUrl(bgUrl)) {
                        throw new Error('رابط الصورة غير صحيح');
                    }

                    // Save URL as well
                    await saveSetting(`${pageName}_header_bg_url`, bgUrl, 'pages');
                }

                // Trigger real-time update
                triggerSiteUpdate();

                // Show success message
                const action = isEnabled ? 'تفعيل' : 'إلغاء تفعيل';
                showMessage(`تم ${action} خلفية رأس صفحة ${getPageDisplayName(pageName)} بنجاح!`);

            } catch (error) {
                console.error(`Error handling toggle for ${pageName}:`, error);
                showMessage(`حدث خطأ: ${error.message}`, 'error');

                // Revert toggle state on error
                const toggleInput = document.getElementById(`${pageName}HeaderBgEnabled`);
                if (toggleInput) {
                    toggleInput.checked = !isEnabled;
                }
            }
        }

        // Handle page header background form submissions
        async function handleHeaderBgForm(event, pageName) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Validate URL if provided
                const bgUrl = formData.get(`${pageName}_header_bg_url`);
                if (bgUrl && !isValidUrl(bgUrl)) {
                    throw new Error('رابط الصورة غير صحيح');
                }

                await saveSetting(`${pageName}_header_bg_enabled`, formData.get(`${pageName}_header_bg_enabled`) === 'on' ? 'true' : 'false', 'pages');
                await saveSetting(`${pageName}_header_bg_url`, bgUrl || '', 'pages');

                showMessage(`تم حفظ خلفية رأس صفحة ${getPageDisplayName(pageName)} بنجاح!`);
                triggerSiteUpdate();

            } catch (error) {
                console.error(`Error saving ${pageName} header background:`, error);
                showMessage(`حدث خطأ أثناء حفظ خلفية رأس الصفحة: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // Helper function to validate URLs
        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        // Helper function to get page display names
        function getPageDisplayName(pageName) {
            const displayNames = {
                'products': 'المنتجات',
                'contact': 'اتصل بنا',
                'faq': 'الأسئلة الشائعة',
                'guidelines': 'الإرشادات',
                'terms': 'الشروط والأحكام',
                'offers': 'العروض'
            };
            return displayNames[pageName] || pageName;
        }



        // Function to manually refresh toggle states from database
        async function refreshToggleStates() {
            console.log('🔄 Refreshing toggle states from database...');
            try {
                await loadSettings();
                showMessage('تم تحديث حالة المفاتيح بنجاح!');
            } catch (error) {
                console.error('Error refreshing toggle states:', error);
                showMessage('حدث خطأ أثناء تحديث حالة المفاتيح', 'error');
            }
        }





        // Reset all toggles to false
        function resetAllToggles() {
            console.log('🔄 Resetting all toggles...');
            const pages = ['products', 'contact', 'faq', 'guidelines', 'terms', 'offers'];

            pages.forEach(page => {
                const toggleId = `${page}HeaderBgEnabled`;
                const toggle = document.getElementById(toggleId);
                if (toggle) {
                    toggle.checked = false;
                    toggle.dispatchEvent(new Event('change'));
                }
            });
        }





        // Handle hero section form submission
        async function handleHeroSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all hero section settings
                await saveSetting('hero_title_line1', formData.get('hero_title_line1'), 'homepage');
                await saveSetting('hero_title_line2', formData.get('hero_title_line2'), 'homepage');
                await saveSetting('hero_subtitle', formData.get('hero_subtitle'), 'homepage');
                await saveSetting('trust_indicator_1', formData.get('trust_indicator_1'), 'homepage');
                await saveSetting('trust_indicator_1_icon', formData.get('trust_indicator_1_icon'), 'homepage');
                await saveSetting('trust_indicator_2', formData.get('trust_indicator_2'), 'homepage');
                await saveSetting('trust_indicator_2_icon', formData.get('trust_indicator_2_icon'), 'homepage');
                await saveSetting('trust_indicator_3', formData.get('trust_indicator_3'), 'homepage');
                await saveSetting('trust_indicator_3_icon', formData.get('trust_indicator_3_icon'), 'homepage');
                await saveSetting('featured_product_name', formData.get('featured_product_name'), 'homepage');
                await saveSetting('featured_product_price', formData.get('featured_product_price'), 'homepage');
                await saveSetting('featured_product_original_price', formData.get('featured_product_original_price'), 'homepage');

                // Save hero background settings
                await saveSetting('hero_background_enabled', formData.get('hero_background_enabled') ? '1' : '0', 'homepage');
                await saveSetting('hero_background_url', formData.get('hero_background_url') || '', 'homepage');

                // Save hero showcase settings
                await saveSetting('hero_showcase_enabled', formData.get('hero_showcase_enabled') ? '1' : '0', 'homepage');
                await saveSetting('hero_showcase_url', formData.get('hero_showcase_url') || '', 'homepage');

                // Save trust indicators toggle
                const trustIndicatorsEnabled = document.getElementById('trustIndicatorsEnabled');
                if (trustIndicatorsEnabled) {
                    await saveSetting('trust_indicators_enabled', trustIndicatorsEnabled.checked ? '1' : '0', 'homepage');
                }

                // Save featured product toggle
                const featuredProductEnabled = document.getElementById('featuredProductEnabled');
                if (featuredProductEnabled) {
                    await saveSetting('featured_product_enabled', featuredProductEnabled.checked ? '1' : '0', 'homepage');
                }

                showMessage('تم حفظ إعدادات البانر الرئيسي بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving hero section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات البانر الرئيسي', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات البانر الرئيسي';
            }
        }

        // Handle featured products section form submission
        async function handleFeaturedProductsSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all featured products section settings
                await saveSetting('featured_products_badge', formData.get('featured_products_badge'), 'homepage');
                await saveSetting('featured_products_title', formData.get('featured_products_title'), 'homepage');
                await saveSetting('featured_products_subtitle', formData.get('featured_products_subtitle'), 'homepage');

                showMessage('تم حفظ إعدادات المنتجات المميزة بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving featured products section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات المنتجات المميزة', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات المنتجات المميزة';
            }
        }

        // Handle features section form submission
        async function handleFeaturesSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all features section settings
                await saveSetting('features_badge', formData.get('features_badge'), 'homepage');
                await saveSetting('features_title', formData.get('features_title'), 'homepage');
                await saveSetting('features_subtitle', formData.get('features_subtitle'), 'homepage');

                // Save individual features (title, description, and icon for each)
                await saveSetting('feature_1_title', formData.get('feature_1_title'), 'homepage');
                await saveSetting('feature_1_description', formData.get('feature_1_description'), 'homepage');
                await saveSetting('feature_1_icon', formData.get('feature_1_icon'), 'homepage');
                await saveSetting('feature_2_title', formData.get('feature_2_title'), 'homepage');
                await saveSetting('feature_2_description', formData.get('feature_2_description'), 'homepage');
                await saveSetting('feature_2_icon', formData.get('feature_2_icon'), 'homepage');
                await saveSetting('feature_3_title', formData.get('feature_3_title'), 'homepage');
                await saveSetting('feature_3_description', formData.get('feature_3_description'), 'homepage');
                await saveSetting('feature_3_icon', formData.get('feature_3_icon'), 'homepage');
                await saveSetting('feature_4_title', formData.get('feature_4_title'), 'homepage');
                await saveSetting('feature_4_description', formData.get('feature_4_description'), 'homepage');
                await saveSetting('feature_4_icon', formData.get('feature_4_icon'), 'homepage');
                await saveSetting('feature_5_title', formData.get('feature_5_title'), 'homepage');
                await saveSetting('feature_5_description', formData.get('feature_5_description'), 'homepage');
                await saveSetting('feature_5_icon', formData.get('feature_5_icon'), 'homepage');
                await saveSetting('feature_6_title', formData.get('feature_6_title'), 'homepage');
                await saveSetting('feature_6_description', formData.get('feature_6_description'), 'homepage');
                await saveSetting('feature_6_icon', formData.get('feature_6_icon'), 'homepage');

                // Save individual feature toggles
                for (let i = 1; i <= 6; i++) {
                    const featureEnabled = document.getElementById(`feature${i}Enabled`);
                    if (featureEnabled) {
                        await saveSetting(`feature_${i}_enabled`, featureEnabled.checked ? '1' : '0', 'homepage');
                    }
                }

                showMessage('تم حفظ إعدادات الميزات والخدمات بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving features section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات الميزات والخدمات', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات الميزات والخدمات';
            }
        }

        // Handle about section form submission
        async function handleAboutSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all about section settings
                await saveSetting('about_badge', formData.get('about_badge'), 'homepage');
                await saveSetting('about_title', formData.get('about_title'), 'homepage');
                await saveSetting('about_subtitle', formData.get('about_subtitle'), 'homepage');
                await saveSetting('about_description_1', formData.get('about_description_1'), 'homepage');
                await saveSetting('about_description_2', formData.get('about_description_2'), 'homepage');

                showMessage('تم حفظ إعدادات قسم "من نحن" بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving about section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات قسم "من نحن"', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات قسم "من نحن"';
            }
        }

        // Handle stats section form submission
        async function handleStatsSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save all stats section settings
                await saveSetting('stat_customers', formData.get('stat_customers'), 'homepage');
                await saveSetting('stat_orders', formData.get('stat_orders'), 'homepage');
                await saveSetting('stat_years', formData.get('stat_years'), 'homepage');
                await saveSetting('stat_rating', formData.get('stat_rating'), 'homepage');

                showMessage('تم حفظ الإحصائيات بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving stats section:', error);
                showMessage('حدث خطأ أثناء حفظ الإحصائيات', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الإحصائيات';
            }
        }

        // Handle testimonials section form submission
        async function handleTestimonialsSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save testimonials section settings
                await saveSetting('testimonials_badge', formData.get('testimonials_badge'), 'homepage');
                await saveSetting('testimonials_title', formData.get('testimonials_title'), 'homepage');
                await saveSetting('testimonials_subtitle', formData.get('testimonials_subtitle'), 'homepage');

                // Clear existing testimonials in database first
                let testimonialIndex = 1;
                while (currentSettings[`testimonial_${testimonialIndex}_name`]) {
                    await saveSetting(`testimonial_${testimonialIndex}_name`, '', 'homepage');
                    await saveSetting(`testimonial_${testimonialIndex}_location`, '', 'homepage');
                    await saveSetting(`testimonial_${testimonialIndex}_initial`, '', 'homepage');
                    await saveSetting(`testimonial_${testimonialIndex}_rating`, '', 'homepage');
                    await saveSetting(`testimonial_${testimonialIndex}_text`, '', 'homepage');
                    await saveSetting(`testimonial_${testimonialIndex}_image`, '', 'homepage');
                    testimonialIndex++;
                }

                // Save all dynamic testimonials
                testimonialIndex = 1;
                for (const [key, value] of formData.entries()) {
                    if (key.startsWith('testimonial_') && key.includes('_name')) {
                        const testimonialNum = key.match(/testimonial_(\d+)_name/)[1];

                        await saveSetting(`testimonial_${testimonialIndex}_name`, formData.get(`testimonial_${testimonialNum}_name`), 'homepage');
                        await saveSetting(`testimonial_${testimonialIndex}_location`, formData.get(`testimonial_${testimonialNum}_location`), 'homepage');
                        await saveSetting(`testimonial_${testimonialIndex}_initial`, formData.get(`testimonial_${testimonialNum}_initial`), 'homepage');
                        await saveSetting(`testimonial_${testimonialIndex}_rating`, formData.get(`testimonial_${testimonialNum}_rating`), 'homepage');
                        await saveSetting(`testimonial_${testimonialIndex}_text`, formData.get(`testimonial_${testimonialNum}_text`), 'homepage');
                        await saveSetting(`testimonial_${testimonialIndex}_image`, formData.get(`testimonial_${testimonialNum}_image`) || '', 'homepage');

                        testimonialIndex++;
                    }
                }

                // Save testimonials statistics
                await saveSetting('testimonials_total_reviews', formData.get('testimonials_total_reviews'), 'homepage');
                await saveSetting('testimonials_average_rating', formData.get('testimonials_average_rating'), 'homepage');
                await saveSetting('testimonials_satisfaction', formData.get('testimonials_satisfaction'), 'homepage');

                // Save testimonials stats toggle
                const testimonialsStatsEnabled = document.getElementById('testimonialsStatsEnabled');
                if (testimonialsStatsEnabled) {
                    await saveSetting('testimonials_stats_enabled', testimonialsStatsEnabled.checked ? '1' : '0', 'homepage');
                }

                // Check if any settings were saved as fallback
                const hasFallbackSaves = await checkForFallbackSaves();
                if (hasFallbackSaves) {
                    showMessage('تم حفظ إعدادات آراء العملاء مؤقتاً. قد تحتاج إلى إعادة تحميل الصفحة لرؤية جميع التغييرات.', 'warning');
                } else {
                    showMessage('تم حفظ إعدادات آراء العملاء بنجاح!');
                }
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving testimonials section:', error);

                // Show detailed error message
                let errorMessage = 'حدث خطأ أثناء حفظ إعدادات آراء العملاء';
                if (error.message.includes('انتهت صلاحية')) {
                    errorMessage = 'انتهت صلاحية الجلسة. يرجى إعادة تحميل الصفحة وتسجيل الدخول مرة أخرى.';
                } else if (error.message.includes('لا يمكن الاتصال')) {
                    errorMessage = 'مشكلة في الاتصال بقاعدة البيانات. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.';
                } else if (error.message.includes('صلاحية')) {
                    errorMessage = 'ليس لديك صلاحية لحفظ هذه الإعدادات. يرجى التحقق من صلاحيات المدير.';
                }

                showMessage(errorMessage, 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات آراء العملاء';
            }
        }

        // Handle newsletter section form submission
        async function handleNewsletterSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save newsletter section settings
                await saveSetting('newsletter_title', formData.get('newsletter_title'), 'homepage');
                await saveSetting('newsletter_subtitle', formData.get('newsletter_subtitle'), 'homepage');
                await saveSetting('newsletter_benefit_1', formData.get('newsletter_benefit_1'), 'homepage');
                await saveSetting('newsletter_benefit_2', formData.get('newsletter_benefit_2'), 'homepage');
                await saveSetting('newsletter_benefit_3', formData.get('newsletter_benefit_3'), 'homepage');
                await saveSetting('newsletter_privacy_text', formData.get('newsletter_privacy_text'), 'homepage');
                await saveSetting('newsletter_subscribers_count', formData.get('newsletter_subscribers_count'), 'homepage');
                await saveSetting('newsletter_satisfaction_rate', formData.get('newsletter_satisfaction_rate'), 'homepage');
                await saveSetting('newsletter_weekly_tips', formData.get('newsletter_weekly_tips'), 'homepage');

                showMessage('تم حفظ إعدادات النشرة الإخبارية بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving newsletter section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات النشرة الإخبارية', 'error');
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات النشرة الإخبارية';
            }
        }

        // Handle timeline section form submission
        async function handleTimelineSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save general section settings
                await saveSetting('timeline_badge', formData.get('timeline_badge'), 'homepage');
                await saveSetting('timeline_title', formData.get('timeline_title'), 'homepage');
                await saveSetting('timeline_subtitle', formData.get('timeline_subtitle'), 'homepage');

                // Save all 6 timeline items
                for (let i = 1; i <= 6; i++) {
                    await saveSetting(`timeline_${i}_year`, formData.get(`timeline_${i}_year`), 'homepage');
                    await saveSetting(`timeline_${i}_title`, formData.get(`timeline_${i}_title`), 'homepage');
                    await saveSetting(`timeline_${i}_description`, formData.get(`timeline_${i}_description`), 'homepage');
                    await saveSetting(`timeline_${i}_icon`, formData.get(`timeline_${i}_icon`), 'homepage');

                    // Save timeline item enabled toggle
                    const timelineEnabledField = document.getElementById(`timeline${i}Enabled`);
                    if (timelineEnabledField) {
                        await saveSetting(`timeline_${i}_enabled`, timelineEnabledField.checked ? '1' : '0', 'homepage');
                    }
                }

                // Save timeline stats
                await saveSetting('timeline_stat_years', formData.get('timeline_stat_years'), 'homepage');
                await saveSetting('timeline_stat_customers', formData.get('timeline_stat_customers'), 'homepage');
                await saveSetting('timeline_stat_products', formData.get('timeline_stat_products'), 'homepage');
                await saveSetting('timeline_stat_brands', formData.get('timeline_stat_brands'), 'homepage');

                // Save timeline stats toggle
                const timelineStatsEnabled = document.getElementById('timelineStatsEnabled');
                if (timelineStatsEnabled) {
                    await saveSetting('timeline_stats_enabled', timelineStatsEnabled.checked ? '1' : '0', 'homepage');
                }

                showMessage('تم حفظ إعدادات الخط الزمني بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving timeline section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات الخط الزمني', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات الخط الزمني';
            }
        }

        // Handle benefits showcase section form submission
        async function handleBenefitsShowcaseSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save general section settings
                await saveSetting('benefits_showcase_badge', formData.get('benefits_showcase_badge'), 'homepage');
                await saveSetting('benefits_showcase_title', formData.get('benefits_showcase_title'), 'homepage');
                await saveSetting('benefits_showcase_subtitle', formData.get('benefits_showcase_subtitle'), 'homepage');

                // Save benefit items (1-6)
                for (let i = 1; i <= 6; i++) {
                    await saveSetting(`benefit_${i}_icon`, formData.get(`benefit_${i}_icon`), 'homepage');
                    await saveSetting(`benefit_${i}_title`, formData.get(`benefit_${i}_title`), 'homepage');
                    await saveSetting(`benefit_${i}_description`, formData.get(`benefit_${i}_description`), 'homepage');
                    await saveSetting(`benefit_${i}_details`, formData.get(`benefit_${i}_details`), 'homepage');
                    await saveSetting(`benefit_${i}_stat_value`, formData.get(`benefit_${i}_stat_value`), 'homepage');
                    await saveSetting(`benefit_${i}_stat_label`, formData.get(`benefit_${i}_stat_label`), 'homepage');

                    // Save enable/disable toggle for benefits 4-6
                    if (i >= 4) {
                        const enabledCheckbox = document.getElementById(`benefit${i}Enabled`);
                        if (enabledCheckbox) {
                            await saveSetting(`benefit_${i}_enabled`, enabledCheckbox.checked ? '1' : '0', 'homepage');
                        }
                    }
                }

                // Save benefits summary
                await saveSetting('benefits_summary_title', formData.get('benefits_summary_title'), 'homepage');
                await saveSetting('benefits_summary_description', formData.get('benefits_summary_description'), 'homepage');
                await saveSetting('benefits_summary_stat1_value', formData.get('benefits_summary_stat1_value'), 'homepage');
                await saveSetting('benefits_summary_stat1_label', formData.get('benefits_summary_stat1_label'), 'homepage');

                showMessage('تم حفظ إعدادات عرض الفوائد التفاعلي بنجاح!');
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving benefits showcase section:', error);
                showMessage('حدث خطأ أثناء حفظ إعدادات عرض الفوائد التفاعلي', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات عرض الفوائد التفاعلي';
            }
        }

        // Handle categories section form submission
        async function handleCategoriesSectionForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = event.target.querySelector('button[type="submit"]');

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                // Save general section settings
                await saveSetting('categories_badge', formData.get('categories_badge'), 'homepage');
                await saveSetting('categories_title', formData.get('categories_title'), 'homepage');
                await saveSetting('categories_subtitle', formData.get('categories_subtitle'), 'homepage');

                // Save all 4 categories with their new fields
                for (let i = 1; i <= 4; i++) {
                    await saveSetting(`category_${i}_enabled`, formData.get(`category_${i}_enabled`) ? '1' : '0', 'homepage');
                    await saveSetting(`category_${i}_name`, formData.get(`category_${i}_name`), 'homepage');
                    await saveSetting(`category_${i}_description`, formData.get(`category_${i}_description`), 'homepage');
                    await saveSetting(`category_${i}_count`, formData.get(`category_${i}_count`), 'homepage');
                    await saveSetting(`category_${i}_image`, formData.get(`category_${i}_image`), 'homepage');
                }

                // Check if any settings were saved as fallback
                const hasFallbackSaves = await checkForFallbackSaves();
                if (hasFallbackSaves) {
                    showMessage('تم حفظ إعدادات فئات المنتجات مؤقتاً. قد تحتاج إلى إعادة تحميل الصفحة لرؤية جميع التغييرات.', 'warning');
                } else {
                    showMessage('تم حفظ إعدادات فئات المنتجات بنجاح!');
                }
                triggerSiteUpdate();

            } catch (error) {
                console.error('Error saving categories section:', error);

                // Show detailed error message
                let errorMessage = 'حدث خطأ أثناء حفظ إعدادات فئات المنتجات';
                if (error.message.includes('انتهت صلاحية')) {
                    errorMessage = 'انتهت صلاحية الجلسة. يرجى إعادة تحميل الصفحة وتسجيل الدخول مرة أخرى.';
                } else if (error.message.includes('لا يمكن الاتصال')) {
                    errorMessage = 'مشكلة في الاتصال بقاعدة البيانات. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.';
                } else if (error.message.includes('صلاحية')) {
                    errorMessage = 'ليس لديك صلاحية لحفظ هذه الإعدادات. يرجى التحقق من صلاحيات المدير.';
                } else if (error.message.includes('fallback')) {
                    errorMessage = 'تم حفظ الإعدادات مؤقتاً. قد تحتاج إلى إعادة تحميل الصفحة لرؤية التغييرات.';
                }

                showMessage(errorMessage, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ إعدادات فئات المنتجات';
            }
        }

        // Refresh current settings from database
        async function refreshCurrentSettings() {
            try {
                console.log('🔄 Refreshing current settings...');
                await loadSettings();
                console.log('✅ Current settings refreshed');
            } catch (error) {
                console.error('Error refreshing current settings:', error);
            }
        }

        // Trigger real-time update across the website
        async function triggerSiteUpdate() {
            console.log('🔄 Triggering site update...');

            // First, refresh the current settings to ensure we have the latest data
            await refreshCurrentSettings();

            // Dispatch custom event that other pages can listen to
            window.dispatchEvent(new CustomEvent('siteSettingsUpdated', {
                detail: currentSettings
            }));

            // Dispatch admin-specific event
            window.dispatchEvent(new CustomEvent('adminSettingsChanged', {
                detail: {
                    settings: currentSettings,
                    timestamp: Date.now()
                }
            }));

            // Also trigger a storage event for cross-tab communication
            localStorage.setItem('siteSettingsLastUpdate', Date.now().toString());

            // Try to notify parent window if in iframe
            try {
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'siteSettingsUpdated',
                        settings: currentSettings
                    }, '*');
                }
            } catch (e) {
                // Ignore cross-origin errors
            }

            console.log('✅ Site update triggered successfully');

            // Try to update any open homepage tabs
            try {
                // Send message to all windows/tabs
                const bc = new BroadcastChannel('site-settings-updates');
                bc.postMessage({
                    type: 'settingsUpdated',
                    settings: currentSettings,
                    timestamp: Date.now()
                });
                bc.close();
            } catch (e) {
                console.log('BroadcastChannel not supported, using localStorage fallback');
            }
        }



        // Hero Background Image Toggle Functions
        function toggleHeroBackgroundField(showBackground) {
            const backgroundUrlGroup = document.getElementById('heroBackgroundUrlGroup');
            const backgroundPreview = document.getElementById('heroBackgroundPreview');

            if (showBackground) {
                backgroundUrlGroup.classList.remove('hidden');
            } else {
                backgroundUrlGroup.classList.add('hidden');
                backgroundPreview.classList.add('hidden');
            }
        }

        function updateHeroBackgroundPreview(url) {
            const preview = document.getElementById('heroBackgroundPreview');
            const img = document.getElementById('heroBackgroundPreviewImg');

            if (url && url.trim()) {
                img.src = url;
                img.onload = function() {
                    preview.classList.remove('hidden');
                    preview.classList.add('visible');
                };
                img.onerror = function() {
                    preview.classList.add('hidden');
                    preview.classList.remove('visible');
                    showMessage('لا يمكن تحميل صورة الخلفية من الرابط المحدد', 'error');
                };
            } else {
                preview.classList.add('hidden');
                preview.classList.remove('visible');
            }
        }

        // Hero Showcase Image Toggle Functions
        function toggleHeroShowcaseField(showShowcase) {
            const showcaseUrlGroup = document.getElementById('heroShowcaseUrlGroup');
            const showcasePreview = document.getElementById('heroShowcasePreview');

            if (showShowcase) {
                showcaseUrlGroup.classList.remove('hidden');
            } else {
                showcaseUrlGroup.classList.add('hidden');
                showcasePreview.classList.add('hidden');
            }
        }

        function updateHeroShowcasePreview(url) {
            const preview = document.getElementById('heroShowcasePreview');
            const img = document.getElementById('heroShowcasePreviewImg');

            if (url && url.trim()) {
                img.src = url;
                img.onload = function() {
                    preview.classList.remove('hidden');
                    preview.classList.add('visible');
                };
                img.onerror = function() {
                    preview.classList.add('hidden');
                    preview.classList.remove('visible');
                    showMessage('لا يمكن تحميل صورة المنتج من الرابط المحدد', 'error');
                };
            } else {
                preview.classList.add('hidden');
                preview.classList.remove('visible');
            }
        }

        // Testimonials Section Toggle Function
        function toggleTestimonialsSection(enabled) {
            const testimonialsSection = document.getElementById('testimonials-section');
            if (testimonialsSection) {
                if (enabled) {
                    testimonialsSection.classList.remove('hidden');
                    testimonialsSection.classList.add('visible');
                } else {
                    testimonialsSection.classList.add('hidden');
                    testimonialsSection.classList.remove('visible');
                }
            }
        }

        // Newsletter Section Toggle Function
        function toggleNewsletterSection(enabled) {
            const newsletterSection = document.getElementById('newsletter-section');
            if (newsletterSection) {
                if (enabled) {
                    newsletterSection.classList.remove('hidden');
                    newsletterSection.classList.add('visible');
                } else {
                    newsletterSection.classList.add('hidden');
                    newsletterSection.classList.remove('visible');
                }
            }
        }

        // Timeline Section Toggle Function
        function toggleTimelineSection(enabled) {
            const timelineSection = document.getElementById('timeline-section');
            if (timelineSection) {
                if (enabled) {
                    timelineSection.classList.remove('hidden');
                    timelineSection.classList.add('visible');
                } else {
                    timelineSection.classList.add('hidden');
                    timelineSection.classList.remove('visible');
                }
            }
        }

        // Benefits Showcase Section Toggle Function
        function toggleBenefitsShowcaseSection(enabled) {
            const benefitsShowcaseSection = document.getElementById('benefits-showcase-section');
            if (benefitsShowcaseSection) {
                if (enabled) {
                    benefitsShowcaseSection.classList.remove('hidden');
                    benefitsShowcaseSection.classList.add('visible');
                } else {
                    benefitsShowcaseSection.classList.add('hidden');
                    benefitsShowcaseSection.classList.remove('visible');
                }
            }
        }

        // Categories Section Toggle Function
        function toggleCategoriesSection(enabled) {
            const categoriesSection = document.getElementById('categories-section');
            if (categoriesSection) {
                if (enabled) {
                    categoriesSection.classList.remove('hidden');
                    categoriesSection.classList.add('visible');
                } else {
                    categoriesSection.classList.add('hidden');
                    categoriesSection.classList.remove('visible');
                }
            }
        }

        // Initialize Collapsible Sections
        function initializeCollapsibleSections() {
            // Find the first collapsible section and expand it by default
            const firstCollapsibleSection = document.querySelector('.collapsible-section');
            if (firstCollapsibleSection) {
                const firstContent = firstCollapsibleSection.querySelector('.section-content');
                const firstIcon = firstCollapsibleSection.querySelector('.section-toggle-icon');
                if (firstContent && firstIcon) {
                    firstContent.classList.add('expanded');
                    firstIcon.classList.add('expanded');
                }
            }
        }

        // Collapsible Section Toggle Function
        function toggleSection(headerElement) {
            const section = headerElement.parentElement;
            const content = section.querySelector('.section-content');
            const icon = headerElement.querySelector('.section-toggle-icon');

            if (!content || !icon) return;

            const isExpanded = content.classList.contains('expanded');

            // Close all other sections (accordion behavior)
            const allSections = document.querySelectorAll('.form-section');
            allSections.forEach(otherSection => {
                if (otherSection !== section) {
                    const otherContent = otherSection.querySelector('.section-content');
                    const otherIcon = otherSection.querySelector('.section-toggle-icon');
                    if (otherContent && otherIcon) {
                        otherContent.classList.remove('expanded');
                        otherIcon.classList.remove('expanded');
                    }
                }
            });

            // Toggle current section
            if (isExpanded) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }

        // Mobile sidebar toggle - now handled by unified-sidebar-manager.js

        // Production-ready authentication system - test functions removed

        // Simple test session function for development


        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Site Settings page loading...');

            if (checkAuth()) {
                console.log('Authentication successful, initializing page...');

                // Initialize collapsible sections (expand first section by default)
                initializeCollapsibleSections();

                // Load current settings
                try {
                    loadSettings();
                } catch (error) {
                    console.error('Error loading settings:', error);
                }

                // Add form event listeners with error handling
                try {
                    const forms = [
                        { id: 'contactForm', handler: handleContactForm },
                        { id: 'hoursForm', handler: handleHoursForm },
                        { id: 'socialForm', handler: handleSocialForm },

                        { id: 'pageDescriptionsForm', handler: handlePageDescriptionsForm },

                        // Page Header Background forms
                        { id: 'productsHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'products') },
                        { id: 'contactHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'contact') },
                        { id: 'faqHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'faq') },
                        { id: 'guidelinesHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'guidelines') },
                        { id: 'termsHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'terms') },
                        { id: 'offersHeaderBgForm', handler: (e) => handleHeaderBgForm(e, 'offers') },

                        // Homepage section forms
                        { id: 'heroSectionForm', handler: handleHeroSectionForm },
                        { id: 'featuredProductsSectionForm', handler: handleFeaturedProductsSectionForm },
                        { id: 'featuresSectionForm', handler: handleFeaturesSectionForm },
                        { id: 'aboutSectionForm', handler: handleAboutSectionForm },
                        { id: 'statsSectionForm', handler: handleStatsSectionForm },
                        { id: 'testimonialsSectionForm', handler: handleTestimonialsSectionForm },
                        { id: 'newsletterSectionForm', handler: handleNewsletterSectionForm },
                        { id: 'timelineSectionForm', handler: handleTimelineSectionForm },
                        { id: 'benefitsShowcaseSectionForm', handler: handleBenefitsShowcaseSectionForm },
                        { id: 'categoriesSectionForm', handler: handleCategoriesSectionForm }
                    ];

                    forms.forEach(({ id, handler }) => {
                        const form = document.getElementById(id);
                        if (form) {
                            form.addEventListener('submit', handler);
                        } else {
                            console.warn(`Form with ID '${id}' not found`);
                        }
                    });

                    // Set up individual toggle listeners for real-time updates
                    const toggles = [
                        { id: 'productsHeaderBgEnabled', page: 'products' },
                        { id: 'contactHeaderBgEnabled', page: 'contact' },
                        { id: 'faqHeaderBgEnabled', page: 'faq' },
                        { id: 'guidelinesHeaderBgEnabled', page: 'guidelines' },
                        { id: 'termsHeaderBgEnabled', page: 'terms' },
                        { id: 'offersHeaderBgEnabled', page: 'offers' }
                    ];

                    toggles.forEach(({ id, page }) => {
                        const toggleElement = document.getElementById(id);
                        if (toggleElement) {
                            // Add change listener to the checkbox
                            toggleElement.addEventListener('change', function() {
                                console.log(`Toggle ${id} changed to: ${this.checked}`);
                                handleHeaderBgToggle(page, this.checked);
                            });

                            // Add click listener to the toggle slider (the visible part)
                            const toggleSlider = toggleElement.nextElementSibling;
                            if (toggleSlider && toggleSlider.classList.contains('toggle-slider')) {
                                toggleSlider.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log(`🖱️ Toggle slider clicked for ${id}`);

                                    // Toggle the checkbox state
                                    toggleElement.checked = !toggleElement.checked;

                                    // Trigger the change event manually
                                    const changeEvent = new Event('change', { bubbles: true });
                                    toggleElement.dispatchEvent(changeEvent);
                                });
                                console.log(`✅ Toggle slider click listener added for ${id}`);
                            } else {
                                console.warn(`⚠️ Toggle slider not found for ${id}`);
                            }

                            // Also add click listener to the toggle label
                            const toggleLabel = toggleSlider ? toggleSlider.nextElementSibling : null;
                            if (toggleLabel && toggleLabel.classList.contains('toggle-label')) {
                                toggleLabel.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log(`🏷️ Toggle label clicked for ${id}`);

                                    // Toggle the checkbox state
                                    toggleElement.checked = !toggleElement.checked;

                                    // Trigger the change event manually
                                    const changeEvent = new Event('change', { bubbles: true });
                                    toggleElement.dispatchEvent(changeEvent);
                                });
                                console.log(`✅ Toggle label click listener added for ${id}`);
                            }

                            console.log(`✅ Toggle listeners added for ${id}`);
                        } else {
                            console.warn(`Toggle with ID '${id}' not found`);
                        }
                    });



                } catch (error) {
                    console.error('Error setting up form listeners:', error);
                }



                    // Hero background toggle event listeners
                    const heroBackgroundCheckbox = document.getElementById('heroBackgroundEnabled');
                    if (heroBackgroundCheckbox) {
                        heroBackgroundCheckbox.addEventListener('change', function() {
                            toggleHeroBackgroundField(this.checked);
                        });
                    }

                    // Hero background URL input change listener for preview
                    const heroBackgroundUrlInput = document.getElementById('heroBackgroundUrl');
                    if (heroBackgroundUrlInput) {
                        heroBackgroundUrlInput.addEventListener('input', function() {
                            updateHeroBackgroundPreview(this.value);
                        });
                    }

                    // Hero showcase toggle event listeners
                    const heroShowcaseCheckbox = document.getElementById('heroShowcaseEnabled');
                    if (heroShowcaseCheckbox) {
                        heroShowcaseCheckbox.addEventListener('change', function() {
                            toggleHeroShowcaseField(this.checked);
                        });
                    }

                    // Hero showcase URL input change listener for preview
                    const heroShowcaseUrlInput = document.getElementById('heroShowcaseUrl');
                    if (heroShowcaseUrlInput) {
                        heroShowcaseUrlInput.addEventListener('input', function() {
                            updateHeroShowcasePreview(this.value);
                        });
                    }

                    // Testimonials section toggle event listener
                    const testimonialsCheckbox = document.getElementById('testimonialsEnabled');
                    if (testimonialsCheckbox) {
                        testimonialsCheckbox.addEventListener('change', function() {
                            toggleTestimonialsSection(this.checked);
                            // Save the setting immediately
                            saveSetting('testimonials_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Newsletter section toggle event listener
                    const newsletterCheckbox = document.getElementById('newsletterEnabled');
                    if (newsletterCheckbox) {
                        newsletterCheckbox.addEventListener('change', function() {
                            toggleNewsletterSection(this.checked);
                            // Save the setting immediately
                            saveSetting('newsletter_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Timeline section toggle event listener
                    const timelineCheckbox = document.getElementById('timelineEnabled');
                    if (timelineCheckbox) {
                        timelineCheckbox.addEventListener('change', function() {
                            toggleTimelineSection(this.checked);
                            // Save the setting immediately
                            saveSetting('timeline_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Timeline individual items toggle event listeners
                    for (let i = 1; i <= 6; i++) {
                        const timelineItemCheckbox = document.getElementById(`timeline${i}Enabled`);
                        if (timelineItemCheckbox) {
                            timelineItemCheckbox.addEventListener('change', function() {
                                // Save the setting immediately
                                saveSetting(`timeline_${i}_enabled`, this.checked ? '1' : '0', 'homepage');
                            });
                        }
                    }

                    // Timeline stats toggle event listener
                    const timelineStatsCheckbox = document.getElementById('timelineStatsEnabled');
                    if (timelineStatsCheckbox) {
                        timelineStatsCheckbox.addEventListener('change', function() {
                            // Save the setting immediately
                            saveSetting('timeline_stats_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Testimonials stats toggle event listener
                    const testimonialsStatsCheckbox = document.getElementById('testimonialsStatsEnabled');
                    if (testimonialsStatsCheckbox) {
                        testimonialsStatsCheckbox.addEventListener('change', function() {
                            // Save the setting immediately
                            saveSetting('testimonials_stats_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Benefits showcase section toggle event listener
                    const benefitsShowcaseCheckbox = document.getElementById('benefitsShowcaseEnabled');
                    if (benefitsShowcaseCheckbox) {
                        benefitsShowcaseCheckbox.addEventListener('change', function() {
                            toggleBenefitsShowcaseSection(this.checked);
                            // Save the setting immediately
                            saveSetting('benefits_showcase_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Categories section toggle event listener
                    const categoriesCheckbox = document.getElementById('categoriesEnabled');
                    if (categoriesCheckbox) {
                        categoriesCheckbox.addEventListener('change', function() {
                            toggleCategoriesSection(this.checked);
                            // Save the setting immediately
                            saveSetting('categories_enabled', this.checked ? '1' : '0', 'homepage');
                        });
                    }

                    // Individual benefit toggle event listeners (Benefits 4-6)
                    for (let i = 4; i <= 6; i++) {
                        const benefitCheckbox = document.getElementById(`benefit${i}Enabled`);
                        if (benefitCheckbox) {
                            benefitCheckbox.addEventListener('change', function() {
                                // Save the setting immediately
                                saveSetting(`benefit_${i}_enabled`, this.checked ? '1' : '0', 'homepage');
                            });
                        }
                    }

                    // Trust indicators toggle event listener
                    const trustIndicatorsCheckbox = document.getElementById('trustIndicatorsEnabled');
                    if (trustIndicatorsCheckbox) {
                        trustIndicatorsCheckbox.addEventListener('change', function() {
                            // Save the setting immediately
                            saveSetting('trust_indicators_enabled', this.checked ? '1' : '0', 'homepage');
                            triggerSiteUpdate();
                        });
                    }

                    // Featured product toggle event listener
                    const featuredProductCheckbox = document.getElementById('featuredProductEnabled');
                    if (featuredProductCheckbox) {
                        featuredProductCheckbox.addEventListener('change', function() {
                            // Save the setting immediately
                            saveSetting('featured_product_enabled', this.checked ? '1' : '0', 'homepage');
                            triggerSiteUpdate();
                        });
                    }

                    // Individual features toggle event listeners
                    for (let i = 1; i <= 6; i++) {
                        const featureCheckbox = document.getElementById(`feature${i}Enabled`);
                        if (featureCheckbox) {
                            featureCheckbox.addEventListener('change', function() {
                                // Save the setting immediately
                                saveSetting(`feature_${i}_enabled`, this.checked ? '1' : '0', 'homepage');
                                triggerSiteUpdate();
                            });
                        }
                    }


                // Setup real-time subscription for settings changes (with improved error handling)
                try {
                    const client = getSupabaseClient();
                    if (client) {
                        // Create subscription with better error handling
                        const subscription = client
                            .channel('site_settings_changes', {
                                config: {
                                    broadcast: { self: false },
                                    presence: { key: 'admin_user' }
                                }
                            })
                            .on('postgres_changes',
                                {
                                    event: '*',
                                    schema: 'public',
                                    table: 'site_settings'
                                },
                                (payload) => {
                                    console.log('Settings changed via realtime:', payload);
                                    // Reload settings when changes occur (with debouncing)
                                    clearTimeout(window.realtimeReloadTimeout);
                                    window.realtimeReloadTimeout = setTimeout(() => {
                                        try {
                                            loadSettings();
                                        } catch (error) {
                                            console.error('Error reloading settings:', error);
                                        }
                                    }, 1000); // Debounce for 1 second
                                }
                            )
                            .subscribe((status) => {
                                console.log('Realtime subscription status:', status);
                                if (status === 'SUBSCRIBED') {
                                    console.log('✅ Realtime subscription active');
                                } else if (status === 'CHANNEL_ERROR') {
                                    console.warn('⚠️ Realtime subscription error - continuing without realtime updates');
                                } else if (status === 'TIMED_OUT') {
                                    console.warn('⚠️ Realtime subscription timed out - continuing without realtime updates');
                                }
                            });

                        // Store subscription for cleanup
                        window.siteSettingsSubscription = subscription;

                        // Cleanup subscription on page unload
                        window.addEventListener('beforeunload', () => {
                            try {
                                if (window.siteSettingsSubscription) {
                                    window.siteSettingsSubscription.unsubscribe();
                                }
                                if (window.realtimeReloadTimeout) {
                                    clearTimeout(window.realtimeReloadTimeout);
                                }
                            } catch (error) {
                                console.error('Error during cleanup:', error);
                            }
                        });
                    } else {
                        console.warn('⚠️ Supabase client not available - skipping realtime subscription');
                    }
                } catch (error) {
                    console.warn('⚠️ Error setting up real-time subscription (continuing without realtime):', error);
                }

                console.log('Site Settings page initialized successfully');

                // Setup category toggles after page initialization
                setupCategoryToggles();

                // Check for failed saves and show retry button if needed
                setTimeout(() => {
                    checkAndShowRetryButton();
                }, 1000);
            } else {
                console.log('Authentication failed, redirecting to login');
            }
        });

        // Category Management Functions
        function toggleCategoryRequiredFields(categoryNumber, isEnabled) {
            const categoryFields = [
                `category${categoryNumber}Name`,
                `category${categoryNumber}Count`,
                `category${categoryNumber}Description`,
                `category${categoryNumber}Image`
            ];

            categoryFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    if (isEnabled) {
                        field.setAttribute('required', 'required');
                    } else {
                        field.removeAttribute('required');
                    }
                }
            });
        }

        // Setup category toggle listeners
        function setupCategoryToggles() {
            for (let i = 1; i <= 4; i++) {
                const toggleCheckbox = document.getElementById(`category${i}Enabled`);
                if (toggleCheckbox) {
                    toggleCheckbox.addEventListener('change', function() {
                        toggleCategoryRequiredFields(i, this.checked);
                    });

                    // Initialize on page load
                    toggleCategoryRequiredFields(i, toggleCheckbox.checked);
                }
            }
        }

        // Dynamic Testimonials Management
        let testimonialsCount = 0;
        let testimonials = [];

        // Add new testimonial
        function addTestimonial(testimonialData = null) {
            testimonialsCount++;
            const testimonialId = testimonialData ? testimonialData.id : `testimonial_${testimonialsCount}`;

            const testimonialHtml = `
                <div class="testimonial-item" data-testimonial-id="${testimonialId}">
                    <div class="testimonial-header">
                        <h5>التقييم ${testimonialsCount}</h5>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم العميل</label>
                            <input type="text" name="testimonial_${testimonialsCount}_name" placeholder="سارة أحمد" value="${testimonialData ? testimonialData.name : ''}" required>
                        </div>
                        <div class="form-group">
                            <label>المدينة</label>
                            <input type="text" name="testimonial_${testimonialsCount}_location" placeholder="بغداد" value="${testimonialData ? testimonialData.location : ''}" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الحرف الأول</label>
                            <input type="text" name="testimonial_${testimonialsCount}_initial" placeholder="س" maxlength="1" value="${testimonialData ? testimonialData.initial : ''}" required>
                            <small class="form-help">سيتم استخدامه كبديل إذا لم تتوفر صورة العميل</small>
                        </div>
                        <div class="form-group">
                            <label>التقييم</label>
                            <select name="testimonial_${testimonialsCount}_rating" required>
                                <option value="5.0" ${testimonialData && testimonialData.rating === '5.0' ? 'selected' : ''}>5.0 - ممتاز</option>
                                <option value="4.5" ${testimonialData && testimonialData.rating === '4.5' ? 'selected' : ''}>4.5 - جيد جداً</option>
                                <option value="4.0" ${testimonialData && testimonialData.rating === '4.0' ? 'selected' : ''}>4.0 - جيد</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>صورة العميل (اختياري)</label>
                        <input type="url" name="testimonial_${testimonialsCount}_image" placeholder="https://example.com/customer-photo.jpg" value="${testimonialData ? testimonialData.image || '' : ''}">
                        <small class="form-help">أدخل رابط صورة العميل (اختياري). إذا لم تتوفر صورة، سيتم استخدام الحرف الأول</small>
                    </div>
                    <div class="form-group">
                        <label>نص التقييم</label>
                        <textarea name="testimonial_${testimonialsCount}_text" rows="3" placeholder="منتجات رائعة وجودة عالية..." required>${testimonialData ? testimonialData.text : ''}</textarea>
                    </div>
                </div>
            `;

            document.getElementById('testimonialsContainer').insertAdjacentHTML('beforeend', testimonialHtml);

            if (!testimonialData) {
                testimonials.push({
                    id: testimonialId,
                    name: '',
                    location: '',
                    initial: '',
                    rating: '5.0',
                    text: '',
                    image: ''
                });
            }
        }

        // Remove testimonial
        function removeTestimonial(testimonialId) {
            const testimonialElement = document.querySelector(`[data-testimonial-id="${testimonialId}"]`);
            if (testimonialElement) {
                testimonialElement.remove();
                testimonials = testimonials.filter(t => t.id !== testimonialId);
            }
        }

        // Load testimonials from settings
        function loadTestimonials() {
            // Clear existing testimonials
            document.getElementById('testimonialsContainer').innerHTML = '';
            testimonialsCount = 0;
            testimonials = [];

            // Load testimonials from currentSettings
            let testimonialIndex = 1;
            while (currentSettings[`testimonial_${testimonialIndex}_name`]) {
                const testimonialData = {
                    id: `testimonial_${testimonialIndex}`,
                    name: currentSettings[`testimonial_${testimonialIndex}_name`] || '',
                    location: currentSettings[`testimonial_${testimonialIndex}_location`] || '',
                    initial: currentSettings[`testimonial_${testimonialIndex}_initial`] || '',
                    rating: currentSettings[`testimonial_${testimonialIndex}_rating`] || '5.0',
                    text: currentSettings[`testimonial_${testimonialIndex}_text`] || '',
                    image: currentSettings[`testimonial_${testimonialIndex}_image`] || ''
                };
                addTestimonial(testimonialData);
                testimonials.push(testimonialData);
                testimonialIndex++;
            }

            // If no testimonials exist, add one default testimonial
            if (testimonialsCount === 0) {
                addTestimonial({
                    id: 'testimonial_1',
                    name: 'سارة أحمد',
                    location: 'بغداد',
                    initial: 'س',
                    rating: '5.0',
                    text: 'منتجات رائعة وجودة عالية، خاصة سيروم فيتامين سي. لاحظت تحسن كبير في بشرتي خلال أسبوعين فقط. التوصيل سريع والتعامل محترف جداً.',
                    image: ''
                });
            }
        }

        // Terms and Conditions Management Functions
        let termsData = [];

        // Load Terms and Conditions from Database
        async function loadTermsAndConditions() {
            try {
                console.log('🔄 Loading terms and conditions...');
                const supabase = getSupabaseClient();
                if (!supabase) {
                    console.error('❌ Supabase client not available for terms loading');
                    showMessage('خطأ في الاتصال بقاعدة البيانات', 'error');
                    return;
                }

                const { data: terms, error } = await supabase
                    .from('terms_conditions')
                    .select('*')
                    .order('order_index', { ascending: true });

                if (error) {
                    console.error('❌ Error loading terms:', error);
                    showMessage('خطأ في تحميل الشروط والأحكام', 'error');
                    // Initialize with empty array to prevent further errors
                    termsData = [];
                    displayTermsSections();
                    return;
                }

                termsData = terms || [];
                console.log(`✅ Loaded ${termsData.length} terms sections`);
                displayTermsSections();

            } catch (error) {
                console.error('❌ Unexpected error loading terms:', error);
                showMessage('خطأ غير متوقع في تحميل الشروط والأحكام', 'error');
                // Initialize with empty array to prevent further errors
                termsData = [];
                displayTermsSections();
            }
        }

        // Display Terms Sections in Admin Panel
        function displayTermsSections() {
            const container = document.getElementById('termsSectionsList');
            if (!container) return;

            container.innerHTML = '';

            termsData.forEach((term, index) => {
                const sectionHtml = createTermsSectionHtml(term, index);
                container.insertAdjacentHTML('beforeend', sectionHtml);
            });
        }

        // Create HTML for Terms Section
        function createTermsSectionHtml(term, index) {
            return `
                <div class="terms-section-item" data-term-id="${term.id}">
                    <div class="section-header-controls">
                        <div class="form-row">
                            <div class="form-group">
                                <label>عنوان القسم</label>
                                <input type="text" class="section-title" value="${term.section_title}" required>
                            </div>
                            <div class="form-group">
                                <label>نوع القسم</label>
                                <select class="section-type">
                                    <option value="الشروط العامة" ${term.section_type === 'الشروط العامة' ? 'selected' : ''}>الشروط العامة</option>
                                    <option value="الطلبات والدفع" ${term.section_type === 'الطلبات والدفع' ? 'selected' : ''}>الطلبات والدفع</option>
                                    <option value="التوصيل" ${term.section_type === 'التوصيل' ? 'selected' : ''}>التوصيل</option>
                                    <option value="الإرجاع والاستبدال" ${term.section_type === 'الإرجاع والاستبدال' ? 'selected' : ''}>الإرجاع والاستبدال</option>
                                    <option value="سياسة الخصوصية" ${term.section_type === 'سياسة الخصوصية' ? 'selected' : ''}>سياسة الخصوصية</option>
                                    <option value="معلومات التواصل" ${term.section_type === 'معلومات التواصل' ? 'selected' : ''}>معلومات التواصل</option>
                                    <option value="أخرى" ${term.section_type === 'أخرى' ? 'selected' : ''}>أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>ترتيب العرض</label>
                                <input type="number" class="section-order" min="0" value="${term.order_index || 0}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>تفعيل القسم</label>
                            <div class="toggle-switch">
                                <input type="checkbox" class="section-enabled" ${term.is_active ? 'checked' : ''}>
                                <label class="toggle-label">
                                    <span class="toggle-inner"></span>
                                    <span class="toggle-switch-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>محتوى القسم</label>
                        <textarea class="section-content" rows="8" required>${term.section_content}</textarea>
                    </div>

                    <div class="section-actions">
                        <button type="button" class="btn btn-primary" onclick="saveTermsSection(this)">
                            <i class="fas fa-save"></i>
                            حفظ القسم
                        </button>
                        <button type="button" class="btn btn-danger" onclick="deleteTermsSection(this)">
                            <i class="fas fa-trash"></i>
                            حذف القسم
                        </button>
                    </div>
                </div>
            `;
        }

        // Add New Terms Section
        function addNewTermsSection() {
            const template = document.getElementById('termsSectionTemplate');
            const container = document.getElementById('termsSectionsList');

            if (!template || !container) return;

            const newSection = template.cloneNode(true);
            newSection.id = '';
            newSection.classList.remove('hidden');
            newSection.classList.add('visible');
            newSection.setAttribute('data-term-id', 'new');

            container.appendChild(newSection);
        }

        // Save Terms Section
        async function saveTermsSection(button) {
            const sectionItem = button.closest('.terms-section-item');
            const termId = sectionItem.getAttribute('data-term-id');

            const sectionData = {
                section_title: sectionItem.querySelector('.section-title').value,
                section_content: sectionItem.querySelector('.section-content').value,
                section_type: sectionItem.querySelector('.section-type').value,
                order_index: parseInt(sectionItem.querySelector('.section-order').value) || 0,
                is_active: sectionItem.querySelector('.section-enabled').checked
            };

            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                let result;
                if (termId === 'new') {
                    // Insert new section
                    result = await supabase
                        .from('terms_conditions')
                        .insert([sectionData])
                        .select();
                } else {
                    // Update existing section
                    result = await supabase
                        .from('terms_conditions')
                        .update(sectionData)
                        .eq('id', termId)
                        .select();
                }

                if (result.error) {
                    throw result.error;
                }

                showMessage('تم حفظ القسم بنجاح!');
                await loadTermsAndConditions(); // Reload data

            } catch (error) {
                console.error('Error saving terms section:', error);
                showMessage('حدث خطأ أثناء حفظ القسم', 'error');
            }
        }

        // Delete Terms Section
        async function deleteTermsSection(button) {
            const sectionItem = button.closest('.terms-section-item');
            const termId = sectionItem.getAttribute('data-term-id');

            if (termId === 'new') {
                // Just remove from DOM if it's a new unsaved section
                sectionItem.remove();
                return;
            }

            if (!confirm('هل أنت متأكد من حذف هذا القسم؟')) {
                return;
            }

            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                const { error } = await supabase
                    .from('terms_conditions')
                    .delete()
                    .eq('id', termId);

                if (error) {
                    throw error;
                }

                showMessage('تم حذف القسم بنجاح!');
                await loadTermsAndConditions(); // Reload data

            } catch (error) {
                console.error('Error deleting terms section:', error);
                showMessage('حدث خطأ أثناء حذف القسم', 'error');
            }
        }

        // Handle Terms General Form
        document.addEventListener('DOMContentLoaded', function() {
            const termsGeneralForm = document.getElementById('termsGeneralForm');
            if (termsGeneralForm) {
                termsGeneralForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);

                    try {
                        await saveSetting('terms_page_enabled', formData.get('terms_page_enabled') === 'on', 'pages');
                        await saveSetting('terms_page_title', formData.get('terms_page_title'), 'pages');
                        await saveSetting('terms_page_description', formData.get('terms_page_description'), 'pages');

                        showMessage('تم حفظ الإعدادات العامة بنجاح!');
                        triggerSiteUpdate();

                    } catch (error) {
                        console.error('Error saving terms general settings:', error);
                        showMessage('حدث خطأ أثناء حفظ الإعدادات', 'error');
                    }
                });
            }

            // Load terms data when page loads
            loadTermsAndConditions();
        });
    </script>

    <!-- Enhanced Responsive Design for Settings -->
    <style>
        /* Enhanced responsive design for settings navigation */
        @media (max-width: 1199px) {
            .settings-nav {
                margin-bottom: var(--spacing-lg);
            }

            .nav-tabs {
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            .nav-tab {
                min-width: 120px;
                padding: var(--spacing-md) var(--spacing-lg);
                font-size: 0.9rem;
            }
        }

        /* Timeline Admin Form Styles */
        .timeline-item-group {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .timeline-item-group h5 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .timeline-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .timeline-item-header h5 {
            margin-bottom: 0;
            border-bottom: none;
            padding-bottom: 0;
        }

        .timeline-item-header .form-group {
            margin-bottom: 0;
        }

        .statistics-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }

        .statistics-section-header h4 {
            margin-bottom: 0;
            color: var(--primary-color);
        }

        .statistics-section-header .form-group {
            margin-bottom: 0;
        }

        .timeline-item-group .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .timeline-item-group .form-group {
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .timeline-item-group .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Enhanced Benefits Admin Form Styles */
        .benefit-item-group {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .benefit-item-group:hover {
            box-shadow: 0 4px 16px rgba(74, 144, 164, 0.1);
            border-color: rgba(74, 144, 164, 0.3);
        }

        .benefit-item-group h5 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 1.2rem;
            padding-bottom: 0.8rem;
            border-bottom: 3px solid var(--primary-color);
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .benefit-item-group h5::before {
            content: "✨";
            font-size: 1.2rem;
        }

        .benefit-item-group .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.2rem;
            margin-bottom: 1.2rem;
        }

        .benefit-item-group .form-group {
            margin-bottom: 1.2rem;
        }

        .benefit-item-group .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            display: block;
        }

        .benefit-item-group input,
        .benefit-item-group textarea {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.8rem;
            transition: all 0.3s ease;
        }

        .benefit-item-group input:focus,
        .benefit-item-group textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
            outline: none;
        }

        @media (max-width: 768px) {
            .benefit-item-group .form-row {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .settings-nav {
                border-radius: var(--radius-md);
                margin-bottom: var(--spacing-md);
            }

            .nav-tabs {
                flex-wrap: wrap;
                justify-content: center;
                gap: var(--spacing-xs);
                padding: var(--spacing-sm);
            }

            .nav-tab {
                flex: 1;
                min-width: calc(50% - var(--spacing-xs));
                max-width: 160px;
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 0.85rem;
                margin: var(--spacing-xs) / 2;
            }

            .nav-tab i {
                font-size: 1rem;
            }

            .settings-section {
                padding: var(--spacing-lg);
                border-radius: var(--radius-md);
            }
        }

        @media (max-width: 480px) {
            .nav-tab {
                flex: 1;
                min-width: calc(100% - var(--spacing-sm));
                max-width: none;
                margin: var(--spacing-xs) / 4;
            }

            .settings-section {
                padding: var(--spacing-md);
                margin: 0 calc(-1 * var(--spacing-sm));
            }
        }

        /* Enhanced focus states for accessibility */
        .nav-tab:focus-visible {
            outline: 3px solid var(--color-primary);
            outline-offset: 2px;
            box-shadow: 0 0 0 6px rgba(130, 135, 122, 0.2);
        }

        /* Improved print styles */
        @media print {
            .settings-nav {
                box-shadow: none;
                border: 2px solid #000;
            }

            .nav-tab.active {
                background: #000 !important;
                color: #fff !important;
            }

            .settings-section {
                box-shadow: none;
                border: 1px solid #000;
                page-break-inside: avoid;
            }
        }

        /* Testimonials Management Styles */
        .testimonials-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
        }

        .testimonials-header h4 {
            margin: 0;
            color: var(--color-text-primary);
            font-size: 1.3rem;
            font-weight: 700;
        }

        .testimonial-item {
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.02) 0%,
                rgba(130, 135, 122, 0.05) 100%);
            border: 1px solid rgba(130, 135, 122, 0.1);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            position: relative;
            transition: var(--transition-base);
        }

        .testimonial-item:hover {
            box-shadow: var(--shadow-md);
            border-color: rgba(130, 135, 122, 0.15);
        }

        .testimonial-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
        }

        .testimonial-header h5 {
            margin: 0;
            color: var(--color-text-primary);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .btn-sm {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.875rem;
            border-radius: var(--radius-md);
        }

        .btn-danger {
            background: linear-gradient(135deg,
                var(--color-danger) 0%,
                #dc3545 100%);
            border: 1px solid var(--color-danger);
            color: white;
            transition: var(--transition-base);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg,
                #c82333 0%,
                #bd2130 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .btn-outline-primary {
            background: transparent;
            border: 2px solid var(--color-primary);
            color: var(--color-primary);
            transition: var(--transition-base);
        }

        .btn-outline-primary:hover {
            background: var(--color-primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.3);
        }

        /* Category Management Styles */
        .category-section {
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.02) 0%,
                rgba(130, 135, 122, 0.05) 100%);
            border: 1px solid rgba(130, 135, 122, 0.1);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            position: relative;
            transition: var(--transition-base);
        }

        .category-section:hover {
            box-shadow: var(--shadow-md);
            border-color: rgba(130, 135, 122, 0.15);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
        }

        .category-header h4 {
            margin: 0;
            color: var(--color-text-primary);
            font-size: 1.2rem;
            font-weight: 600;
        }

        /* Toggle Switch Styles */
        .toggle-switch {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
            user-select: none;
            padding: 0.25rem;
            border-radius: 6px;
            transition: var(--transition-base);
        }

        .toggle-switch:hover {
            background: rgba(130, 135, 122, 0.05);
        }

        .toggle-switch input[type="checkbox"] {
            display: none;
        }

        .toggle-slider {
            position: relative;
            width: 50px;
            height: 24px;
            background: var(--color-gray-300);
            border-radius: 12px;
            transition: var(--transition-base);
            cursor: pointer;
            border: 2px solid transparent;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .toggle-slider:hover {
            background: var(--color-gray-400);
            transform: scale(1.05);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(74, 144, 164, 0.2);
        }

        .toggle-slider:active {
            transform: scale(0.95);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: var(--transition-base);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch input[type="checkbox"]:checked + .toggle-slider {
            background: var(--color-primary);
            border-color: var(--color-primary-600);
            box-shadow: 0 0 0 2px rgba(130, 135, 122, 0.2);
        }

        .toggle-switch input[type="checkbox"]:checked + .toggle-slider:hover {
            background: var(--color-primary-600);
            transform: scale(1.05);
        }

        .toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
            transform: translateX(26px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        /* Additional hover and focus states for better UX */
        .toggle-switch input[type="checkbox"]:focus + .toggle-slider {
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.2);
            outline: none;
        }

        .toggle-switch input[type="checkbox"]:disabled + .toggle-slider {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .toggle-switch input[type="checkbox"]:disabled + .toggle-slider:hover {
            transform: none;
            background: var(--color-gray-300);
        }

        .toggle-label {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-base);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .toggle-label:hover {
            color: var(--color-primary);
            background: rgba(130, 135, 122, 0.05);
        }

        .toggle-switch input[type="checkbox"]:checked ~ .toggle-label {
            color: var(--color-primary-600);
            font-weight: 600;
        }

        .toggle-switch:hover .toggle-slider {
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }
    </style>

    <!-- Mobile Sidebar System -->
    <!-- Legacy mobile sidebar code removed - now using unified-sidebar-manager.js -->

    <!-- Site Settings System -->
    <script src="../js/site-settings.js"></script>

    <!-- Unified Sidebar System -->
    <script src="js/unified-sidebar-manager.js"></script>

    <!-- Unified Notification System -->
    <script src="js/unified-notification-system.js"></script>

    <!-- Professional Logout System -->
    <script src="js/logout-system.js"></script>
</body>
</html>
