<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>إدارة المحتوى - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <!-- Rich Text Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <!-- Sortable.js for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script></script>
    <!-- Removed conflicting sidebar scripts - using unified system -->
    
    <style>
        /* Enhanced Content Management specific styles - base styles are in enhanced-sidebar.css */

        /* Enhanced Content Management specific styles */
        .content-management {
            padding: var(--spacing-xxl);
            background: linear-gradient(135deg, #fafbfc 0%, #ffffff 50%, #f8f9fa 100%);
            min-height: 100vh;
            position: relative;
        }

        .content-management::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(130, 135, 122, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(130, 135, 122, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .content-management > * {
            position: relative;
            z-index: 1;
        }

        /* Enhanced Welcome Section */
        .welcome-section {
            margin-bottom: var(--spacing-xxl);
        }

        .welcome-section .card {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            border-radius: var(--radius-xxl);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .welcome-section .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            opacity: 0.8;
        }

        .welcome-section .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 16px 50px rgba(130, 135, 122, 0.12);
        }

        .welcome-section .card-body {
            padding: var(--spacing-xxl);
        }

        .welcome-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.3);
            position: relative;
        }

        .welcome-icon::after {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0.3;
            filter: blur(8px);
        }

        /* Enhanced Content Stats */
        .content-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            border: 1px solid rgba(130, 135, 122, 0.12);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(130, 135, 122, 0.15);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 0.2rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #333333;
            font-weight: 600;
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            flex-shrink: 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .stat-icon::after {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0.2;
            filter: blur(10px);
        }

        .stat-icon.faq {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        .stat-icon.faq::after {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .stat-icon.guidelines {
            background: linear-gradient(135deg, #28a745 0%, #229954 100%);
        }
        .stat-icon.guidelines::after {
            background: linear-gradient(135deg, #28a745, #229954);
        }
        .stat-icon.tips {
            background: linear-gradient(135deg, #ffc107 0%, #e67e22 100%);
        }
        .stat-icon.tips::after {
            background: linear-gradient(135deg, #ffc107, #e67e22);
        }
        .stat-icon.terms {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }
        .stat-icon.terms::after {
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
        }

        /* Enhanced Messages */
        .messages-container {
            margin-bottom: var(--spacing-lg);
        }

        .message {
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            transition: var(--transition-base);
        }

        .message.success {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
            color: var(--color-success);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .message.error {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
            color: var(--color-danger);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .message::before {
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .message.success::before {
            content: '\f058'; /* fa-check-circle */
        }

        .message.error::before {
            content: '\f06a'; /* fa-exclamation-circle */
        }

        /* Enhanced Content Tabs Container */
        .content-tabs-container {
            margin-bottom: var(--spacing-xl);
        }

        .tabs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            background: rgba(130, 135, 122, 0.02);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
        }

        .tabs-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Enhanced Professional Content Tabs */
        .tabs {
            display: flex;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .tab {
            flex: 1;
            padding: var(--spacing-lg) var(--spacing-xl);
            background: none;
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            color: var(--color-text-secondary);
            transition: var(--transition-base);
            border-bottom: 3px solid transparent;
            position: relative;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            /* Performance optimizations */
            will-change: transform, background-color;
            contain: layout style;
        }

        .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(130, 135, 122, 0.1) 0%,
                rgba(130, 135, 122, 0.05) 100%);
            opacity: 0;
            transition: var(--transition-base);
            pointer-events: none;
        }

        .tab:hover::before {
            opacity: 1;
        }

        .tab.active {
            color: white;
            border-bottom-color: var(--color-primary);
            background: linear-gradient(135deg,
                var(--color-primary) 0%,
                var(--color-primary-dark) 100%);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .tab.active::before {
            display: none;
        }

        .tab:hover {
            color: var(--color-primary);
            transform: translateY(-1px);
        }

        .tab.active:hover {
            color: white;
            transform: translateY(-2px);
        }

        .tab i {
            font-size: 1.1rem;
            transition: var(--transition-base);
        }

        .tab:hover i {
            transform: scale(1.1);
        }

        .tab.active i {
            transform: scale(1.05);
        }

        .tab-content {
            display: none;
            padding: var(--spacing-xl);
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.5s ease-in-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Content Forms */
        .content-form {
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(130, 135, 122, 0.1);
            margin-bottom: var(--spacing-lg);
        }

        .content-form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
        }

        .content-form-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--color-text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .content-form-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Enhanced Rich Text Editor */
        .editor-container {
            margin-bottom: var(--spacing-lg);
        }

        .editor-label {
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .editor-wrapper {
            border: 2px solid rgba(130, 135, 122, 0.1);
            border-radius: var(--radius-md);
            overflow: hidden;
            transition: var(--transition-base);
        }

        .editor-wrapper:focus-within {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .ql-toolbar {
            background: rgba(130, 135, 122, 0.05);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
        }

        .ql-editor {
            min-height: 200px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            line-height: 1.6;
        }

        .ql-editor.ql-blank::before {
            color: var(--color-text-muted);
            font-style: italic;
        }

        /* Enhanced Content List */
        .content-list {
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(130, 135, 122, 0.1);
            overflow: hidden;
        }

        .content-list-header {
            background: rgba(130, 135, 122, 0.05);
            padding: var(--spacing-lg);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-list-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .content-list-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .content-item {
            padding: var(--spacing-lg);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
            transition: var(--transition-base);
            position: relative;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-item:hover {
            background: rgba(130, 135, 122, 0.02);
        }

        .content-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }

        .content-item-title {
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
            cursor: pointer;
        }

        .content-item-title:hover {
            color: var(--color-primary);
        }

        .content-item-meta {
            font-size: 0.9rem;
            color: var(--color-text-muted);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .content-item-actions {
            display: flex;
            gap: var(--spacing-xs);
            opacity: 0;
            transition: var(--transition-base);
        }

        .content-item:hover .content-item-actions {
            opacity: 1;
        }

        .content-item-preview {
            color: var(--color-text-secondary);
            line-height: 1.6;
            margin-top: var(--spacing-sm);
            max-height: 60px;
            overflow: hidden;
            position: relative;
        }

        .content-item-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 100%;
            height: 20px;
            background: linear-gradient(transparent, var(--color-bg-primary));
        }

        /* Enhanced Action Buttons */
        .action-btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: none;
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition-base);
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .action-btn.edit {
            background: rgba(23, 162, 184, 0.1);
            color: var(--color-info);
        }

        .action-btn.edit:hover {
            background: var(--color-info);
            color: white;
        }

        .action-btn.delete {
            background: rgba(220, 53, 69, 0.1);
            color: var(--color-danger);
        }

        .action-btn.delete:hover {
            background: var(--color-danger);
            color: white;
        }

        .action-btn.view {
            background: rgba(130, 135, 122, 0.1);
            color: var(--color-primary);
        }

        .action-btn.view:hover {
            background: var(--color-primary);
            color: white;
        }

        /* Enhanced Loading States */
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xxl);
            gap: var(--spacing-md);
            color: var(--color-text-secondary);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(130, 135, 122, 0.2);
            border-top-color: var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .welcome-section .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .welcome-actions {
                width: 100%;
                justify-content: stretch;
            }

            .welcome-actions .btn {
                flex: 1;
            }

            .content-stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: var(--spacing-md);
            }

            .tabs-header {
                flex-direction: column;
                gap: var(--spacing-md);
                align-items: stretch;
            }

            .tabs-actions {
                width: 100%;
                justify-content: stretch;
            }

            .tabs-actions .btn {
                flex: 1;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                border-bottom: none;
                border-right: 3px solid transparent;
            }

            .tab.active {
                border-right-color: var(--color-primary);
                transform: translateX(2px);
            }

            .tab:hover {
                transform: translateX(1px);
            }

            .tab.active:hover {
                transform: translateX(2px);
            }

            .content-form-header {
                flex-direction: column;
                gap: var(--spacing-md);
                align-items: stretch;
            }

            .content-form-actions {
                width: 100%;
                justify-content: stretch;
            }

            .content-form-actions .btn {
                flex: 1;
            }

            .content-list-header {
                flex-direction: column;
                gap: var(--spacing-md);
                align-items: stretch;
            }

            .content-list-actions {
                width: 100%;
                justify-content: stretch;
            }

            .content-list-actions .btn {
                flex: 1;
            }

            .content-item-header {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }

            .content-item-actions {
                opacity: 1;
                width: 100%;
                justify-content: stretch;
            }

            .content-item-actions .action-btn {
                flex: 1;
            }
        }

        @media (max-width: 480px) {
            .tab-content {
                padding: var(--spacing-md);
            }

            .content-form {
                padding: var(--spacing-md);
            }

            .content-stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                padding: var(--spacing-md);
            }

            .ql-editor {
                min-height: 150px;
            }

            .content-item {
                padding: var(--spacing-md);
            }
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .stat-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        .content-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        /* Print styles */
        @media print {
            .welcome-actions,
            .tabs-actions,
            .content-form-actions,
            .content-list-actions,
            .content-item-actions {
                display: none !important;
            }

            .content-form,
            .content-list,
            .stat-card {
                break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .ql-editor {
                min-height: auto !important;
            }
        }

        /* Enhanced Rich Text Editor */
        .rich-editor-container {
            background: white;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .rich-editor-container:focus-within {
            border-color: #82877a;
            box-shadow: 0 0 0 4px rgba(130, 135, 122, 0.1);
        }

        .ql-toolbar {
            border: none !important;
            border-bottom: 2px solid #f0f0f0 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 1rem !important;
        }

        .ql-container {
            border: none !important;
            font-family: 'Cairo', sans-serif !important;
            font-size: 1rem !important;
            min-height: 200px;
        }

        .ql-editor {
            padding: 1.5rem !important;
            line-height: 1.8 !important;
        }

        .ql-editor.ql-blank::before {
            color: #999 !important;
            font-style: normal !important;
        }

        /* Drag and Drop Styling */
        .content-list {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .content-item {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: move;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .content-item:hover {
            border-color: #82877a;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.2);
        }

        .content-item.sortable-ghost {
            opacity: 0.5;
            background: rgba(130, 135, 122, 0.1);
        }

        .content-item.sortable-chosen {
            transform: rotate(2deg);
            box-shadow: 0 10px 30px rgba(130, 135, 122, 0.3);
        }

        .content-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .content-item-title {
            font-weight: 700;
            color: #121414;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .drag-handle {
            color: #82877a;
            cursor: grab;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .drag-handle:hover {
            background: rgba(130, 135, 122, 0.1);
            transform: scale(1.1);
        }

        .drag-handle:active {
            cursor: grabbing;
        }

        .content-item-actions {
            display: flex;
            gap: 0.5rem;
        }

        .content-item-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .content-item-content {
            color: #333;
            line-height: 1.6;
            margin-bottom: 1rem;
        }



        /* Form Enhancements */
        .content-form {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(130, 135, 122, 0.1);
            margin-bottom: 2rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #f0f0f0;
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            z-index: 10;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f0f0f0;
            border-top: 4px solid #82877a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Cards */
        .content-card {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            border-radius: var(--radius-xxl);
            box-shadow: 0 8px 32px rgba(130, 135, 122, 0.1);
            margin-bottom: var(--spacing-xxl);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(130, 135, 122, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: white;
            padding: var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .card-content {
            padding: var(--spacing-xxl);
            background: #ffffff;
        }

        /* Buttons */
        .btn {
            background: #82877a;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn:hover {
            background: #6b7062;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* Messages */
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #82877a;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Delete Confirmation Modal */
        .delete-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .delete-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .delete-modal {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.7) translateY(-50px);
            transition: all 0.3s ease;
            overflow: hidden;
            direction: rtl;
        }

        .delete-modal-overlay.show .delete-modal {
            transform: scale(1) translateY(0);
        }

        .delete-modal-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
            position: relative;
        }

        .delete-modal-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .delete-modal-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .delete-modal-close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .delete-modal-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }

        .delete-modal-body {
            padding: 2rem;
            text-align: center;
        }

        .delete-modal-message {
            font-size: 1.1rem;
            color: #333;
            line-height: 1.6;
            margin-bottom: 0.5rem;
        }

        .delete-modal-item {
            font-weight: 700;
            color: #e74c3c;
            background: #fff5f5;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid #fed7d7;
        }

        .delete-modal-warning {
            font-size: 0.9rem;
            color: #666;
            margin-top: 1rem;
            font-style: italic;
        }

        .delete-modal-footer {
            padding: 1.5rem 2rem;
            background: #f8f9fa;
            display: flex;
            gap: 1rem;
            justify-content: center;
            border-top: 1px solid #eee;
        }

        .delete-modal-btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            min-width: 120px;
        }

        .delete-modal-btn-cancel {
            background: #6c757d;
            color: white;
        }

        .delete-modal-btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }

        .delete-modal-btn-confirm {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            position: relative;
            overflow: hidden;
            animation: pulse 2s infinite;
        }

        .delete-modal-btn-confirm:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .delete-modal-btn-confirm:active {
            transform: translateY(0);
        }

        .delete-modal-btn-confirm.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .delete-modal-btn-confirm.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideInRight {
            0% {
                transform: translateX(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Enhanced message styles */
        .message.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .message.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        .message i {
            margin-left: 0.5rem;
            font-size: 1.1rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            /* Mobile menu button styles are now in css/enhanced-sidebar.css */

            .tabs {
                flex-direction: column;
            }
        }

        /* Mobile menu button styles are now in css/enhanced-sidebar.css */

        /* Mobile responsive for delete modal */
        @media (max-width: 768px) {
            .delete-modal {
                width: 95%;
                margin: 1rem;
            }

            .delete-modal-footer {
                flex-direction: column;
            }

            .delete-modal-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Unified Navigation Section -->
        <div class="sidebar-section unified-navigation">
            <!-- Seamless Navigation List -->
            <nav class="sidebar-nav">
                <!-- Dashboard Navigation Links -->
                <a href="dashboard.html" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="orders.html" class="sidebar-link">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link active">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-file-alt"></i>
                        إدارة المحتوى
                    </h1>
                </div>

                <div class="top-bar-actions">
                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>


                </div>
            </div>
        </div>

        <div class="content-management">
            <!-- Enhanced Welcome Section -->
            <div class="welcome-section mb-lg">
                <div class="card card-elevated">
                    <div class="card-body">
                        <div class="flex items-center gap-lg">
                            <div class="welcome-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold mb-sm">إدارة المحتوى</h2>
                                <p class="text-secondary">إدارة وتحرير محتوى الموقع والصفحات بطريقة احترافية ومنظمة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Messages -->
            <div class="messages-container">
                <div class="message success" id="successMessage" style="display: none;"></div>
                <div class="message error" id="errorMessage" style="display: none;"></div>
            </div>

            <!-- Enhanced Content Stats -->
            <div class="content-stats-grid mb-lg">
                <div class="stat-card" data-stat="faq">
                    <div class="stat-header">
                        <div class="stat-icon faq">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+3</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="faqCount">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">الأسئلة الشائعة</div>
                        <div class="stat-description">أسئلة وأجوبة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="guidelines">
                    <div class="stat-header">
                        <div class="stat-icon guidelines">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+2</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="guidelinesCount">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">إرشادات الاستخدام</div>
                        <div class="stat-description">دليل المستخدم</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="tips">
                    <div class="stat-header">
                        <div class="stat-icon tips">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="tipsCount">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">النصائح الإضافية</div>
                        <div class="stat-description">نصائح مفيدة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="terms">
                    <div class="stat-header">
                        <div class="stat-icon terms">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="stat-change neutral">
                            <i class="fas fa-minus"></i>
                            <span>ثابت</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="termsCount">
                            <span class="loading-placeholder">1</span>
                        </div>
                        <div class="stat-label">الشروط والأحكام</div>
                        <div class="stat-description">سياسات الموقع</div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Tabs -->
            <div class="content-tabs-container card">
                <div class="tabs-header">
                    <div class="tabs">
                        <button class="tab active" onclick="switchTab('faq')" data-tab="faq">
                            <i class="fas fa-question-circle"></i>
                            الأسئلة الشائعة
                        </button>
                        <button class="tab" onclick="switchTab('guidelines')" data-tab="guidelines">
                            <i class="fas fa-book"></i>
                            إرشادات الاستخدام
                        </button>
                        <button class="tab" onclick="switchTab('tips')" data-tab="tips">
                            <i class="fas fa-lightbulb"></i>
                            النصائح الإضافية
                        </button>
                        <button class="tab" onclick="switchTab('terms')" data-tab="terms">
                            <i class="fas fa-file-contract"></i>
                            الشروط والأحكام
                        </button>
                    </div>

                </div>
            </div>



            <!-- FAQ Tab -->
            <div id="faq-tab" class="tab-content active">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-question-circle"></i>
                            إدارة الأسئلة الشائعة
                        </div>
                        <button class="btn btn-success" onclick="showAddFaqModal()">
                            <i class="fas fa-plus"></i>
                            إضافة سؤال جديد
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="loading" id="faqLoading">
                            <i class="fas fa-spinner"></i>
                            <p>جاري تحميل الأسئلة الشائعة...</p>
                        </div>
                        <div id="faqContent"></div>
                    </div>
                </div>
            </div>

            <!-- Guidelines Tab -->
            <div id="guidelines-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-lightbulb"></i>
                            إدارة إرشادات الاستخدام
                        </div>
                        <button class="btn btn-success" onclick="showAddGuidelineModal()">
                            <i class="fas fa-plus"></i>
                            إضافة إرشاد جديد
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="loading" id="guidelinesLoading">
                            <i class="fas fa-spinner"></i>
                            <p>جاري تحميل الإرشادات...</p>
                        </div>
                        <div id="guidelinesContent"></div>
                    </div>
                </div>
            </div>

            <!-- Tips Tab -->
            <div id="tips-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-star"></i>
                            إدارة النصائح الإضافية
                        </div>
                        <button class="btn btn-primary" onclick="showAddTipModal()">
                            <i class="fas fa-plus"></i>
                            إضافة نصيحة جديدة
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="tipsLoading" style="text-align: center; padding: 2rem; color: #82877a;">
                            <i class="fas fa-spinner"></i>
                            <p>جاري تحميل النصائح...</p>
                        </div>
                        <div id="tipsContent"></div>
                    </div>
                </div>
            </div>

            <!-- Terms & Conditions Tab -->
            <div id="terms-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-file-contract"></i>
                            إدارة الشروط والأحكام
                        </div>
                        <button class="btn btn-primary" onclick="showAddTermModal()">
                            <i class="fas fa-plus"></i>
                            إضافة قسم جديد
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="termsLoading" style="text-align: center; padding: 2rem; color: #82877a;">
                            <i class="fas fa-spinner"></i>
                            <p>جاري تحميل الشروط والأحكام...</p>
                        </div>
                        <div id="termsContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Check authentication
        function checkAuth() {
            const adminUser = sessionStorage.getItem('adminUser');
            if (!adminUser) {
                console.log('No admin user found in session, redirecting to login');
                window.location.href = 'login.html';
                return false;
            }

            try {
                const user = JSON.parse(adminUser);
                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff >= 8) {
                    console.log('Session expired, redirecting to login');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                // Update user info in sidebar - always use standardized values
                const sidebarUserName = document.getElementById('sidebarUserName');
                const sidebarUserAvatar = document.getElementById('sidebarUserAvatar');

                if (sidebarUserName) {
                    sidebarUserName.textContent = 'مدير النظام';
                }
                if (sidebarUserAvatar) {
                    sidebarUserAvatar.textContent = 'A';
                    sidebarUserAvatar.setAttribute('title', 'مدير النظام');
                }

                console.log('Authentication successful for user:', user.username);
                return true;
            } catch (error) {
                console.error('Authentication error:', error);
                sessionStorage.removeItem('adminUser');
                window.location.href = 'login.html';
                return false;
            }
        }

        // Logout function - now uses professional logout modal system
        function logout() {
            showLogoutModal();
        }

        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // Show message with enhanced effects
        function showMessage(message, type = 'success') {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            if (type === 'success') {
                successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                successDiv.style.display = 'block';
                successDiv.style.animation = 'slideInRight 0.5s ease-out';
                errorDiv.style.display = 'none';

                // Play success sound
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
                    oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);

                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.4);
                } catch (error) {
                    console.log('Audio not supported');
                }
            } else {
                errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
                errorDiv.style.display = 'block';
                errorDiv.style.animation = 'slideInRight 0.5s ease-out';
                successDiv.style.display = 'none';
            }

            // Hide message after 5 seconds
            setTimeout(() => {
                successDiv.style.display = 'none';
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Switch tabs
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab
            event.target.classList.add('active');

            // Load content based on tab
            if (tabName === 'faq') {
                loadFAQ();
            } else if (tabName === 'guidelines') {
                loadGuidelines();
            } else if (tabName === 'tips') {
                loadTips();
            } else if (tabName === 'terms') {
                loadTerms();
            }
        }

        // Play notification sound
        function playNotificationSound() {
            try {
                // Create audio context for notification sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (error) {
                // Silently fail if audio is not supported
                console.log('Audio notification not supported');
            }
        }

        // Custom Delete Confirmation Modal System
        function showDeleteConfirmation(options) {
            // Play notification sound
            playNotificationSound();

            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'delete-modal-overlay';
                overlay.id = 'deleteModalOverlay';

                // Create modal content
                overlay.innerHTML = `
                    <div class="delete-modal">
                        <div class="delete-modal-header">
                            <button class="delete-modal-close" onclick="closeDeleteModal(false)">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="delete-modal-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h3 class="delete-modal-title">تأكيد الحذف</h3>
                        </div>
                        <div class="delete-modal-body">
                            <p class="delete-modal-message">${options.message}</p>
                            <div class="delete-modal-item">${options.itemName}</div>
                            <p class="delete-modal-warning">⚠️ لا يمكن التراجع عن هذا الإجراء</p>
                            <div style="margin-top: 1rem; padding: 0.8rem; background: #f8f9fa; border-radius: 8px; font-size: 0.9rem; color: #666;">
                                <i class="fas fa-keyboard"></i>
                                <strong>اختصارات لوحة المفاتيح:</strong>
                                <br>• Enter: تأكيد الحذف
                                <br>• Escape: إلغاء
                            </div>
                        </div>
                        <div class="delete-modal-footer">
                            <button class="delete-modal-btn delete-modal-btn-cancel" onclick="closeDeleteModal(false)" title="اضغط Escape للإلغاء">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                            <button class="delete-modal-btn delete-modal-btn-confirm" onclick="confirmDelete()" title="اضغط Enter للتأكيد">
                                <i class="fas fa-trash"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </div>
                `;

                // Add to body
                document.body.appendChild(overlay);

                // Store resolve function globally
                window.deleteModalResolve = resolve;

                // Show modal with animation
                setTimeout(() => {
                    overlay.classList.add('show');
                    // Add shake effect to modal
                    const modal = overlay.querySelector('.delete-modal');
                    modal.style.animation = 'shake 0.5s ease-in-out';
                }, 10);

                // Handle keyboard shortcuts
                const handleKeyboard = (e) => {
                    if (e.key === 'Escape') {
                        closeDeleteModal(false);
                        document.removeEventListener('keydown', handleKeyboard);
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        confirmDelete();
                        document.removeEventListener('keydown', handleKeyboard);
                    } else if (e.key === 'Tab') {
                        // Trap focus within modal
                        const focusableElements = overlay.querySelectorAll('button');
                        const firstElement = focusableElements[0];
                        const lastElement = focusableElements[focusableElements.length - 1];

                        if (e.shiftKey && document.activeElement === firstElement) {
                            e.preventDefault();
                            lastElement.focus();
                        } else if (!e.shiftKey && document.activeElement === lastElement) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                };
                document.addEventListener('keydown', handleKeyboard);

                // Focus the cancel button initially
                setTimeout(() => {
                    const cancelBtn = overlay.querySelector('.delete-modal-btn-cancel');
                    if (cancelBtn) cancelBtn.focus();
                }, 100);

                // Handle click outside
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        closeDeleteModal(false);
                    }
                });
            });
        }

        // Close delete modal
        function closeDeleteModal(confirmed) {
            const overlay = document.getElementById('deleteModalOverlay');
            if (overlay) {
                overlay.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                    if (window.deleteModalResolve) {
                        window.deleteModalResolve(confirmed);
                        window.deleteModalResolve = null;
                    }
                }, 300);
            }
        }

        // Confirm delete
        function confirmDelete() {
            const confirmBtn = document.querySelector('.delete-modal-btn-confirm');
            confirmBtn.classList.add('loading');
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';

            setTimeout(() => {
                closeDeleteModal(true);
            }, 500);
        }

        // Update statistics
        async function updateStatistics() {
            try {
                // Get FAQ count
                const { data: faqs, error: faqError } = await supabase
                    .from('faq')
                    .select('id', { count: 'exact' });

                if (!faqError) {
                    document.getElementById('faqCount').textContent = faqs.length;
                }

                // Get Guidelines count
                const { data: guidelines, error: guidelinesError } = await supabase
                    .from('guidelines')
                    .select('id', { count: 'exact' });

                if (!guidelinesError) {
                    document.getElementById('guidelinesCount').textContent = guidelines.length;
                }

                // Get Tips count
                const { data: tips, error: tipsError } = await supabase
                    .from('tips')
                    .select('id', { count: 'exact' });

                if (!tipsError) {
                    document.getElementById('tipsCount').textContent = tips.length;
                }

                // Get Terms count
                const { data: terms, error: termsError } = await supabase
                    .from('terms_conditions')
                    .select('id', { count: 'exact' });

                if (!termsError) {
                    document.getElementById('termsCount').textContent = terms.length;
                }

            } catch (error) {
                console.error('Error updating statistics:', error);
            }
        }

        // Load FAQ
        async function loadFAQ() {
            const loading = document.getElementById('faqLoading');
            const content = document.getElementById('faqContent');

            loading.style.display = 'block';
            content.innerHTML = '';

            try {
                const { data: faqs, error } = await supabase
                    .from('faq')
                    .select('*')
                    .order('order_index', { ascending: true });

                if (error) throw error;

                loading.style.display = 'none';

                // Update statistics
                updateStatistics();

                if (faqs.length === 0) {
                    content.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-question-circle" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h3>لا توجد أسئلة شائعة</h3>
                            <p>ابدأ بإضافة أول سؤال شائع</p>
                        </div>
                    `;
                    return;
                }

                // Create FAQ table
                let html = `
                    <div style="margin-bottom: 1rem;">
                        <input type="text" id="faqSearch" placeholder="البحث في الأسئلة..."
                               style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                               onkeyup="filterFAQ()">
                    </div>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden;">
                            <thead style="background: #82877a; color: white;">
                                <tr>
                                    <th style="padding: 1rem; text-align: right;">السؤال</th>
                                    <th style="padding: 1rem; text-align: right;">الفئة</th>
                                    <th style="padding: 1rem; text-align: center;">الحالة</th>
                                    <th style="padding: 1rem; text-align: center;">الترتيب</th>
                                    <th style="padding: 1rem; text-align: center;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="faqTableBody">
                `;

                faqs.forEach(faq => {
                    html += `
                        <tr style="border-bottom: 1px solid #eee;" data-faq-id="${faq.id}">
                            <td style="padding: 1rem;">
                                <div style="font-weight: 600; margin-bottom: 0.5rem;">${faq.question}</div>
                                <div style="color: #666; font-size: 0.9rem;">${faq.answer.substring(0, 100)}...</div>
                            </td>
                            <td style="padding: 1rem;">
                                <span style="background: #f8f9fa; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                    ${faq.category}
                                </span>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <span style="background: ${faq.is_active ? '#28a745' : '#dc3545'}; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                    ${faq.is_active ? 'نشط' : 'غير نشط'}
                                </span>
                            </td>
                            <td style="padding: 1rem; text-align: center;">${faq.order_index}</td>
                            <td style="padding: 1rem; text-align: center;">
                                <button class="btn btn-secondary" style="margin: 0.2rem; padding: 0.5rem;" onclick="editFAQ('${faq.id}')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn ${faq.is_active ? 'btn-secondary' : 'btn-success'}" style="margin: 0.2rem; padding: 0.5rem;" onclick="toggleFAQStatus('${faq.id}', ${!faq.is_active})" title="${faq.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                                    <i class="fas fa-${faq.is_active ? 'eye-slash' : 'eye'}"></i>
                                </button>
                                <button class="btn btn-danger" style="margin: 0.2rem; padding: 0.5rem;" onclick="deleteFAQ('${faq.id}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                content.innerHTML = html;

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading FAQ:', error);
                showMessage('خطأ في تحميل الأسئلة الشائعة: ' + error.message, 'error');
            }
        }

        // Filter FAQ
        function filterFAQ() {
            const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
            const rows = document.querySelectorAll('#faqTableBody tr');

            rows.forEach(row => {
                const question = row.querySelector('td:first-child').textContent.toLowerCase();
                if (question.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Show add FAQ modal
        function showAddFaqModal() {
            const modal = createFAQModal();
            document.body.appendChild(modal);
        }

        // Create FAQ modal
        function createFAQModal(faq = null) {
            const isEdit = faq !== null;
            const modalId = 'faqModal';

            const modal = document.createElement('div');
            modal.id = modalId;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-question-circle"></i>
                            ${isEdit ? 'تعديل السؤال' : 'إضافة سؤال جديد'}
                        </h3>
                    </div>
                    <div style="padding: 2rem;">
                        <form id="faqForm">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">السؤال *</label>
                                <input type="text" id="faqQuestion" required
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? faq.question : ''}" placeholder="أدخل السؤال">
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الإجابة *</label>
                                <textarea id="faqAnswer" required rows="4"
                                          style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"
                                          placeholder="أدخل الإجابة">${isEdit ? faq.answer : ''}</textarea>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الفئة</label>
                                <select id="faqCategory"
                                        style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                    <option value="عام" ${isEdit && faq.category === 'عام' ? 'selected' : ''}>عام</option>
                                    <option value="منتجات" ${isEdit && faq.category === 'منتجات' ? 'selected' : ''}>منتجات</option>
                                    <option value="طلبات" ${isEdit && faq.category === 'طلبات' ? 'selected' : ''}>طلبات</option>
                                    <option value="توصيل" ${isEdit && faq.category === 'توصيل' ? 'selected' : ''}>توصيل</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ترتيب العرض</label>
                                <input type="number" id="faqOrder" min="0"
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? faq.order_index : 0}" placeholder="0">
                            </div>

                            <div style="margin-bottom: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="checkbox" id="faqActive" ${isEdit && faq.is_active ? 'checked' : 'checked'}>
                                    <span>نشط (يظهر للزوار)</span>
                                </label>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                                <button type="button" class="btn btn-secondary" onclick="closeFAQModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    ${isEdit ? 'تحديث' : 'إضافة'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Handle form submission
            modal.querySelector('#faqForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const question = document.getElementById('faqQuestion').value.trim();
                const answer = document.getElementById('faqAnswer').value.trim();
                const category = document.getElementById('faqCategory').value;
                const order_index = parseInt(document.getElementById('faqOrder').value) || 0;
                const is_active = document.getElementById('faqActive').checked;

                if (!question || !answer) {
                    showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                try {
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

                    if (isEdit) {
                        const { error } = await supabase
                            .from('faq')
                            .update({
                                question,
                                answer,
                                category,
                                order_index,
                                is_active,
                                updated_at: new Date().toISOString()
                            })
                            .eq('id', faq.id);

                        if (error) throw error;
                        showMessage('تم تحديث السؤال بنجاح!', 'success');
                    } else {
                        const { error } = await supabase
                            .from('faq')
                            .insert({
                                question,
                                answer,
                                category,
                                order_index,
                                is_active
                            });

                        if (error) throw error;
                        showMessage('تم إضافة السؤال بنجاح!', 'success');
                    }

                    closeFAQModal();
                    loadFAQ();

                } catch (error) {
                    console.error('Error saving FAQ:', error);
                    showMessage('خطأ في حفظ السؤال: ' + error.message, 'error');

                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = `<i class="fas fa-save"></i> ${isEdit ? 'تحديث' : 'إضافة'}`;
                }
            });

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeFAQModal();
                }
            });

            return modal;
        }

        // Close FAQ modal
        function closeFAQModal() {
            const modal = document.getElementById('faqModal');
            if (modal) {
                modal.remove();
            }
        }

        // Edit FAQ
        async function editFAQ(id) {
            try {
                const { data: faq, error } = await supabase
                    .from('faq')
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) throw error;

                const modal = createFAQModal(faq);
                document.body.appendChild(modal);

            } catch (error) {
                console.error('Error loading FAQ for edit:', error);
                showMessage('خطأ في تحميل السؤال للتعديل', 'error');
            }
        }

        // Toggle FAQ status
        async function toggleFAQStatus(id, newStatus) {
            try {
                const { error } = await supabase
                    .from('faq')
                    .update({
                        is_active: newStatus,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', id);

                if (error) throw error;

                showMessage(`تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} السؤال بنجاح!`, 'success');
                loadFAQ();

            } catch (error) {
                console.error('Error toggling FAQ status:', error);
                showMessage('خطأ في تغيير حالة السؤال', 'error');
            }
        }

        // Delete FAQ
        async function deleteFAQ(id) {
            try {
                // Get FAQ details for confirmation
                const { data: faq, error: fetchError } = await supabase
                    .from('faq')
                    .select('question')
                    .eq('id', id)
                    .single();

                if (fetchError) throw fetchError;

                // Show custom confirmation modal
                const confirmed = await showDeleteConfirmation({
                    message: 'هل أنت متأكد من حذف هذا السؤال؟',
                    itemName: faq.question
                });

                if (!confirmed) return;

                // Proceed with deletion
                const { error } = await supabase
                    .from('faq')
                    .delete()
                    .eq('id', id);

                if (error) throw error;

                showMessage('تم حذف السؤال بنجاح!', 'success');
                loadFAQ();

            } catch (error) {
                console.error('Error deleting FAQ:', error);
                showMessage('خطأ في حذف السؤال: ' + error.message, 'error');
            }
        }

        // Load Guidelines
        async function loadGuidelines() {
            const loading = document.getElementById('guidelinesLoading');
            const content = document.getElementById('guidelinesContent');

            loading.style.display = 'block';
            content.innerHTML = '';

            try {
                const { data: guidelines, error } = await supabase
                    .from('guidelines')
                    .select('*')
                    .order('order_index', { ascending: true });

                if (error) throw error;

                loading.style.display = 'none';

                if (guidelines.length === 0) {
                    content.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-lightbulb" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h3>لا توجد إرشادات</h3>
                            <p>ابدأ بإضافة أول إرشاد للاستخدام</p>
                        </div>
                    `;
                    return;
                }

                // Create guidelines grid
                let html = `
                    <div style="margin-bottom: 1rem;">
                        <input type="text" id="guidelinesSearch" placeholder="البحث في الإرشادات..."
                               style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                               onkeyup="filterGuidelines()">
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1.5rem;" id="guidelinesGrid">
                `;

                guidelines.forEach(guideline => {
                    html += `
                        <div class="guideline-card" data-guideline-id="${guideline.id}" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s;">
                            <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem;">
                                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                    <i class="${guideline.icon}" style="font-size: 2rem;"></i>
                                    <div>
                                        <h3 style="margin: 0; font-size: 1.1rem;">${guideline.title}</h3>
                                        <span style="background: rgba(255,255,255,0.2); padding: 0.2rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                            ${guideline.category}
                                        </span>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="background: ${guideline.is_active ? '#28a745' : '#dc3545'}; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                        ${guideline.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                    <span style="font-size: 0.9rem; opacity: 0.8;">ترتيب: ${guideline.order_index}</span>
                                </div>
                            </div>
                            <div style="padding: 1.5rem;">
                                <p style="color: #666; line-height: 1.6; margin-bottom: 1.5rem;">
                                    ${guideline.content.substring(0, 150)}${guideline.content.length > 150 ? '...' : ''}
                                </p>
                                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="editGuideline('${guideline.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </button>
                                    <button class="btn ${guideline.is_active ? 'btn-secondary' : 'btn-success'}" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="toggleGuidelineStatus('${guideline.id}', ${!guideline.is_active})" title="${guideline.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                                        <i class="fas fa-${guideline.is_active ? 'eye-slash' : 'eye'}"></i>
                                        ${guideline.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                                    </button>
                                    <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="deleteGuideline('${guideline.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
                content.innerHTML = html;

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading guidelines:', error);
                showMessage('خطأ في تحميل الإرشادات: ' + error.message, 'error');
            }
        }

        // Filter Guidelines
        function filterGuidelines() {
            const searchTerm = document.getElementById('guidelinesSearch').value.toLowerCase();
            const cards = document.querySelectorAll('.guideline-card');

            cards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const content = card.querySelector('p').textContent.toLowerCase();
                if (title.includes(searchTerm) || content.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Show add guideline modal
        function showAddGuidelineModal() {
            const modal = createGuidelineModal();
            document.body.appendChild(modal);
        }

        // Create Guideline modal
        function createGuidelineModal(guideline = null) {
            const isEdit = guideline !== null;
            const modalId = 'guidelineModal';

            const modal = document.createElement('div');
            modal.id = modalId;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-lightbulb"></i>
                            ${isEdit ? 'تعديل الإرشاد' : 'إضافة إرشاد جديد'}
                        </h3>
                    </div>
                    <div style="padding: 2rem;">
                        <form id="guidelineForm">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">عنوان الإرشاد *</label>
                                <input type="text" id="guidelineTitle" required
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? guideline.title : ''}" placeholder="أدخل عنوان الإرشاد">
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">محتوى الإرشاد *</label>
                                <textarea id="guidelineContent" required rows="6"
                                          style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"
                                          placeholder="أدخل محتوى الإرشاد بالتفصيل">${isEdit ? guideline.content : ''}</textarea>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الفئة</label>
                                    <select id="guidelineCategory"
                                            style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="عام" ${isEdit && guideline.category === 'عام' ? 'selected' : ''}>عام</option>
                                        <option value="العناية بالبشرة" ${isEdit && guideline.category === 'العناية بالبشرة' ? 'selected' : ''}>العناية بالبشرة</option>
                                        <option value="العناية بالشعر" ${isEdit && guideline.category === 'العناية بالشعر' ? 'selected' : ''}>العناية بالشعر</option>
                                    </select>
                                </div>

                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الأيقونة</label>
                                    <div class="icon-selector-container">
                                        <input type="hidden" id="guidelineIcon" value="${isEdit ? guideline.icon : 'fas fa-info-circle'}">
                                        <div class="icon-preview" onclick="window.openIconPicker('guideline')"
                                             style="width: 100%; padding: 1rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; cursor: pointer; display: flex; align-items: center; gap: 1rem; background: white; transition: all 0.3s ease;">
                                            <i id="guidelineIconPreview" class="${isEdit ? guideline.icon : 'fas fa-info-circle'}" style="font-size: 1.5rem; color: #4a90a4;"></i>
                                            <span id="guidelineIconText" style="flex: 1;">${isEdit ? getIconName(guideline.icon) : 'اختر أيقونة'}</span>
                                            <i class="fas fa-chevron-down" style="color: #666;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ترتيب العرض</label>
                                <input type="number" id="guidelineOrder" min="0"
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? guideline.order_index : 0}" placeholder="0">
                            </div>

                            <div style="margin-bottom: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="checkbox" id="guidelineActive" ${isEdit && guideline.is_active ? 'checked' : 'checked'}>
                                    <span>نشط (يظهر للزوار)</span>
                                </label>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                                <button type="button" class="btn btn-secondary" onclick="closeGuidelineModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    ${isEdit ? 'تحديث' : 'إضافة'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Handle form submission
            modal.querySelector('#guidelineForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const title = document.getElementById('guidelineTitle').value.trim();
                const content = document.getElementById('guidelineContent').value.trim();
                const category = document.getElementById('guidelineCategory').value;
                const icon = document.getElementById('guidelineIcon').value;
                const order_index = parseInt(document.getElementById('guidelineOrder').value) || 0;
                const is_active = document.getElementById('guidelineActive').checked;

                if (!title || !content) {
                    showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                try {
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

                    if (isEdit) {
                        const { error } = await supabase
                            .from('guidelines')
                            .update({
                                title,
                                content,
                                category,
                                icon,
                                order_index,
                                is_active,
                                updated_at: new Date().toISOString()
                            })
                            .eq('id', guideline.id);

                        if (error) throw error;
                        showMessage('تم تحديث الإرشاد بنجاح!', 'success');
                    } else {
                        const { error } = await supabase
                            .from('guidelines')
                            .insert({
                                title,
                                content,
                                category,
                                icon,
                                order_index,
                                is_active
                            });

                        if (error) throw error;
                        showMessage('تم إضافة الإرشاد بنجاح!', 'success');
                    }

                    closeGuidelineModal();
                    loadGuidelines();

                } catch (error) {
                    console.error('Error saving guideline:', error);
                    showMessage('خطأ في حفظ الإرشاد: ' + error.message, 'error');

                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = `<i class="fas fa-save"></i> ${isEdit ? 'تحديث' : 'إضافة'}`;
                }
            });

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeGuidelineModal();
                }
            });

            return modal;
        }

        // Close Guideline modal
        function closeGuidelineModal() {
            const modal = document.getElementById('guidelineModal');
            if (modal) {
                modal.remove();
            }
        }

        // Edit Guideline
        async function editGuideline(id) {
            try {
                const { data: guideline, error } = await supabase
                    .from('guidelines')
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) throw error;

                const modal = createGuidelineModal(guideline);
                document.body.appendChild(modal);

            } catch (error) {
                console.error('Error loading guideline for edit:', error);
                showMessage('خطأ في تحميل الإرشاد للتعديل', 'error');
            }
        }

        // Toggle Guideline status
        async function toggleGuidelineStatus(id, newStatus) {
            try {
                const { error } = await supabase
                    .from('guidelines')
                    .update({
                        is_active: newStatus,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', id);

                if (error) throw error;

                showMessage(`تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} الإرشاد بنجاح!`, 'success');
                loadGuidelines();

            } catch (error) {
                console.error('Error toggling guideline status:', error);
                showMessage('خطأ في تغيير حالة الإرشاد', 'error');
            }
        }

        // Delete Guideline
        async function deleteGuideline(id) {
            try {
                // Get guideline details for confirmation
                const { data: guideline, error: fetchError } = await supabase
                    .from('guidelines')
                    .select('title')
                    .eq('id', id)
                    .single();

                if (fetchError) throw fetchError;

                // Show custom confirmation modal
                const confirmed = await showDeleteConfirmation({
                    message: 'هل أنت متأكد من حذف هذا الإرشاد؟',
                    itemName: guideline.title
                });

                if (!confirmed) return;

                // Proceed with deletion
                const { error } = await supabase
                    .from('guidelines')
                    .delete()
                    .eq('id', id);

                if (error) throw error;

                showMessage('تم حذف الإرشاد بنجاح!', 'success');
                loadGuidelines();

            } catch (error) {
                console.error('Error deleting guideline:', error);
                showMessage('خطأ في حذف الإرشاد: ' + error.message, 'error');
            }
        }

        // Load Tips
        async function loadTips() {
            const loading = document.getElementById('tipsLoading');
            const content = document.getElementById('tipsContent');

            loading.style.display = 'block';
            content.innerHTML = '';

            try {
                const { data: tips, error } = await supabase
                    .from('tips')
                    .select('*')
                    .order('order_index', { ascending: true });

                if (error) throw error;

                loading.style.display = 'none';

                if (tips.length === 0) {
                    content.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-star" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h3>لا توجد نصائح إضافية</h3>
                            <p>ابدأ بإضافة أول نصيحة إضافية</p>
                        </div>
                    `;
                    return;
                }

                // Create tips grid
                let html = `
                    <div style="margin-bottom: 1rem;">
                        <input type="text" id="tipsSearch" placeholder="البحث في النصائح..."
                               style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                               onkeyup="filterTips()">
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1.5rem;" id="tipsGrid">
                `;

                tips.forEach(tip => {
                    html += `
                        <div class="tip-card" data-tip-id="${tip.id}" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s;">
                            <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem;">
                                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                    <i class="${tip.icon}" style="font-size: 2rem;"></i>
                                    <div>
                                        <h3 style="margin: 0; font-size: 1.1rem;">${tip.title}</h3>
                                        <span style="background: rgba(255,255,255,0.2); padding: 0.2rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                            ${tip.category}
                                        </span>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="background: ${tip.is_active ? '#28a745' : '#dc3545'}; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                        ${tip.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                    <span style="font-size: 0.9rem; opacity: 0.8;">ترتيب: ${tip.order_index}</span>
                                </div>
                            </div>
                            <div style="padding: 1.5rem;">
                                <p style="color: #666; line-height: 1.6; margin-bottom: 1.5rem;">
                                    ${tip.content.substring(0, 150)}${tip.content.length > 150 ? '...' : ''}
                                </p>
                                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="editTip('${tip.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </button>
                                    <button class="btn ${tip.is_active ? 'btn-secondary' : 'btn-success'}" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="toggleTipStatus('${tip.id}', ${!tip.is_active})" title="${tip.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                                        <i class="fas fa-${tip.is_active ? 'eye-slash' : 'eye'}"></i>
                                        ${tip.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                                    </button>
                                    <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="deleteTip('${tip.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
                content.innerHTML = html;

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading tips:', error);
                showMessage('خطأ في تحميل النصائح: ' + error.message, 'error');
            }
        }

        // Filter Tips
        function filterTips() {
            const searchTerm = document.getElementById('tipsSearch').value.toLowerCase();
            const cards = document.querySelectorAll('.tip-card');

            cards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const content = card.querySelector('p').textContent.toLowerCase();
                if (title.includes(searchTerm) || content.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Show add tip modal
        function showAddTipModal() {
            const modal = createTipModal();
            document.body.appendChild(modal);
        }

        // Create Tip modal
        function createTipModal(tip = null) {
            const isEdit = tip !== null;
            const modalId = 'tipModal';

            const modal = document.createElement('div');
            modal.id = modalId;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-star"></i>
                            ${isEdit ? 'تعديل النصيحة' : 'إضافة نصيحة جديدة'}
                        </h3>
                    </div>
                    <div style="padding: 2rem;">
                        <form id="tipForm">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">عنوان النصيحة *</label>
                                <input type="text" id="tipTitle" required
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? tip.title : ''}" placeholder="أدخل عنوان النصيحة">
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">محتوى النصيحة *</label>
                                <textarea id="tipContent" required rows="6"
                                          style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"
                                          placeholder="أدخل محتوى النصيحة بالتفصيل">${isEdit ? tip.content : ''}</textarea>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الفئة</label>
                                    <select id="tipCategory"
                                            style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="عام" ${isEdit && tip.category === 'عام' ? 'selected' : ''}>عام</option>
                                        <option value="البشرة الجافة" ${isEdit && tip.category === 'البشرة الجافة' ? 'selected' : ''}>البشرة الجافة</option>
                                        <option value="البشرة الدهنية" ${isEdit && tip.category === 'البشرة الدهنية' ? 'selected' : ''}>البشرة الدهنية</option>
                                        <option value="البشرة الحساسة" ${isEdit && tip.category === 'البشرة الحساسة' ? 'selected' : ''}>البشرة الحساسة</option>
                                        <option value="الشعر التالف" ${isEdit && tip.category === 'الشعر التالف' ? 'selected' : ''}>الشعر التالف</option>
                                        <option value="الشعر الدهني" ${isEdit && tip.category === 'الشعر الدهني' ? 'selected' : ''}>الشعر الدهني</option>
                                    </select>
                                </div>

                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الأيقونة</label>
                                    <div class="icon-selector-container">
                                        <input type="hidden" id="tipIcon" value="${isEdit ? tip.icon : 'fas fa-star'}">
                                        <div class="icon-preview" onclick="window.openIconPicker('tip')"
                                             style="width: 100%; padding: 1rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; cursor: pointer; display: flex; align-items: center; gap: 1rem; background: white; transition: all 0.3s ease;">
                                            <i id="tipIconPreview" class="${isEdit ? tip.icon : 'fas fa-star'}" style="font-size: 1.5rem; color: #4a90a4;"></i>
                                            <span id="tipIconText" style="flex: 1;">${isEdit ? getIconName(tip.icon) : 'اختر أيقونة'}</span>
                                            <i class="fas fa-chevron-down" style="color: #666;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ترتيب العرض</label>
                                <input type="number" id="tipOrder" min="0"
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? tip.order_index : 0}" placeholder="0">
                            </div>

                            <div style="margin-bottom: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="checkbox" id="tipActive" ${isEdit && tip.is_active ? 'checked' : 'checked'}>
                                    <span>نشط (يظهر للزوار)</span>
                                </label>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                                <button type="button" class="btn btn-secondary" onclick="closeTipModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    ${isEdit ? 'تحديث' : 'إضافة'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Handle form submission
            modal.querySelector('#tipForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const title = document.getElementById('tipTitle').value.trim();
                const content = document.getElementById('tipContent').value.trim();
                const category = document.getElementById('tipCategory').value;
                const icon = document.getElementById('tipIcon').value;
                const order_index = parseInt(document.getElementById('tipOrder').value) || 0;
                const is_active = document.getElementById('tipActive').checked;

                if (!title || !content) {
                    showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                try {
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

                    if (isEdit) {
                        const { error } = await supabase
                            .from('tips')
                            .update({
                                title,
                                content,
                                category,
                                icon,
                                order_index,
                                is_active,
                                updated_at: new Date().toISOString()
                            })
                            .eq('id', tip.id);

                        if (error) throw error;
                        showMessage('تم تحديث النصيحة بنجاح!', 'success');
                    } else {
                        const { error } = await supabase
                            .from('tips')
                            .insert({
                                title,
                                content,
                                category,
                                icon,
                                order_index,
                                is_active
                            });

                        if (error) throw error;
                        showMessage('تم إضافة النصيحة بنجاح!', 'success');
                    }

                    closeTipModal();
                    loadTips();

                } catch (error) {
                    console.error('Error saving tip:', error);
                    showMessage('خطأ في حفظ النصيحة: ' + error.message, 'error');

                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = `<i class="fas fa-save"></i> ${isEdit ? 'تحديث' : 'إضافة'}`;
                }
            });

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeTipModal();
                }
            });

            return modal;
        }

        // Close Tip modal
        function closeTipModal() {
            const modal = document.getElementById('tipModal');
            if (modal) {
                modal.remove();
            }
        }

        // Edit Tip
        async function editTip(id) {
            try {
                const { data: tip, error } = await supabase
                    .from('tips')
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) throw error;

                const modal = createTipModal(tip);
                document.body.appendChild(modal);

            } catch (error) {
                console.error('Error loading tip for edit:', error);
                showMessage('خطأ في تحميل النصيحة للتعديل', 'error');
            }
        }

        // Toggle Tip status
        async function toggleTipStatus(id, newStatus) {
            try {
                const { error } = await supabase
                    .from('tips')
                    .update({
                        is_active: newStatus,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', id);

                if (error) throw error;

                showMessage(`تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} النصيحة بنجاح!`, 'success');
                loadTips();

            } catch (error) {
                console.error('Error toggling tip status:', error);
                showMessage('خطأ في تغيير حالة النصيحة', 'error');
            }
        }

        // Delete Tip
        async function deleteTip(id) {
            try {
                // Get tip details for confirmation
                const { data: tip, error: fetchError } = await supabase
                    .from('tips')
                    .select('title')
                    .eq('id', id)
                    .single();

                if (fetchError) throw fetchError;

                // Show custom confirmation modal
                const confirmed = await showDeleteConfirmation({
                    message: 'هل أنت متأكد من حذف هذه النصيحة؟',
                    itemName: tip.title
                });

                if (!confirmed) return;

                // Proceed with deletion
                const { error } = await supabase
                    .from('tips')
                    .delete()
                    .eq('id', id);

                if (error) throw error;

                showMessage('تم حذف النصيحة بنجاح!', 'success');
                loadTips();

            } catch (error) {
                console.error('Error deleting tip:', error);
                showMessage('خطأ في حذف النصيحة: ' + error.message, 'error');
            }
        }

        // Load Terms & Conditions
        async function loadTerms() {
            const loading = document.getElementById('termsLoading');
            const content = document.getElementById('termsContent');

            loading.style.display = 'block';
            content.innerHTML = '';

            try {
                const { data: terms, error } = await supabase
                    .from('terms_conditions')
                    .select('*')
                    .order('order_index', { ascending: true });

                if (error) throw error;

                loading.style.display = 'none';

                if (terms.length === 0) {
                    content.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #666;">
                            <i class="fas fa-file-contract" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h3>لا توجد شروط وأحكام</h3>
                            <p>ابدأ بإضافة أول قسم للشروط والأحكام</p>
                        </div>
                    `;
                    return;
                }

                // Create terms grid
                let html = `
                    <div style="margin-bottom: 1rem;">
                        <input type="text" id="termsSearch" placeholder="البحث في الشروط والأحكام..."
                               style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                               onkeyup="filterTerms()">
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 1.5rem;" id="termsGrid">
                `;

                terms.forEach(term => {
                    html += `
                        <div class="term-card" data-term-id="${term.id}" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s;">
                            <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem;">
                                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                    <i class="fas fa-file-contract" style="font-size: 2rem;"></i>
                                    <div>
                                        <h3 style="margin: 0; font-size: 1.1rem;">${term.section_title}</h3>
                                        <span style="background: rgba(255,255,255,0.2); padding: 0.2rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                            ${term.section_type}
                                        </span>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="background: ${term.is_active ? '#28a745' : '#dc3545'}; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem;">
                                        ${term.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                    <span style="font-size: 0.9rem; opacity: 0.8;">ترتيب: ${term.order_index}</span>
                                </div>
                            </div>
                            <div style="padding: 1.5rem;">
                                <p style="color: #666; line-height: 1.6; margin-bottom: 1.5rem;">
                                    ${term.section_content.substring(0, 200)}${term.section_content.length > 200 ? '...' : ''}
                                </p>
                                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                    <button class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="editTerm('${term.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </button>
                                    <button class="btn ${term.is_active ? 'btn-secondary' : 'btn-success'}" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="toggleTermStatus('${term.id}', ${!term.is_active})" title="${term.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                                        <i class="fas fa-${term.is_active ? 'eye-slash' : 'eye'}"></i>
                                        ${term.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                                    </button>
                                    <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.9rem;" onclick="deleteTerm('${term.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
                content.innerHTML = html;

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading terms:', error);
                showMessage('خطأ في تحميل الشروط والأحكام: ' + error.message, 'error');
            }
        }

        // Filter Terms
        function filterTerms() {
            const searchTerm = document.getElementById('termsSearch').value.toLowerCase();
            const cards = document.querySelectorAll('.term-card');

            cards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const content = card.querySelector('p').textContent.toLowerCase();
                if (title.includes(searchTerm) || content.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Show add term modal
        function showAddTermModal() {
            const modal = createTermModal();
            document.body.appendChild(modal);
        }

        // Create Term modal
        function createTermModal(term = null) {
            const isEdit = term !== null;
            const modalId = 'termModal';

            const modal = document.createElement('div');
            modal.id = modalId;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 800px; max-height: 90vh; overflow-y: auto;">
                    <div style="background: linear-gradient(135deg, #82877a 0%, #6b7062 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-file-contract"></i>
                            ${isEdit ? 'تعديل قسم الشروط والأحكام' : 'إضافة قسم جديد للشروط والأحكام'}
                        </h3>
                    </div>
                    <div style="padding: 2rem;">
                        <form id="termForm">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">عنوان القسم *</label>
                                <input type="text" id="termTitle" required
                                       style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                       value="${isEdit ? term.section_title : ''}" placeholder="أدخل عنوان القسم">
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">محتوى القسم *</label>
                                <textarea id="termContent" required rows="8"
                                          style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"
                                          placeholder="أدخل محتوى القسم بالتفصيل">${isEdit ? term.section_content : ''}</textarea>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">نوع القسم</label>
                                    <select id="termType"
                                            style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="الشروط العامة" ${isEdit && term.section_type === 'الشروط العامة' ? 'selected' : ''}>الشروط العامة</option>
                                        <option value="الطلبات والدفع" ${isEdit && term.section_type === 'الطلبات والدفع' ? 'selected' : ''}>الطلبات والدفع</option>
                                        <option value="التوصيل" ${isEdit && term.section_type === 'التوصيل' ? 'selected' : ''}>التوصيل</option>
                                        <option value="الإرجاع والاستبدال" ${isEdit && term.section_type === 'الإرجاع والاستبدال' ? 'selected' : ''}>الإرجاع والاستبدال</option>
                                        <option value="سياسة الخصوصية" ${isEdit && term.section_type === 'سياسة الخصوصية' ? 'selected' : ''}>سياسة الخصوصية</option>
                                        <option value="معلومات التواصل" ${isEdit && term.section_type === 'معلومات التواصل' ? 'selected' : ''}>معلومات التواصل</option>
                                    </select>
                                </div>

                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ترتيب العرض</label>
                                    <input type="number" id="termOrder" min="0"
                                           style="width: 100%; padding: 0.8rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                                           value="${isEdit ? term.order_index : 0}" placeholder="0">
                                </div>
                            </div>

                            <div style="margin-bottom: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="checkbox" id="termActive" ${isEdit && term.is_active ? 'checked' : 'checked'}>
                                    <span>نشط (يظهر للزوار)</span>
                                </label>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                                <button type="button" class="btn btn-secondary" onclick="closeTermModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    ${isEdit ? 'تحديث' : 'إضافة'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Handle form submission
            modal.querySelector('#termForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const section_title = document.getElementById('termTitle').value.trim();
                const section_content = document.getElementById('termContent').value.trim();
                const section_type = document.getElementById('termType').value;
                const order_index = parseInt(document.getElementById('termOrder').value) || 0;
                const is_active = document.getElementById('termActive').checked;

                if (!section_title || !section_content) {
                    showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                try {
                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

                    if (isEdit) {
                        const { error } = await supabase
                            .from('terms_conditions')
                            .update({
                                section_title,
                                section_content,
                                section_type,
                                order_index,
                                is_active,
                                updated_at: new Date().toISOString()
                            })
                            .eq('id', term.id);

                        if (error) throw error;
                        showMessage('تم تحديث القسم بنجاح!', 'success');
                    } else {
                        const { error } = await supabase
                            .from('terms_conditions')
                            .insert({
                                section_title,
                                section_content,
                                section_type,
                                order_index,
                                is_active
                            });

                        if (error) throw error;
                        showMessage('تم إضافة القسم بنجاح!', 'success');
                    }

                    closeTermModal();
                    loadTerms();

                } catch (error) {
                    console.error('Error saving term:', error);
                    showMessage('خطأ في حفظ القسم: ' + error.message, 'error');

                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = `<i class="fas fa-save"></i> ${isEdit ? 'تحديث' : 'إضافة'}`;
                }
            });

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeTermModal();
                }
            });

            return modal;
        }

        // Close Term modal
        function closeTermModal() {
            const modal = document.getElementById('termModal');
            if (modal) {
                modal.remove();
            }
        }

        // Edit Term
        async function editTerm(id) {
            try {
                const { data: term, error } = await supabase
                    .from('terms_conditions')
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) throw error;

                const modal = createTermModal(term);
                document.body.appendChild(modal);

            } catch (error) {
                console.error('Error loading term for edit:', error);
                showMessage('خطأ في تحميل القسم للتعديل', 'error');
            }
        }

        // Toggle Term status
        async function toggleTermStatus(id, newStatus) {
            try {
                const { error } = await supabase
                    .from('terms_conditions')
                    .update({
                        is_active: newStatus,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', id);

                if (error) throw error;

                showMessage(`تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} القسم بنجاح!`, 'success');
                loadTerms();

            } catch (error) {
                console.error('Error toggling term status:', error);
                showMessage('خطأ في تغيير حالة القسم', 'error');
            }
        }

        // Delete Term
        async function deleteTerm(id) {
            try {
                // Get term details for confirmation
                const { data: term, error: fetchError } = await supabase
                    .from('terms_conditions')
                    .select('section_title')
                    .eq('id', id)
                    .single();

                if (fetchError) throw fetchError;

                // Show custom confirmation modal
                const confirmed = await showDeleteConfirmation({
                    message: 'هل أنت متأكد من حذف هذا القسم؟',
                    itemName: term.section_title
                });

                if (!confirmed) return;

                // Proceed with deletion
                const { error } = await supabase
                    .from('terms_conditions')
                    .delete()
                    .eq('id', id);

                if (error) throw error;

                showMessage('تم حذف القسم بنجاح!', 'success');
                loadTerms();

            } catch (error) {
                console.error('Error deleting term:', error);
                showMessage('خطأ في حذف القسم: ' + error.message, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                updateStatistics();
                loadFAQ();
            }
        });
    </script>

    <!-- Mobile Sidebar System -->
    <!-- Legacy mobile sidebar code removed - now using unified-sidebar-manager.js -->

    <!-- Unified Sidebar System -->
    <script src="js/unified-sidebar-manager.js"></script>

    <!-- Unified Notification System -->
    <script src="js/unified-notification-system.js"></script>

    <!-- Professional Logout System -->
    <script src="js/logout-system.js"></script>

    <!-- Icon Picker Modal -->
    <div id="iconPickerModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <h3 style="margin: 0; color: #4a90a4; font-family: 'Cairo', sans-serif;">
                    <i class="fas fa-icons" style="margin-left: 0.5rem;"></i>
                    اختيار الأيقونة
                </h3>
                <span class="close" onclick="window.closeIconPicker()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Search Box -->
                <div style="margin-bottom: 2rem;">
                    <input type="text" id="iconSearch" placeholder="البحث عن أيقونة..."
                           style="width: 100%; padding: 1rem; border: 2px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;"
                           oninput="window.filterIcons()">
                </div>

                <!-- Category Tabs -->
                <div class="icon-categories" style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
                    <button class="category-tab active" onclick="window.showIconCategory('all')" data-category="all">
                        <i class="fas fa-th" style="margin-left: 0.5rem;"></i>الكل
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('skincare')" data-category="skincare">
                        <i class="fas fa-spa" style="margin-left: 0.5rem;"></i>العناية بالبشرة
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('haircare')" data-category="haircare">
                        <i class="fas fa-cut" style="margin-left: 0.5rem;"></i>العناية بالشعر
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('beauty')" data-category="beauty">
                        <i class="fas fa-magic" style="margin-left: 0.5rem;"></i>التجميل
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('wellness')" data-category="wellness">
                        <i class="fas fa-heart" style="margin-left: 0.5rem;"></i>الصحة والعافية
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('tools')" data-category="tools">
                        <i class="fas fa-tools" style="margin-left: 0.5rem;"></i>الأدوات
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('services')" data-category="services">
                        <i class="fas fa-concierge-bell" style="margin-left: 0.5rem;"></i>الخدمات
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('categories')" data-category="categories">
                        <i class="fas fa-tags" style="margin-left: 0.5rem;"></i>الفئات
                    </button>
                    <button class="category-tab" onclick="window.showIconCategory('general')" data-category="general">
                        <i class="fas fa-star" style="margin-left: 0.5rem;"></i>عام
                    </button>
                </div>

                <!-- Icons Grid -->
                <div id="iconsGrid" class="icons-grid"></div>
            </div>
        </div>
    </div>

    <style>
        .icon-categories {
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 1rem;
        }

        .category-tab {
            padding: 0.8rem 1.5rem;
            border: 2px solid #ddd;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .category-tab:hover {
            border-color: #4a90a4;
            color: #4a90a4;
            transform: translateY(-2px);
        }

        .category-tab.active {
            background: #4a90a4;
            color: white;
            border-color: #4a90a4;
        }

        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 1rem;
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
        }

        .icon-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .icon-option:hover {
            border-color: #4a90a4;
            background: #f8fffe;
            transform: translateY(-2px);
        }

        .icon-option.selected {
            border-color: #4a90a4;
            background: #4a90a4;
            color: white;
        }

        .icon-option i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: inherit;
        }

        .icon-option span {
            font-size: 0.8rem;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        .icon-preview:hover {
            border-color: #4a90a4;
            background: #f8fffe;
        }

        /* Icon Picker Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            direction: rtl;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.7) translateY(-50px);
            transition: all 0.3s ease;
            overflow: hidden;
            direction: rtl;
        }

        .modal.show .modal-content {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .modal-header .close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .modal-header .close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-body {
            padding: 2rem;
        }
    </style>

    <script>
        // Initialize icon picker when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Icon picker initialized');

            // Count total icons
            let totalIcons = 0;
            for (const category in iconData) {
                totalIcons += iconData[category].length;
                console.log(`📂 Category ${category}: ${iconData[category].length} icons`);
            }
            console.log(`📊 Total icons available: ${totalIcons}`);

            // Test function to verify modal works
            window.testIconPicker = function() {
                console.log('Testing icon picker...');
                const modal = document.getElementById('iconPickerModal');
                if (modal) {
                    console.log('Modal found, opening...');
                    window.openIconPicker('test');
                } else {
                    console.error('Modal not found!');
                }
            };
        });

        // Icon data with categories
        const iconData = {
            skincare: [
                { icon: 'fas fa-spa', name: 'سبا' },
                { icon: 'fas fa-tint', name: 'ترطيب' },
                { icon: 'fas fa-soap', name: 'تنظيف' },
                { icon: 'fas fa-sun', name: 'واقي الشمس' },
                { icon: 'fas fa-snowflake', name: 'تبريد' },
                { icon: 'fas fa-fire', name: 'تدفئة' },
                { icon: 'fas fa-leaf', name: 'طبيعي' },
                { icon: 'fas fa-vial', name: 'سيروم' },
                { icon: 'fas fa-flask', name: 'مختبر' },
                { icon: 'fas fa-eye', name: 'العين' },
                { icon: 'fas fa-smile', name: 'الوجه' },
                { icon: 'fas fa-hand-paper', name: 'اليد' },
                { icon: 'fas fa-compress', name: 'ماسك' },
                { icon: 'fas fa-spray-can', name: 'بخاخ' },
                { icon: 'fas fa-pump-soap', name: 'مضخة' },
                { icon: 'fas fa-droplet', name: 'قطرة' },
                { icon: 'fas fa-water', name: 'ماء' },
                { icon: 'fas fa-glass-water', name: 'ماء نقي' },
                { icon: 'fas fa-bottle-water', name: 'ماء معبأ' },
                { icon: 'fas fa-oil-can', name: 'زيت' },
                { icon: 'fas fa-magic-wand-sparkles', name: 'سحري' },
                { icon: 'fas fa-star', name: 'نجمة' },
                { icon: 'fas fa-gem', name: 'جوهرة' },
                { icon: 'fas fa-certificate', name: 'شهادة' },
                { icon: 'fas fa-award', name: 'جائزة' },
                { icon: 'fas fa-crown', name: 'تاج' },
                { icon: 'fas fa-feather', name: 'ريشة' },
                { icon: 'fas fa-cloud', name: 'سحابة' },
                { icon: 'fas fa-moon', name: 'قمر' },
                { icon: 'fas fa-seedling', name: 'نبتة' },
                { icon: 'fas fa-cannabis', name: 'زهرة' },
                { icon: 'fas fa-heart', name: 'وردة' },
                { icon: 'fas fa-cannabis', name: 'خزامى' },
                { icon: 'fas fa-seedling', name: 'صبار' },
                { icon: 'fas fa-jar', name: 'عسل' },
                { icon: 'fas fa-jar', name: 'عسل طبيعي' },
                { icon: 'fas fa-lemon', name: 'ليمون' },
                { icon: 'fas fa-apple-whole', name: 'أفوكادو' },
                { icon: 'fas fa-carrot', name: 'خيار' },
                { icon: 'fas fa-mug-saucer', name: 'شاي' },
                { icon: 'fas fa-mug-saucer', name: 'قهوة' },
                { icon: 'fas fa-mortar-pestle', name: 'هاون' },
                { icon: 'fas fa-microscope', name: 'مجهر' },
                { icon: 'fas fa-vial', name: 'أنبوب اختبار' },
                { icon: 'fas fa-flask', name: 'كأس مختبر' },
                { icon: 'fas fa-thermometer', name: 'ميزان حرارة' },
                { icon: 'fas fa-clock', name: 'ساعة' },
                { icon: 'fas fa-stopwatch', name: 'مؤقت' },
                { icon: 'fas fa-hourglass', name: 'ساعة رملية' }
            ],
            haircare: [
                { icon: 'fas fa-cut', name: 'قص' },
                { icon: 'fas fa-shower', name: 'غسيل' },
                { icon: 'fas fa-oil-can', name: 'زيت' },
                { icon: 'fas fa-wind', name: 'تجفيف' },
                { icon: 'fas fa-magic-wand-sparkles', name: 'تصفيف' },
                { icon: 'fas fa-brush', name: 'فرشاة' },
                { icon: 'fas fa-grip-lines', name: 'مشط' },
                { icon: 'fas fa-fire', name: 'حرارة' },
                { icon: 'fas fa-snowflake', name: 'بارد' },
                { icon: 'fas fa-spray-can', name: 'سبراي' },
                { icon: 'fas fa-tint', name: 'ترطيب' },
                { icon: 'fas fa-leaf', name: 'طبيعي' },
                { icon: 'fas fa-vial', name: 'علاج' },
                { icon: 'fas fa-compress', name: 'ماسك' },
                { icon: 'fas fa-hand-sparkles', name: 'تدليك' },
                { icon: 'fas fa-scissors', name: 'مقص' },
                { icon: 'fas fa-plug', name: 'مجفف' },
                { icon: 'fas fa-fire-flame-curved', name: 'مكواة تجعيد' },
                { icon: 'fas fa-minus', name: 'مكواة فرد' },
                { icon: 'fas fa-paperclip', name: 'مشبك شعر' },
                { icon: 'fas fa-circle', name: 'رباط شعر' },
                { icon: 'fas fa-bottle-droplet', name: 'شامبو' },
                { icon: 'fas fa-pump-soap', name: 'شامبو بمضخة' },
                { icon: 'fas fa-bottle-water', name: 'بلسم' },
                { icon: 'fas fa-bottle-droplet', name: 'بلسم مرطب' },
                { icon: 'fas fa-mask', name: 'ماسك شعر' },
                { icon: 'fas fa-droplet', name: 'سيروم شعر' },
                { icon: 'fas fa-oil-can', name: 'زيت شعر' },
                { icon: 'fas fa-jar', name: 'كريم شعر' },
                { icon: 'fas fa-cube', name: 'جل شعر' },
                { icon: 'fas fa-square', name: 'شمع شعر' },
                { icon: 'fas fa-cloud', name: 'موس شعر' },
                { icon: 'fas fa-spray-can', name: 'سبراي تثبيت' },
                { icon: 'fas fa-heart-pulse', name: 'علاج شعر' },
                { icon: 'fas fa-hand-dots', name: 'تدليك فروة الرأس' },
                { icon: 'fas fa-arrow-up', name: 'نمو الشعر' },
                { icon: 'fas fa-arrow-down', name: 'تساقط الشعر' },
                { icon: 'fas fa-snowflake', name: 'قشرة الرأس' },
                { icon: 'fas fa-palette', name: 'صبغة شعر' },
                { icon: 'fas fa-sun', name: 'تفتيح شعر' },
                { icon: 'fas fa-strikethrough', name: 'خصل ملونة' },
                { icon: 'fas fa-rotate', name: 'تجعيد دائم' },
                { icon: 'fas fa-arrows-left-right', name: 'فرد الشعر' },
                { icon: 'fas fa-shield', name: 'كيراتين' },
                { icon: 'fas fa-dna', name: 'بروتين شعر' },
                { icon: 'fas fa-pills', name: 'فيتامينات شعر' },
                { icon: 'fas fa-building', name: 'صالون شعر' },
                { icon: 'fas fa-user-tie', name: 'مصفف شعر' }
            ],
            beauty: [
                { icon: 'fas fa-magic-wand-sparkles', name: 'سحر' },
                { icon: 'fas fa-star', name: 'نجمة' },
                { icon: 'fas fa-gem', name: 'جوهرة' },
                { icon: 'fas fa-crown', name: 'تاج' },
                { icon: 'fas fa-palette', name: 'ألوان' },
                { icon: 'fas fa-paint-brush', name: 'فرشاة' },
                { icon: 'fas fa-kiss', name: 'أحمر شفاه' },
                { icon: 'fas fa-mirror', name: 'مرآة' },
                { icon: 'fas fa-circle', name: 'بودرة' },
                { icon: 'fas fa-eye', name: 'مكياج العين' },
                { icon: 'fas fa-smile', name: 'ابتسامة' },
                { icon: 'fas fa-hand-sparkles', name: 'لمعان' },
                { icon: 'fas fa-certificate', name: 'جودة' },
                { icon: 'fas fa-award', name: 'جائزة' },
                { icon: 'fas fa-ribbon', name: 'شريط' },
                { icon: 'fas fa-brush', name: 'فرشاة مكياج' },
                { icon: 'fas fa-square', name: 'كريم أساس' },
                { icon: 'fas fa-pen', name: 'كونسيلر' },
                { icon: 'fas fa-circle-dot', name: 'بودرة' },
                { icon: 'fas fa-heart', name: 'أحمر خدود' },
                { icon: 'fas fa-sun', name: 'برونزر' },
                { icon: 'fas fa-highlighter', name: 'هايلايتر' },
                { icon: 'fas fa-mountain', name: 'كونتور' },
                { icon: 'fas fa-eye', name: 'ظلال عيون' },
                { icon: 'fas fa-pen-nib', name: 'كحل' },
                { icon: 'fas fa-brush', name: 'ماسكارا' },
                { icon: 'fas fa-minus', name: 'حواجب' },
                { icon: 'fas fa-droplet', name: 'ملمع شفاه' },
                { icon: 'fas fa-pencil', name: 'محدد شفاه' },
                { icon: 'fas fa-hand', name: 'طلاء أظافر' },
                { icon: 'fas fa-file', name: 'مبرد أظافر' },
                { icon: 'fas fa-cut', name: 'قصافة أظافر' },
                { icon: 'fas fa-grip-lines-vertical', name: 'ملقط' },
                { icon: 'fas fa-compress', name: 'مكبس رموش' },
                { icon: 'fas fa-circle', name: 'إسفنجة مكياج' },
                { icon: 'fas fa-egg', name: 'بيوتي بلندر' },
                { icon: 'fas fa-eraser', name: 'مزيل مكياج' },
                { icon: 'fas fa-oil-can', name: 'زيت تنظيف' },
                { icon: 'fas fa-droplet', name: 'ماء ميسيلار' },
                { icon: 'fas fa-tissue', name: 'مناديل مكياج' },
                { icon: 'fas fa-spray-can', name: 'سبراي تثبيت' },
                { icon: 'fas fa-layer-group', name: 'برايمر' },
                { icon: 'fas fa-feather', name: 'رموش صناعية' },
                { icon: 'fas fa-droplet', name: 'غراء رموش' },
                { icon: 'fas fa-th', name: 'باليت مكياج' },
                { icon: 'fas fa-suitcase', name: 'حقيبة مكياج' },
                { icon: 'fas fa-mirror', name: 'مرآة تجميل' },
                { icon: 'fas fa-lightbulb', name: 'مرآة LED' },
                { icon: 'fas fa-search-plus', name: 'مرآة مكبرة' },
                { icon: 'fas fa-hand-holding', name: 'مرآة يدوية' },
                { icon: 'fas fa-circle', name: 'مرآة مدورة' },
                { icon: 'fas fa-square', name: 'مرآة مربعة' },
                { icon: 'fas fa-tissue', name: 'مناديل مكياج' },
                { icon: 'fas fa-toilet-paper', name: 'مناديل تنظيف' },
                { icon: 'fas fa-hand-paper', name: 'مناديل مبللة' }
            ],
            wellness: [
                { icon: 'fas fa-heart', name: 'قلب' },
                { icon: 'fas fa-heartbeat', name: 'نبضات' },
                { icon: 'fas fa-apple-whole', name: 'تفاحة' },
                { icon: 'fas fa-carrot', name: 'جزر' },
                { icon: 'fas fa-glass-water', name: 'ماء' },
                { icon: 'fas fa-bed', name: 'نوم' },
                { icon: 'fas fa-moon', name: 'قمر' },
                { icon: 'fas fa-sun', name: 'شمس' },
                { icon: 'fas fa-dumbbell', name: 'رياضة' },
                { icon: 'fas fa-person-running', name: 'جري' },
                { icon: 'fas fa-om', name: 'تأمل' },
                { icon: 'fas fa-leaf', name: 'طبيعة' },
                { icon: 'fas fa-seedling', name: 'نمو' },
                { icon: 'fas fa-balance-scale', name: 'توازن' },
                { icon: 'fas fa-shield-halved', name: 'حماية' },
                { icon: 'fas fa-shield', name: 'حماية قوية' },
                { icon: 'fas fa-shield-virus', name: 'حماية متقدمة' },
                { icon: 'fas fa-spa', name: 'علاج سبا' },
                { icon: 'fas fa-hand-dots', name: 'تدليك' },
                { icon: 'fas fa-wind', name: 'علاج عطري' },
                { icon: 'fas fa-oil-can', name: 'زيوت عطرية' },
                { icon: 'fas fa-fire', name: 'شمعة' },
                { icon: 'fas fa-cloud', name: 'بخور' },
                { icon: 'fas fa-person-praying', name: 'يوغا' },
                { icon: 'fas fa-person-walking', name: 'بيلاتس' },
                { icon: 'fas fa-arrows-alt', name: 'تمدد' },
                { icon: 'fas fa-lungs', name: 'تنفس' },
                { icon: 'fas fa-brain', name: 'وعي' },
                { icon: 'fas fa-couch', name: 'استرخاء' },
                { icon: 'fas fa-hand-holding-heart', name: 'تخفيف التوتر' },
                { icon: 'fas fa-bolt', name: 'طاقة' },
                { icon: 'fas fa-star', name: 'حيوية' },
                { icon: 'fas fa-recycle', name: 'تطهير' },
                { icon: 'fas fa-broom', name: 'تنظيف' },
                { icon: 'fas fa-filter', name: 'تنقية' },
                { icon: 'fas fa-sync', name: 'انتعاش' },
                { icon: 'fas fa-redo', name: 'تجديد' },
                { icon: 'fas fa-heart-pulse', name: 'إحياء' },
                { icon: 'fas fa-clock', name: 'مكافحة الشيخوخة' },
                { icon: 'fas fa-dna', name: 'كولاجين' },
                { icon: 'fas fa-droplet', name: 'حمض الهيالورونيك' },
                { icon: 'fas fa-pills', name: 'فيتامين سي' },
                { icon: 'fas fa-capsules', name: 'ريتينول' },
                { icon: 'fas fa-atom', name: 'ببتيدات' },
                { icon: 'fas fa-shield', name: 'مضادات الأكسدة' },
                { icon: 'fas fa-tint', name: 'ترطيب' },
                { icon: 'fas fa-utensils', name: 'تغذية' },
                { icon: 'fas fa-tools', name: 'إصلاح' },
                { icon: 'fas fa-umbrella', name: 'حماية' },
                { icon: 'fas fa-lightbulb', name: 'إشراق' },
                { icon: 'fas fa-gem', name: 'نضارة' },
                { icon: 'fas fa-sun', name: 'إضاءة' }
            ],
            tools: [
                { icon: 'fas fa-tools', name: 'أدوات' },
                { icon: 'fas fa-wrench', name: 'مفتاح ربط' },
                { icon: 'fas fa-screwdriver', name: 'مفك' },
                { icon: 'fas fa-hammer', name: 'مطرقة' },
                { icon: 'fas fa-scissors', name: 'مقص' },
                { icon: 'fas fa-cut', name: 'موس حلاقة' },
                { icon: 'fas fa-grip-lines-vertical', name: 'ملقط' },
                { icon: 'fas fa-magnifying-glass', name: 'عدسة مكبرة' },
                { icon: 'fas fa-thermometer', name: 'ميزان حرارة' },
                { icon: 'fas fa-weight-scale', name: 'ميزان' },
                { icon: 'fas fa-ruler', name: 'مسطرة' },
                { icon: 'fas fa-mug-saucer', name: 'كوب قياس' },
                { icon: 'fas fa-eye-dropper', name: 'قطارة' },
                { icon: 'fas fa-syringe', name: 'حقنة' },
                { icon: 'fas fa-utensil-spoon', name: 'ملعقة' },
                { icon: 'fas fa-pen', name: 'أداة تطبيق' },
                { icon: 'fas fa-paint-roller', name: 'رولر' },
                { icon: 'fas fa-cloud', name: 'جهاز بخار' },
                { icon: 'fas fa-spray-can', name: 'موزع' },
                { icon: 'fas fa-droplet', name: 'مرطب' }
            ],
            services: [
                { icon: 'fas fa-concierge-bell', name: 'خدمة' },
                { icon: 'fas fa-handshake', name: 'اتفاق' },
                { icon: 'fas fa-user-tie', name: 'خبير' },
                { icon: 'fas fa-graduation-cap', name: 'تعليم' },
                { icon: 'fas fa-certificate', name: 'شهادة' },
                { icon: 'fas fa-medal', name: 'ميدالية' },
                { icon: 'fas fa-trophy', name: 'كأس' },
                { icon: 'fas fa-comments', name: 'استشارة' },
                { icon: 'fas fa-calendar', name: 'موعد' },
                { icon: 'fas fa-bookmark', name: 'حجز' },
                { icon: 'fas fa-truck', name: 'توصيل' },
                { icon: 'fas fa-shipping-fast', name: 'شحن' },
                { icon: 'fas fa-undo', name: 'إرجاع' },
                { icon: 'fas fa-shield-alt', name: 'ضمان' },
                { icon: 'fas fa-headset', name: 'دعم' },
                { icon: 'fas fa-question-circle', name: 'مساعدة' },
                { icon: 'fas fa-comment', name: 'محادثة' },
                { icon: 'fas fa-video', name: 'مكالمة فيديو' },
                { icon: 'fas fa-chalkboard-teacher', name: 'تدريب' },
                { icon: 'fas fa-cogs', name: 'ورشة عمل' }
            ],
            categories: [
                { icon: 'fas fa-tags', name: 'فئات' },
                { icon: 'fas fa-layer-group', name: 'مجموعات' },
                { icon: 'fas fa-boxes', name: 'صناديق' },
                { icon: 'fas fa-archive', name: 'أرشيف' },
                { icon: 'fas fa-folder', name: 'مجلد' },
                { icon: 'fas fa-file', name: 'ملف' },
                { icon: 'fas fa-list', name: 'قائمة' },
                { icon: 'fas fa-th', name: 'شبكة' },
                { icon: 'fas fa-grip-lines', name: 'شبكة أفقية' },
                { icon: 'fas fa-grip-lines-vertical', name: 'شبكة عمودية' },
                { icon: 'fas fa-table', name: 'شبكة كاملة' },
                { icon: 'fas fa-layer-group', name: 'مجموعة' },
                { icon: 'fas fa-cubes', name: 'حزمة' },
                { icon: 'fas fa-box', name: 'طرد' },
                { icon: 'fas fa-shipping-fast', name: 'مجموعة توصيل' },
                { icon: 'fas fa-gift', name: 'هدية' },
                { icon: 'fas fa-surprise', name: 'مفاجأة' },
                { icon: 'fas fa-star', name: 'خاص' },
                { icon: 'fas fa-crown', name: 'مميز' },
                { icon: 'fas fa-gem', name: 'فاخر' },
                { icon: 'fas fa-certificate', name: 'حصري' },
                { icon: 'fas fa-award', name: 'محدود' },
                { icon: 'fas fa-fire', name: 'الأكثر مبيعاً' },
                { icon: 'fas fa-chart-line', name: 'رائج' },
                { icon: 'fas fa-trophy', name: 'الأفضل' },
                { icon: 'fas fa-medal', name: 'متميز' },
                { icon: 'fas fa-ribbon', name: 'مختار' },
                { icon: 'fas fa-thumbs-up', name: 'موصى به' },
                { icon: 'fas fa-heart', name: 'المفضل' },
                { icon: 'fas fa-bolt', name: 'سريع البيع' }
            ],
            general: [
                { icon: 'fas fa-info-circle', name: 'معلومات' },
                { icon: 'fas fa-lightbulb', name: 'فكرة' },
                { icon: 'fas fa-check-circle', name: 'صحيح' },
                { icon: 'fas fa-times-circle', name: 'خطأ' },
                { icon: 'fas fa-exclamation-triangle', name: 'تحذير' },
                { icon: 'fas fa-question-circle', name: 'سؤال' },
                { icon: 'fas fa-thumbs-up', name: 'إعجاب' },
                { icon: 'fas fa-thumbs-down', name: 'عدم إعجاب' },
                { icon: 'fas fa-clock', name: 'وقت' },
                { icon: 'fas fa-calendar', name: 'تاريخ' },
                { icon: 'fas fa-map-marker-alt', name: 'موقع' },
                { icon: 'fas fa-phone', name: 'هاتف' },
                { icon: 'fas fa-envelope', name: 'بريد' },
                { icon: 'fas fa-home', name: 'منزل' },
                { icon: 'fas fa-user', name: 'مستخدم' }
            ]
        };

        let currentIconType = '';
        let selectedIcon = '';

        // Helper function to get icon name from icon class
        function getIconName(iconClass) {
            for (const category in iconData) {
                const icon = iconData[category].find(item => item.icon === iconClass);
                if (icon) {
                    return icon.name;
                }
            }
            return 'اختر أيقونة';
        }

        // Make functions available globally
        window.openIconPicker = function(type) {
            console.log('Opening icon picker for type:', type); // Debug log
            currentIconType = type;
            const modal = document.getElementById('iconPickerModal');
            const currentIcon = document.getElementById(type + 'Icon').value;
            selectedIcon = currentIcon;

            // Show modal with animation
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            showIconCategory('all');

            // Clear search
            document.getElementById('iconSearch').value = '';
        }

        window.closeIconPicker = function() {
            const modal = document.getElementById('iconPickerModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        window.showIconCategory = function(category) {
            // Update active tab
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // Show icons
            displayIcons(category);
        }

        window.displayIcons = function(category) {
            const grid = document.getElementById('iconsGrid');
            let icons = [];

            if (category === 'all') {
                // Combine all categories
                Object.values(iconData).forEach(categoryIcons => {
                    icons = icons.concat(categoryIcons);
                });
            } else {
                icons = iconData[category] || [];
            }

            console.log(`🎨 Displaying ${icons.length} icons for category: ${category}`);

            grid.innerHTML = icons.map(iconItem => `
                <div class="icon-option ${selectedIcon === iconItem.icon ? 'selected' : ''}"
                     onclick="window.selectIcon('${iconItem.icon}', '${iconItem.name}')">
                    <i class="${iconItem.icon}"></i>
                    <span>${iconItem.name}</span>
                </div>
            `).join('');

            // Log first few icons for debugging
            if (icons.length > 0) {
                console.log('📋 First 3 icons:', icons.slice(0, 3));
            }
        }

        window.selectIcon = function(iconClass, iconName) {
            selectedIcon = iconClass;

            // Update preview
            const preview = document.getElementById(currentIconType + 'IconPreview');
            const text = document.getElementById(currentIconType + 'IconText');
            const input = document.getElementById(currentIconType + 'Icon');

            preview.className = iconClass;
            text.textContent = iconName;
            input.value = iconClass;

            // Update selected state in grid
            document.querySelectorAll('.icon-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.target.closest('.icon-option').classList.add('selected');

            // Close modal after selection
            setTimeout(() => {
                window.closeIconPicker();
            }, 300);
        }

        window.filterIcons = function() {
            const searchTerm = document.getElementById('iconSearch').value.toLowerCase();
            const iconOptions = document.querySelectorAll('.icon-option');

            iconOptions.forEach(option => {
                const iconName = option.querySelector('span').textContent.toLowerCase();
                if (iconName.includes(searchTerm)) {
                    option.style.display = 'flex';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('iconPickerModal');
            if (event.target === modal) {
                window.closeIconPicker();
            }
        }
    </script>
</body>
</html>
