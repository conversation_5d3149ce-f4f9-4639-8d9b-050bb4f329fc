<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التناسق في الخطوط - Typography Consistency Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    
    <style>
        /* Basic styling for test page */
        :root {
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --spacing-xl: 2rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            border-right: 4px solid var(--primary-color);
        }

        .test-section h2 {
            color: var(--primary-dark);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }

        .typography-sample {
            margin: 1rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .size-label {
            font-size: var(--font-size-sm);
            color: var(--primary-color);
            font-weight: var(--font-weight-semibold);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .color-primary { color: var(--primary-color); }
        .color-secondary { color: var(--primary-dark); }
        .bg-primary { background: var(--primary-color); color: white; padding: 1rem; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <header class="test-section">
            <h1 class="standardized-h1 color-primary">اختبار التناسق في الخطوط</h1>
            <p class="standardized-body-lg">هذه الصفحة تختبر التناسق في أحجام الخطوط عبر جميع صفحات الموقع</p>
        </header>

        <!-- Typography Scale Test -->
        <section class="test-section">
            <h2 class="standardized-h2">مقياس الخطوط المعياري</h2>
            
            <div class="typography-sample">
                <div class="size-label">--font-size-5xl (3.5rem)</div>
                <h1 class="standardized-h1">عنوان الصفحة الرئيسي</h1>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-4xl (2.5rem)</div>
                <div style="font-size: var(--font-size-4xl); font-weight: var(--font-weight-bold);">عنوان القسم الكبير</div>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-3xl (2rem)</div>
                <h2 class="standardized-h2">عنوان القسم المتوسط</h2>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-2xl (1.5rem)</div>
                <h3 class="standardized-h3">عنوان فرعي</h3>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-xl (1.25rem)</div>
                <div class="standardized-body-lg">نص كبير للمقدمات والوصف المهم</div>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-base (1rem)</div>
                <p class="standardized-body">النص الأساسي للمحتوى العادي والفقرات الرئيسية</p>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-sm (0.875rem)</div>
                <div class="standardized-small">نص صغير للتفاصيل والملاحظات</div>
            </div>

            <div class="typography-sample">
                <div class="size-label">--font-size-xs (0.75rem)</div>
                <div class="standardized-xs">نص صغير جداً للتسميات والعدادات</div>
            </div>
        </section>

        <!-- Navigation Test -->
        <section class="test-section">
            <h2 class="standardized-h2">عناصر التنقل</h2>
            
            <div class="typography-sample">
                <div class="size-label">Logo</div>
                <div class="standardized-logo color-primary">Care</div>
            </div>

            <div class="typography-sample">
                <div class="size-label">Navigation Menu</div>
                <nav>
                    <ul style="list-style: none; display: flex; gap: 1rem;">
                        <li><a href="#" class="standardized-nav color-secondary">الرئيسية</a></li>
                        <li><a href="#" class="standardized-nav color-secondary">المنتجات</a></li>
                        <li><a href="#" class="standardized-nav color-secondary">العروض</a></li>
                        <li><a href="#" class="standardized-nav color-secondary">الإرشادات</a></li>
                    </ul>
                </nav>
            </div>
        </section>

        <!-- Button Test -->
        <section class="test-section">
            <h2 class="standardized-h2">الأزرار</h2>
            
            <div class="typography-sample">
                <div class="size-label">Standard Buttons</div>
                <button class="standardized-btn bg-primary" style="margin-left: 1rem; border: none; border-radius: 8px;">زر عادي</button>
                <button class="standardized-btn-lg bg-primary" style="margin-left: 1rem; border: none; border-radius: 8px;">زر كبير</button>
                <button class="standardized-btn-sm bg-primary" style="border: none; border-radius: 8px;">زر صغير</button>
            </div>
        </section>

        <!-- Icon Test -->
        <section class="test-section">
            <h2 class="standardized-h2">الأيقونات</h2>
            
            <div class="typography-sample">
                <div class="size-label">Icon Sizes</div>
                <i class="fas fa-heart standardized-icon-sm color-primary" style="margin-left: 1rem;"></i>
                <i class="fas fa-star standardized-icon-md color-primary" style="margin-left: 1rem;"></i>
                <i class="fas fa-shopping-cart standardized-icon-lg color-primary" style="margin-left: 1rem;"></i>
                <i class="fas fa-box standardized-icon-xl color-primary"></i>
            </div>
        </section>

        <!-- Product Card Test -->
        <section class="test-section">
            <h2 class="standardized-h2">بطاقة المنتج</h2>
            
            <div class="typography-sample">
                <h3 class="standardized-product-name color-secondary">اسم المنتج</h3>
                <p class="standardized-product-description">وصف المنتج يظهر هنا بالخط المعياري للوصف</p>
                <div class="standardized-product-price color-primary">25.000 د.ع</div>
                <span class="standardized-category-tag" style="background: #f8f9fa; padding: 0.3rem 0.8rem; border-radius: 15px;">فئة المنتج</span>
            </div>
        </section>

        <!-- Footer Test -->
        <section class="test-section">
            <h2 class="standardized-h2">التذييل</h2>
            
            <div class="typography-sample">
                <h3 class="standardized-footer-title color-secondary">عنوان قسم التذييل</h3>
                <p class="standardized-footer-text">نص التذييل العادي</p>
                <div style="margin-top: 1rem;">
                    <i class="fas fa-phone standardized-footer-icon color-primary" style="margin-left: 0.5rem;"></i>
                    <span class="standardized-footer-text">معلومات الاتصال</span>
                </div>
                <div style="margin-top: 1rem;">
                    <i class="fab fa-facebook standardized-social-icon color-primary" style="margin-left: 1rem;"></i>
                    <i class="fab fa-instagram standardized-social-icon color-primary" style="margin-left: 1rem;"></i>
                    <i class="fab fa-twitter standardized-social-icon color-primary"></i>
                </div>
            </div>
        </section>

        <!-- Notification Test -->
        <section class="test-section">
            <h2 class="standardized-h2">الإشعارات</h2>
            
            <div class="typography-sample">
                <div style="display: flex; align-items: flex-start; gap: 1rem;">
                    <i class="fas fa-check-circle standardized-notification-icon" style="color: #28a745;"></i>
                    <div>
                        <div class="standardized-notification-title">عنوان الإشعار</div>
                        <div class="standardized-notification-message">رسالة الإشعار تظهر هنا</div>
                    </div>
                </div>
            </div>
        </section>

        <footer class="test-section">
            <p class="standardized-body" style="text-align: center; color: var(--text-secondary);">
                تم إنشاء هذه الصفحة لاختبار التناسق في الخطوط عبر جميع صفحات الموقع
            </p>
        </footer>
    </div>
</body>
</html>
