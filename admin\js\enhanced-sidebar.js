/**
 * Enhanced Sidebar System for Care Admin Dashboard
 * نظام الشريط الجانبي المحسن للوحة التحكم الإدارية
 * 
 * Features:
 * - Desktop-only permanent sidebar
 * - RTL Arabic layout support
 * - Professional UI/UX standards
 */

class EnhancedSidebarManager {
    constructor() {
        this.sidebar = null;

        // Configuration for desktop-only sidebar
        this.config = {
            animations: {
                duration: 400,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        };

        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.findElements();
        this.setupEventListeners();
        this.setActiveNavigation();

        console.log('✅ Enhanced Sidebar Manager initialized (Desktop-only)');
    }
    
    findElements() {
        this.sidebar = document.getElementById('sidebar') || document.querySelector('.sidebar');

        if (!this.sidebar) {
            console.warn('⚠️ Sidebar element not found');
            return;
        }

        console.log('🔍 Sidebar elements found:', {
            sidebar: !!this.sidebar
        });
    }
    

    
    setupEventListeners() {
        // Desktop sidebar doesn't need mobile event listeners
        console.log('🔧 Desktop sidebar event listeners setup complete');
    }
    

    

    
    setActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar-link, .nav-item');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href.replace('./', ''))) {
                link.classList.add('active');
            }
        });
        
        // Default to dashboard if no match found
        if (!document.querySelector('.nav-item.active, .sidebar-link.active')) {
            const dashboardLink = document.querySelector('.nav-item[href*="dashboard"], .sidebar-link[href*="dashboard"]');
            if (dashboardLink) {
                dashboardLink.classList.add('active');
            }
        }
    }
    

}

// Initialize the enhanced sidebar manager
let enhancedSidebarManager;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        enhancedSidebarManager = new EnhancedSidebarManager();
    });
} else {
    enhancedSidebarManager = new EnhancedSidebarManager();
}

// Export for global access
window.EnhancedSidebarManager = EnhancedSidebarManager;
window.enhancedSidebarManager = enhancedSidebarManager;
