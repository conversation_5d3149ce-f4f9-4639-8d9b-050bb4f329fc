<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>تفاصيل المنتج - Care</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script defer src="https://cusdis.com/js/cusdis.es.js"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Standardized Header Design - Clean and Simple */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        /* Breadcrumb */
        .breadcrumb {
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }

        .breadcrumb-nav a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-nav a:hover {
            color: var(--primary-dark);
        }

        .breadcrumb-nav span {
            color: var(--text-secondary);
        }

        /* Product Details Section */
        .product-details {
            padding: var(--spacing-xxl) 0;
        }

        .product-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xxl);
            align-items: start;
        }

        /* Product Images */
        .product-images {
            position: sticky;
            top: 120px;
        }

        .main-image {
            width: 100%;
            height: 500px;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #e9ecef 100%);
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-5xl);
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .main-image:hover {
            transform: scale(1.02);
        }

        .main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-thumbnails {
            display: flex;
            gap: var(--spacing-md);
            justify-content: flex-start;
            overflow-x: auto;
            padding: var(--spacing-sm) 0;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-secondary);
        }

        .image-thumbnails::-webkit-scrollbar {
            height: 6px;
        }

        .image-thumbnails::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 3px;
        }

        .image-thumbnails::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            flex-shrink: 0;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .thumbnail.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.3);
        }

        .thumbnail:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .thumbnail i {
            font-size: var(--font-size-2xl);
            color: var(--primary-color);
        }

        /* Product Info */
        .product-info {
            padding: var(--spacing-lg);
        }

        .product-name {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.3;
        }

        .product-description {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
            line-height: 1.6;
        }

        .price-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: 15px;
        }

        .current-price {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }

        .original-price {
            font-size: var(--font-size-xl);
            color: var(--text-secondary);
            text-decoration: line-through;
            margin-left: var(--spacing-md);
        }

        .discount-badge {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            margin-top: var(--spacing-sm);
        }

        .availability {
            margin-top: var(--spacing-md);
            font-size: var(--font-size-lg);
            font-weight: 600;
        }

        .availability.in-stock {
            color: #27ae60;
        }

        .availability.out-of-stock {
            color: #e74c3c;
        }

        /* Quantity Section */
        .quantity-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: 15px;
        }

        .quantity-section label {
            display: block;
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 2px solid var(--primary-color);
            background: var(--bg-primary);
            color: var(--primary-color);
            border-radius: 10px;
            font-size: var(--font-size-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        #quantity {
            width: 80px;
            height: 40px;
            text-align: center;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-family: var(--font-family);
            font-size: var(--font-size-lg);
            font-weight: 600;
        }

        #quantity:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .add-to-cart-btn {
            flex: 2;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--spacing-lg);
            border-radius: 15px;
            font-family: var(--font-family);
            font-size: var(--font-size-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .add-to-cart-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .add-to-cart-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .back-btn {
            flex: 1;
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: var(--spacing-lg);
            border-radius: 15px;
            font-family: var(--font-family);
            font-size: var(--font-size-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .back-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Social Sharing */
        .social-sharing {
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: 15px;
            margin-bottom: var(--spacing-xl);
        }

        .social-sharing h3 {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .social-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
        }

        .social-btn {
            padding: var(--spacing-md);
            border: none;
            border-radius: 10px;
            font-family: var(--font-family);
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            min-height: 45px;
        }

        .whatsapp-btn {
            background: #25d366;
            color: white;
        }

        .whatsapp-btn:hover {
            background: #128c7e;
        }

        .facebook-btn {
            background: #1877f2;
            color: white;
        }

        .facebook-btn:hover {
            background: #166fe5;
        }

        .instagram-btn {
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            color: white;
        }

        .instagram-btn:hover {
            background: linear-gradient(45deg, #e6683c 0%, #dc2743 25%, #cc2366 50%, #bc1888 75%, #8b0a6b 100%);
        }

        .twitter-btn {
            background: #1da1f2;
            color: white;
        }

        .twitter-btn:hover {
            background: #0d8bd9;
        }

        .telegram-btn {
            background: #0088cc;
            color: white;
        }

        .telegram-btn:hover {
            background: #006699;
        }

        .linkedin-btn {
            background: #0077b5;
            color: white;
        }

        .linkedin-btn:hover {
            background: #005885;
        }

        .copy-btn {
            background: var(--text-secondary);
            color: white;
        }

        .copy-btn:hover {
            background: var(--text-primary);
        }

        /* Product Tabs */
        .product-tabs {
            margin-top: var(--spacing-xxl);
            background: var(--bg-primary);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .tab-buttons {
            display: flex;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .tab-btn {
            flex: 1;
            padding: var(--spacing-lg);
            background: transparent;
            border: none;
            font-family: var(--font-family);
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }

        .tab-btn:hover {
            color: var(--primary-color);
            background: var(--bg-primary);
        }

        .tab-content {
            padding: var(--spacing-xxl);
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .tab-panel h3 {
            font-size: var(--font-size-xl);
            font-weight: 700;
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
        }

        .tab-panel p {
            font-size: var(--font-size-base);
            line-height: 1.8;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
        }



        /* Related Products */
        .related-products {
            margin-top: var(--spacing-xxl);
            padding-top: var(--spacing-xxl);
            border-top: 1px solid var(--border-color);
        }

        .related-products {
            margin-top: 4rem;
            padding: 2rem 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            border: 1px solid rgba(74, 144, 164, 0.1);
        }

        .related-products h2 {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            margin-bottom: 3rem;
            color: #4a90a4;
            text-align: center;
            position: relative;
            font-family: 'Cairo', sans-serif;
        }

        .related-products h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            border-radius: 2px;
        }

        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            padding: 0 2rem;
        }

        .related-product {
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            transform: translateY(0);
        }

        .related-product::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.05) 0%, rgba(44, 62, 80, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .related-product:hover {
            transform: translateY(-12px);
            box-shadow: 0 20px 50px rgba(74, 144, 164, 0.15);
            border-color: #4a90a4;
        }

        .related-product:hover::before {
            opacity: 1;
        }

        .related-product-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-4xl);
            color: #4a90a4;
            position: relative;
            overflow: hidden;
            z-index: 2;
        }

        .related-product-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.8s ease;
            z-index: 3;
        }

        .related-product:hover .related-product-image::after {
            transform: translateX(100%);
        }

        .related-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
        }

        .related-product:hover .related-product-image img {
            transform: scale(1.08);
        }

        .related-product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: var(--font-size-xs);
            font-weight: 600;
            z-index: 4;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .related-product:hover .related-product-badge {
            opacity: 1;
            transform: translateY(0);
        }

        .related-product-info {
            padding: 1.5rem;
            position: relative;
            z-index: 2;
            background: #ffffff;
        }

        .related-product-name {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: #000000;
            margin-bottom: 0.8rem;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
            transition: color 0.3s ease;
        }

        .related-product:hover .related-product-name {
            color: #4a90a4;
        }

        .related-product-price {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .related-current-price {
            font-size: var(--font-size-xl);
            font-weight: 800;
            color: #4a90a4;
            font-family: 'Cairo', sans-serif;
        }

        .related-original-price {
            font-size: var(--font-size-base);
            color: #6c757d;
            text-decoration: line-through;
            font-weight: 500;
        }

        .related-discount-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: var(--font-size-xs);
            font-weight: 700;
        }

        .related-availability {
            font-size: var(--font-size-sm);
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .related-availability.available {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .related-availability.unavailable {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .related-product-actions {
            margin-top: 1rem;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .related-product:hover .related-product-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .related-view-btn {
            width: 100%;
            padding: 0.8rem;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .related-view-btn:hover {
            background: linear-gradient(135deg, #2c3e50, #4a90a4);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 164, 0.3);
        }

        /* Comments Section - Cusdis Only */
        .comments-section {
            margin: 4rem 0;
            padding: 2rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            border: 1px solid rgba(74, 144, 164, 0.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.05);
        }

        .comments-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .comments-header h3 {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 0.5rem;
            font-family: 'Cairo', sans-serif;
        }

        .comments-header p {
            color: #6c757d;
            font-size: var(--font-size-base);
            font-family: 'Cairo', sans-serif;
        }

        /* Cusdis Widget Styling */
        #cusdis_thread {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
            margin-top: 1rem;
            padding: 1rem;
            background: #ffffff;
            border-radius: 15px;
            border: 1px solid rgba(74, 144, 164, 0.1);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* Cusdis RTL Support */
        #cusdis_thread * {
            font-family: 'Cairo', sans-serif !important;
        }

        #cusdis_thread .cusdis-comment-form {
            direction: rtl;
            text-align: right;
        }

        #cusdis_thread .cusdis-comment-list {
            direction: rtl;
            text-align: right;
        }

        #cusdis_thread input,
        #cusdis_thread textarea {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif !important;
        }

        /* Loading and Error States */
        .loading {
            text-align: center;
            padding: var(--spacing-xxl);
            font-size: var(--font-size-lg);
            color: var(--primary-color);
        }

        .error {
            text-align: center;
            padding: var(--spacing-xxl);
        }

        .error-content {
            max-width: 500px;
            margin: 0 auto;
        }

        .error i {
            font-size: var(--font-size-5xl);
            color: #e74c3c;
            margin-bottom: var(--spacing-lg);
        }

        .error h2 {
            font-size: var(--font-size-2xl);
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .error p {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
        }

        /* Footer - Matching Homepage Design */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Notification System */
        .cart-notification {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(400px);
            transition: all 0.4s ease;
            max-width: 400px;
            width: calc(100% - 40px);
        }

        .cart-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-left: 5px solid #4a90a4;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-icon {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
            color: #000000;
        }

        .notification-title {
            font-size: var(--font-size-base);
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #4a90a4;
        }

        .notification-message {
            font-size: var(--font-size-sm);
            color: #000000;
            line-height: 1.4;
            margin-bottom: 0.8rem;
        }

        .notification-actions {
            display: flex;
            gap: 0.8rem;
        }

        .notification-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .notification-btn.primary {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
        }

        .notification-btn.secondary {
            background: rgba(74, 144, 164, 0.1);
            color: #4a90a4;
            border: 1px solid rgba(74, 144, 164, 0.3);
        }




    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>



                <nav>
                    <ul class="nav-menu" id="navMenu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>


    </header>

    <section class="breadcrumb">
        <div class="container">
            <nav class="breadcrumb-nav" aria-label="مسار التنقل">
                <a href="index.html">الرئيسية</a>
                <span>/</span>
                <a href="products.html">المنتجات</a>
                <span>/</span>
                <span id="productBreadcrumb">تفاصيل المنتج</span>
            </nav>
        </div>
    </section>

    <main class="product-details">
        <div class="container">
            <div id="loading" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل تفاصيل المنتج...
            </div>

            <div id="productContainer" class="product-container" style="display: none;">
                <div class="product-images">
                    <div class="main-image" id="mainImage" onclick="openImageZoom()">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="image-thumbnails" id="imageThumbnails">
                        <!-- Thumbnails will be loaded here -->
                    </div>
                </div>

                <div class="product-info">
                    <h1 class="product-name" id="productName">اسم المنتج</h1>
                    <p class="product-description" id="productDescription">وصف المنتج</p>

                    <div class="price-section">
                        <div class="current-price" id="currentPrice">0 د.ع</div>
                        <div id="priceDetails">
                            <!-- Original price and discount will be shown here -->
                        </div>
                        <div class="availability" id="availability">
                            <i class="fas fa-check-circle"></i> متوفر
                        </div>
                    </div>

                    <div class="quantity-section">
                        <label for="quantity">الكمية:</label>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                            <input type="number" id="quantity" value="1" min="1" max="10">
                            <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="add-to-cart-btn" id="addToCartBtn" onclick="addToCart()">
                            <i class="fas fa-shopping-cart"></i> إضافة إلى السلة
                        </button>
                        <button class="back-btn" onclick="window.history.back()">
                            <i class="fas fa-arrow-right"></i> العودة
                        </button>
                    </div>

                    <div class="social-sharing">
                        <h3>شارك المنتج</h3>
                        <div class="social-buttons">
                            <button class="social-btn whatsapp-btn" onclick="shareOnWhatsApp()">
                                <i class="fab fa-whatsapp"></i> واتساب
                            </button>
                            <button class="social-btn facebook-btn" onclick="shareOnFacebook()">
                                <i class="fab fa-facebook-f"></i> فيسبوك
                            </button>
                            <button class="social-btn instagram-btn" onclick="shareOnInstagram()">
                                <i class="fab fa-instagram"></i> انستغرام
                            </button>
                            <button class="social-btn twitter-btn" onclick="shareOnTwitter()">
                                <i class="fab fa-twitter"></i> تويتر
                            </button>
                            <button class="social-btn telegram-btn" onclick="shareOnTelegram()">
                                <i class="fab fa-telegram-plane"></i> تيليغرام
                            </button>
                            <button class="social-btn linkedin-btn" onclick="shareOnLinkedIn()">
                                <i class="fab fa-linkedin-in"></i> لينكد إن
                            </button>
                            <button class="social-btn copy-btn" onclick="copyProductLink()">
                                <i class="fas fa-link"></i> نسخ الرابط
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="product-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('description')">الوصف</button>
                    <button class="tab-btn" onclick="showTab('ingredients')">المكونات</button>
                    <button class="tab-btn" onclick="showTab('usage')">طريقة الاستخدام</button>
                </div>

                <div class="tab-content">
                    <div id="description-tab" class="tab-panel active">
                        <h3>وصف المنتج</h3>
                        <div id="productFullDescription">
                            <!-- Full description will be loaded here -->
                        </div>
                    </div>

                    <div id="ingredients-tab" class="tab-panel">
                        <h3>المكونات</h3>
                        <div id="productIngredients">
                            <!-- Ingredients will be loaded here -->
                        </div>
                    </div>

                    <div id="usage-tab" class="tab-panel">
                        <h3>طريقة الاستخدام</h3>
                        <div id="productUsage">
                            <!-- Usage instructions will be loaded here -->
                        </div>
                    </div>


                </div>
            </div>

            <!-- Comments Section -->
            <div id="commentsSection" class="comments-section" style="display: none;">
                <div class="comments-header">
                    <h3>آراء العملاء</h3>
                    <p>اقرأ تجارب العملاء الحقيقية مع هذا المنتج</p>
                </div>

                <!-- Cusdis Integration -->
                <div id="cusdis_thread"
                     data-host="https://cusdis.com"
                     data-app-id="37ca83b7-9127-40b1-a375-94f7c0f1ee5b"
                     data-page-id=""
                     data-page-url=""
                     data-page-title="">
                </div>
            </div>

            <div id="relatedSection" class="related-products" style="display: none;">
                <h2>منتجات ذات صلة</h2>
                <div class="related-grid" id="relatedGrid">
                    <!-- Related products will be loaded here -->
                </div>
            </div>

            <div id="errorSection" class="error" style="display: none;">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>عذراً، لم يتم العثور على المنتج</h2>
                    <p>المنتج المطلوب غير موجود أو تم حذفه</p>
                    <button class="back-btn" onclick="window.location.href='products.html'">
                        <i class="fas fa-arrow-right"></i> العودة إلى المنتجات
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="https://wa.me/9647713688302" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="انستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على انستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>روابط مهمة</h3>
                    <p><a href="guidelines.html"><i class="fas fa-book"></i>دليل الاستخدام</a></p>
                    <p><a href="faq.html"><i class="fas fa-question-circle"></i>الأسئلة الشائعة</a></p>
                    <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                    <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>


        // Get shared Supabase client
        function getSupabaseClient() {
            // Use shared configuration (singleton pattern)
            if (window.SupabaseConfig) {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            // Last resort: create client directly (should not happen if supabase-config.js is loaded)
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.warn('⚠️ Creating Supabase client directly in product-details.html - supabase-config.js may not be loaded');
                const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

                window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return window.globalSupabaseClient;
            }

            return null;
        }

        // Global variables
        let currentProduct = null;
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Get product ID from URL
        function getProductId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id');
        }

        // Cart functionality
        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price).replace('IQD', 'د.ع');
        }

        // Quantity controls
        function increaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            const currentValue = parseInt(quantityInput.value);
            const maxValue = parseInt(quantityInput.max) || 10;

            if (currentValue < maxValue) {
                quantityInput.value = currentValue + 1;
            }
        }

        function decreaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            const currentValue = parseInt(quantityInput.value);
            const minValue = parseInt(quantityInput.min) || 1;

            if (currentValue > minValue) {
                quantityInput.value = currentValue - 1;
            }
        }

        // Add to cart functionality
        function addToCart() {
            if (!currentProduct || !currentProduct.is_available) {
                return;
            }

            const quantity = parseInt(document.getElementById('quantity').value);
            const existingItem = cart.find(item => item.id === currentProduct.id);

            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                // Get the first available image from any of the three image fields
                const firstImage = currentProduct.image_url || currentProduct.image_url_2 || currentProduct.image_url_3;

                cart.push({
                    id: currentProduct.id,
                    name: currentProduct.name,
                    price: currentProduct.offer_price || currentProduct.price,
                    image_url: firstImage,
                    quantity: quantity
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();

            // Show notification
            const totalQuantity = existingItem ? existingItem.quantity : quantity;
            showCartNotification(currentProduct.name, totalQuantity);
        }

        // Tab functionality
        function showTab(tabName) {
            // Hide all tab panels
            const panels = document.querySelectorAll('.tab-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // Remove active class from all tab buttons
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(button => button.classList.remove('active'));

            // Show selected tab panel
            const selectedPanel = document.getElementById(tabName + '-tab');
            if (selectedPanel) {
                selectedPanel.classList.add('active');
            }

            // Add active class to clicked button
            const clickedButton = event.target;
            clickedButton.classList.add('active');
        }

        // Social sharing functions
        function shareOnWhatsApp() {
            if (!currentProduct) return;

            const text = `تحقق من هذا المنتج الرائع: ${currentProduct.name}\n${window.location.href}`;
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
        }

        function shareOnFacebook() {
            if (!currentProduct) return;

            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`;
            window.open(facebookUrl, '_blank');
        }

        function shareOnInstagram() {
            if (!currentProduct) return;

            // Instagram doesn't support direct URL sharing, so we copy the link and show instructions
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('تم نسخ الرابط! يمكنك الآن لصقه في منشور انستغرام جديد.');
            });
        }

        function shareOnTwitter() {
            if (!currentProduct) return;

            const text = `تحقق من هذا المنتج الرائع: ${currentProduct.name}`;
            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(window.location.href)}`;
            window.open(twitterUrl, '_blank');
        }

        function shareOnTelegram() {
            if (!currentProduct) return;

            const text = `تحقق من هذا المنتج الرائع: ${currentProduct.name}\n${window.location.href}`;
            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(text)}`;
            window.open(telegramUrl, '_blank');
        }

        function shareOnLinkedIn() {
            if (!currentProduct) return;

            const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
            window.open(linkedinUrl, '_blank');
        }

        function copyProductLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                // Show success message
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ!';
                copyBtn.style.background = '#27ae60';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = '';
                }, 2000);
            });
        }

        // Load product details
        async function loadProduct() {
            const productId = getProductId();

            if (!productId) {
                showError();
                return;
            }

            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    document.getElementById('loading').innerHTML = 'خطأ في الاتصال بقاعدة البيانات';
                    return;
                }

                const { data: product, error } = await supabase
                    .from('products')
                    .select('*')
                    .eq('id', productId)
                    .eq('is_active', true)
                    .single();

                if (error || !product) {
                    showError();
                    return;
                }

                currentProduct = product;
                displayProduct(product);
                loadRelatedProducts(product.product_type, product.id);

            } catch (error) {
                showError();
            }
        }

        function displayProduct(product) {
            // Hide loading and show product container
            document.getElementById('loading').style.display = 'none';
            document.getElementById('productContainer').style.display = 'grid';

            // Update page title and breadcrumb
            document.title = `${product.name} - Care`;
            document.getElementById('productBreadcrumb').textContent = product.name;

            // Update product information
            document.getElementById('productName').textContent = product.name;
            document.getElementById('productDescription').textContent = product.description || 'منتج عالي الجودة';

            // Update main image and thumbnails
            const mainImage = document.getElementById('mainImage');
            const imageThumbnails = document.getElementById('imageThumbnails');

            // Handle multiple images from separate database fields
            let productImages = [];

            // Collect all available image URLs from the three image fields
            if (product.image_url && product.image_url.trim()) {
                productImages.push(product.image_url.trim());
            }
            if (product.image_url_2 && product.image_url_2.trim()) {
                productImages.push(product.image_url_2.trim());
            }
            if (product.image_url_3 && product.image_url_3.trim()) {
                productImages.push(product.image_url_3.trim());
            }

            // Filter out any empty or invalid URLs
            productImages = productImages.filter(url => url && url.length > 0);

            if (productImages.length > 0) {
                // Set main image
                mainImage.innerHTML = `<img src="${productImages[0]}" alt="${product.name}" loading="lazy" id="currentMainImage">`;

                // Create thumbnails if there are multiple images
                if (productImages.length > 1) {
                    imageThumbnails.innerHTML = productImages.map((imageUrl, index) => `
                        <div class="thumbnail ${index === 0 ? 'active' : ''}" onclick="changeMainImage('${imageUrl}', ${index})">
                            <img src="${imageUrl}" alt="${product.name} - صورة ${index + 1}" loading="lazy">
                        </div>
                    `).join('');
                } else {
                    imageThumbnails.innerHTML = '';
                }
            } else {
                mainImage.innerHTML = '<i class="fas fa-box"></i>';
                imageThumbnails.innerHTML = '';
            }

            // Update price
            const hasOffer = product.is_on_offer && product.offer_price && product.offer_price < product.price;
            const currentPrice = document.getElementById('currentPrice');
            const priceDetails = document.getElementById('priceDetails');

            currentPrice.textContent = formatPrice(product.offer_price || product.price);

            if (hasOffer) {
                const discountPercentage = Math.round(((product.price - product.offer_price) / product.price) * 100);
                priceDetails.innerHTML = `
                    <span class="original-price">${formatPrice(product.price)}</span>
                    <span class="discount-badge">خصم ${discountPercentage}%</span>
                `;
            } else {
                priceDetails.innerHTML = '';
            }

            // Update availability
            const availability = document.getElementById('availability');
            const addToCartBtn = document.getElementById('addToCartBtn');

            if (product.is_available) {
                availability.innerHTML = '<i class="fas fa-check-circle"></i> متوفر';
                availability.className = 'availability in-stock';
                addToCartBtn.disabled = false;
                addToCartBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> إضافة إلى السلة';
            } else {
                availability.innerHTML = '<i class="fas fa-times-circle"></i> غير متوفر';
                availability.className = 'availability out-of-stock';
                addToCartBtn.disabled = true;
                addToCartBtn.innerHTML = '<i class="fas fa-ban"></i> غير متوفر';
            }

            // Update tab content with actual product data
            const fullDescription = product.details || product.description || 'منتج عالي الجودة من Care';
            document.getElementById('productFullDescription').innerHTML = `
                <div style="line-height: 1.8; color: #2c3e50;">
                    ${fullDescription.split('\n').map(line => `<p>${line.trim()}</p>`).join('')}
                </div>
            `;

            const ingredients = product.ingredients || 'مكونات طبيعية عالية الجودة';
            document.getElementById('productIngredients').innerHTML = `
                <div style="line-height: 1.8; color: #2c3e50;">
                    ${ingredients.split('\n').map(line => `<p>${line.trim()}</p>`).join('')}
                </div>
            `;

            const usageInstructions = product.usage_instructions || 'يُستخدم حسب التوجيهات المرفقة مع المنتج';
            document.getElementById('productUsage').innerHTML = `
                <div style="line-height: 1.8; color: #2c3e50;">
                    ${usageInstructions.split('\n').map(line => `<p>${line.trim()}</p>`).join('')}
                </div>
            `;

            // Show comments section
            document.getElementById('commentsSection').style.display = 'block';

            // Initialize Cusdis after a short delay
            setTimeout(() => {
                initializeCusdis();
            }, 500);
        }

        async function loadRelatedProducts(productType, currentProductId) {
            try {
                const supabase = getSupabaseClient();
                if (!supabase) return;

                const { data: relatedProducts, error } = await supabase
                    .from('products')
                    .select('id, name, price, offer_price, image_url, is_available')
                    .eq('product_type', productType)
                    .eq('is_active', true)
                    .neq('id', currentProductId)
                    .limit(4);

                if (error || !relatedProducts || relatedProducts.length === 0) {
                    return;
                }

                displayRelatedProducts(relatedProducts);

            } catch (error) {
                // Silently fail for related products
            }
        }

        function displayRelatedProducts(products) {
            const relatedSection = document.getElementById('relatedSection');
            const relatedGrid = document.getElementById('relatedGrid');

            relatedGrid.innerHTML = products.map(product => {
                const hasOffer = product.is_on_offer && product.offer_price && product.offer_price < product.price;
                const discountPercentage = hasOffer ? Math.round(((product.price - product.offer_price) / product.price) * 100) : 0;

                // Get the first available image for related products
                const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

                return `
                    <div class="related-product" onclick="window.location.href='product-details.html?id=${product.id}'">
                        <div class="related-product-image">
                            ${firstImage ?
                                `<img src="${firstImage}" alt="${product.name}" loading="lazy">` :
                                `<i class="fas fa-box"></i>`
                            }
                            ${hasOffer ? `<div class="related-product-badge">خصم ${discountPercentage}%</div>` : ''}
                        </div>
                        <div class="related-product-info">
                            <h4 class="related-product-name">${product.name}</h4>
                            <div class="related-product-price">
                                <span class="related-current-price">${formatPrice(product.offer_price || product.price)}</span>
                                ${hasOffer ? `<span class="related-original-price">${formatPrice(product.price)}</span>` : ''}
                                ${hasOffer ? `<span class="related-discount-badge">-${discountPercentage}%</span>` : ''}
                            </div>
                            <span class="related-availability ${product.is_available ? 'available' : 'unavailable'}">
                                <i class="fas ${product.is_available ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                ${product.is_available ? 'متوفر' : 'غير متوفر'}
                            </span>
                            <div class="related-product-actions">
                                <button class="related-view-btn">
                                    <i class="fas fa-eye"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            relatedSection.style.display = 'block';
        }

        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('errorSection').style.display = 'block';
        }

        function showCartNotification(productName, quantity) {
            // Remove any existing notifications
            const existingNotification = document.querySelector('.cart-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'cart-notification';
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'polite');

            notification.innerHTML = `
                <div class="notification-card">
                    <div class="notification-icon">
                        <i class="fas fa-check" aria-hidden="true"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">تمت الإضافة بنجاح!</div>
                        <div class="notification-message">
                            تم إضافة "${productName}" إلى السلة
                            ${quantity > 1 ? `(الكمية: ${quantity})` : ''}
                        </div>
                        <div class="notification-actions">
                            <a href="cart.html" class="notification-btn primary">
                                <i class="fas fa-shopping-cart"></i>
                                عرض السلة
                            </a>
                            <button class="notification-btn secondary" onclick="closeCartNotification()">
                                <i class="fas fa-shopping-bag"></i>
                                متابعة التسوق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Trigger show animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto close after 4 seconds
            setTimeout(() => {
                closeCartNotification();
            }, 4000);
        }

        function closeCartNotification() {
            const notification = document.querySelector('.cart-notification');
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 400);
            }
        }

        // Cusdis Comments Integration
        function initializeCusdis() {
            if (currentProduct) {
                const cusdisElement = document.getElementById('cusdis_thread');

                // Set page-specific attributes
                cusdisElement.setAttribute('data-page-id', `product-${currentProduct.id}`);
                cusdisElement.setAttribute('data-page-url', window.location.href);
                cusdisElement.setAttribute('data-page-title', currentProduct.name);

                // Add RTL language support
                cusdisElement.setAttribute('data-lang', 'ar');

                // Initialize Cusdis when script is loaded
                if (window.CUSDIS) {
                    window.CUSDIS.renderTo(cusdisElement);
                } else {
                    // Wait for Cusdis script to load
                    const checkCusdis = setInterval(() => {
                        if (window.CUSDIS) {
                            clearInterval(checkCusdis);
                            window.CUSDIS.renderTo(cusdisElement);
                        }
                    }, 100);

                    // Clear interval after 10 seconds to prevent infinite checking
                    setTimeout(() => clearInterval(checkCusdis), 10000);
                }
            }
        }

        // Initialize page
        function initializePage() {
            updateCartCount();

            // Check if Supabase is available
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                loadProduct();
            } else {
                setTimeout(initializePage, 100);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for site settings to load before loading product
            if (window.siteSettingsManager) {
                setTimeout(initializePage, 100);
            } else {
                window.addEventListener('siteSettingsLoaded', function() {
                    setTimeout(initializePage, 100);
                });

                // Fallback: load product after a delay if site settings don't load
                setTimeout(() => {
                    if (!currentProduct) {
                        initializePage();
                    }
                }, 3000);
            }

            // Quantity input validation
            const quantityInput = document.getElementById('quantity');
            quantityInput.addEventListener('input', function() {
                const value = parseInt(this.value);
                const min = parseInt(this.min) || 1;
                const max = parseInt(this.max) || 10;

                if (value < min) this.value = min;
                if (value > max) this.value = max;
            });

            // Tab button event listeners
            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabName = this.textContent.trim();
                    let tabId = '';

                    switch(tabName) {
                        case 'الوصف':
                            tabId = 'description';
                            break;
                        case 'المكونات':
                            tabId = 'ingredients';
                            break;
                        case 'طريقة الاستخدام':
                            tabId = 'usage';
                            break;
                    }

                    if (tabId) {
                        showTab(tabId);
                    }
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'b':
                            e.preventDefault();
                            window.history.back();
                            break;
                        case 'Enter':
                            e.preventDefault();
                            if (currentProduct && currentProduct.is_available) {
                                addToCart();
                            }
                            break;
                    }
                }
            });
        });

        // Change main image function
        function changeMainImage(imageUrl, index) {
            const mainImage = document.getElementById('currentMainImage');
            const thumbnails = document.querySelectorAll('.thumbnail');

            if (mainImage) {
                mainImage.src = imageUrl;
            }

            // Update active thumbnail
            thumbnails.forEach((thumb, i) => {
                if (i === index) {
                    thumb.classList.add('active');
                } else {
                    thumb.classList.remove('active');
                }
            });
        }

        // Image zoom functionality
        function openImageZoom() {
            const mainImage = document.getElementById('currentMainImage');
            if (mainImage && mainImage.src) {
                // Create zoom modal
                const zoomModal = document.createElement('div');
                zoomModal.className = 'image-zoom-modal';
                zoomModal.innerHTML = `
                    <div class="zoom-backdrop" onclick="closeImageZoom()"></div>
                    <div class="zoom-content">
                        <img src="${mainImage.src}" alt="صورة مكبرة">
                        <button class="zoom-close" onclick="closeImageZoom()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(zoomModal);
                document.body.style.overflow = 'hidden';

                // Add zoom modal styles
                if (!document.getElementById('zoom-styles')) {
                    const zoomStyles = document.createElement('style');
                    zoomStyles.id = 'zoom-styles';
                    zoomStyles.textContent = `
                        .image-zoom-modal {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            z-index: 10000;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .zoom-backdrop {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0.9);
                        }
                        .zoom-content {
                            position: relative;
                            max-width: 90%;
                            max-height: 90%;
                            z-index: 10001;
                        }
                        .zoom-content img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                            border-radius: 10px;
                        }
                        .zoom-close {
                            position: absolute;
                            top: -40px;
                            right: 0;
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            font-size: var(--font-size-2xl);
                            padding: 10px;
                            border-radius: 50%;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        }
                        .zoom-close:hover {
                            background: rgba(255,255,255,0.3);
                        }
                    `;
                    document.head.appendChild(zoomStyles);
                }
            }
        }

        function closeImageZoom() {
            const zoomModal = document.querySelector('.image-zoom-modal');
            if (zoomModal) {
                document.body.removeChild(zoomModal);
                document.body.style.overflow = '';
            }
        }

        // Handle browser back/forward navigation
        window.addEventListener('popstate', function() {
            const newProductId = getProductId();
            if (newProductId && (!currentProduct || currentProduct.id !== newProductId)) {
                loadProduct();
            }
        });
    </script>
</body>
</html>
