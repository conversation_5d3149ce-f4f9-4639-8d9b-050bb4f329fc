<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>إرشادات الاستخدام - Care</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;

            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #FFFFFF;
            color: #000000;
            line-height: 1.6;
            direction: rtl;
            padding-top: 80px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Header - Exactly Matching Homepage Design */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: var(--font-size-base);
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }



        /* Page Header - Professional Design System Compliant */
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            margin-bottom: 2rem;
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .page-header p {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Professional Guidelines Content */
        .guidelines-section {
            padding: 4rem 0;
            background: #ffffff;
        }

        .guidelines-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .guideline-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 6px 20px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(74, 144, 164, 0.1);
            position: relative;
            overflow: hidden;
        }

        .guideline-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4a90a4, #2c3e50);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .guideline-card:hover::before {
            transform: scaleX(1);
        }

        .guideline-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 32px rgba(0,0,0,0.12);
            border-color: rgba(74, 144, 164, 0.2);
        }

        .guideline-icon {
            font-size: var(--font-size-3xl);
            color: #4a90a4;
            margin-bottom: 2rem;
            text-align: center;
            transition: all 0.4s ease;
            display: block;
        }

        .guideline-card:hover .guideline-icon {
            transform: scale(1.15) rotate(5deg);
            color: #2c3e50;
            text-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }

        .guideline-card h3 {
            font-size: var(--font-size-2xl);
            color: #000000;
            margin-bottom: 2rem;
            font-weight: 800;
            text-align: center;
            font-family: 'Cairo', sans-serif;
            line-height: 1.3;
        }

        .guideline-card p {
            color: #000000;
            line-height: 1.7;
            margin-bottom: 2rem;
            font-size: var(--font-size-lg);
            text-align: center;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }

        .guideline-steps {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
            margin-top: 2rem;
        }

        .guideline-steps li {
            background: #ffffff;
            margin: var(--spacing-xl) 0;
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-xl) 5rem;
            border-radius: 10px;
            border-right: 4px solid var(--primary-color);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            counter-increment: step-counter;
        }

        .guideline-steps li:hover {
            transform: translateX(-12px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border-right-color: var(--primary-dark);
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.05), #ffffff);
        }

        .guideline-steps li::before {
            content: counter(step-counter);
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: var(--font-size-base);
            font-family: var(--font-family);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
            transition: all 0.3s ease;
            z-index: 1;
        }

        .guideline-steps li:hover::before {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 16px rgba(74, 144, 164, 0.4);
        }







        /* Warning Box */
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .warning-box h4 {
            color: #856404;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .warning-box p {
            color: #856404;
            line-height: 1.6;
        }



        /* Professional Category Filter Buttons */
        .category-filter-container {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.02), rgba(44, 62, 80, 0.02));
            border-radius: 16px;
        }

        .category-buttons-wrapper {
            display: inline-flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
        }

        .category-btn {
            background: #ffffff;
            color: #000000;
            border: 2px solid rgba(74, 144, 164, 0.2);
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: var(--font-size-base);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 120px;
        }

        .category-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .category-btn:hover::before {
            left: 100%;
        }

        .category-btn.active {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            border-color: #4a90a4;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(74, 144, 164, 0.3);
        }

        .category-btn:hover {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            border-color: #4a90a4;
            transform: translateY(-3px);
            box-shadow: 0 10px 24px rgba(74, 144, 164, 0.25);
        }



        /* Footer - Exact Match with Homepage */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
            font-family: 'Cairo', sans-serif;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Desktop-Only Optimizations */

        /* Professional Loading Animation */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(74, 144, 164, 0.2);
            border-top: 4px solid #4a90a4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Professional Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Staggered Animation for Cards */
        .guideline-card {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
        }

        .guideline-card:nth-child(1) { animation-delay: 0.1s; }
        .guideline-card:nth-child(2) { animation-delay: 0.2s; }
        .guideline-card:nth-child(3) { animation-delay: 0.3s; }
        .guideline-card:nth-child(4) { animation-delay: 0.4s; }
        .guideline-card:nth-child(5) { animation-delay: 0.5s; }
        .guideline-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Pulse Animation for Icons */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .guideline-icon {
            animation: pulse 3s ease-in-out infinite;
        }

        .guideline-card:hover .guideline-icon {
            animation: none;
        }

        /* Tips Section Styles */
        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .tip-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        }

        .tip-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .tip-header {
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .tip-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .tip-icon {
            font-size: var(--font-size-4xl);
            margin-bottom: 1rem;
            display: block;
        }

        .tip-title {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            font-size: var(--font-size-xl);
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .tip-category {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: var(--font-size-sm);
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .tip-content {
            padding: 2rem;
            font-family: 'Cairo', sans-serif;
            line-height: 1.8;
            color: #333;
            font-size: var(--font-size-base);
        }

        /* Professional Notification System */
        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border-left: 4px solid #4a90a4;
            padding: 1.5rem;
            transform: translateX(450px);
            transition: all 0.4s ease;
            font-family: 'Cairo', sans-serif;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left-color: #28a745;
        }

        .notification.error {
            border-left-color: #dc3545;
        }

        .notification.warning {
            border-left-color: #ffc107;
        }

        .notification .notification-content {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification .notification-icon {
            font-size: var(--font-size-2xl);
            color: #4a90a4;
        }

        .notification.success .notification-icon {
            color: #28a745;
        }

        .notification.error .notification-icon {
            color: #dc3545;
        }

        .notification.warning .notification-icon {
            color: #ffc107;
        }

        .notification .notification-text {
            flex: 1;
        }

        .notification .notification-title {
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #000000;
        }

        .notification .notification-message {
            color: #000000;
            line-height: 1.5;
        }

        /* Standardized Message Styles */
        .message-icon {
            font-size: var(--font-size-3xl);
            margin-bottom: 1rem;
            opacity: 0.3;
            color: #4a90a4;
        }

        .section-title {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            color: #000000;
            font-size: var(--font-size-4xl);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-family: 'Cairo', sans-serif;
            color: #666;
            font-size: var(--font-size-lg);
            max-width: 600px;
            margin: 0 auto;
        }

        .loading-icon {
            font-size: var(--font-size-3xl);
            margin-bottom: 1rem;
        }

        .error-icon {
            font-size: var(--font-size-3xl);
            margin-bottom: 1rem;
            color: #e74c3c;
        }

        .category-tag {
            background: #f8f9fa;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: var(--font-size-sm);
            color: #666;
        }





        /* Cross-Browser Compatibility and Accessibility Enhancements */

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .nav-menu {
                background: #000000;
                border: 2px solid #ffffff;
            }

            .nav-menu a {
                color: #ffffff;
                border-bottom: 1px solid #ffffff;
            }

            .nav-menu a:hover,
            .nav-menu a:focus {
                background: #ffffff;
                color: #000000;
            }



            .guideline-card {
                background: #000000;
                border: 2px solid #ffffff;
                color: #ffffff;
            }

            .category-btn {
                background: #000000;
                color: #ffffff;
                border: 2px solid #ffffff;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                -webkit-animation-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                -webkit-animation-iteration-count: 1 !important;
                animation-iteration-count: 1 !important;
                -webkit-transition-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }

            .nav-menu,
            .guideline-card,
            .category-btn {
                -webkit-transition: none !important;
                transition: none !important;
            }
        }



        /* Print styles */
        @media print {
            header,
            .nav-menu,
            .cart-icon,
            .category-filter-container,
            .warning-box {
                display: none;
            }

            .page-header {
                padding: 2rem 0;
                background: none;
                color: #000000;
            }

            .guidelines-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .guideline-card {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
                border: 1px solid #000000;
            }
        }
    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html" class="active">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount" aria-label="عدد المنتجات في السلة">0</span>
                </div>
            </div>
        </div>
    </header>

    <section class="page-header guidelines-bg" id="guidelinesPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">إرشادات الاستخدام</span>
                </nav>
                <h1 data-setting="guidelines_page_title">إرشادات الاستخدام</h1>
                <p class="guidelines-page-description" data-setting="guidelines_page_description">تعلم كيفية استخدام منتجات العناية بالبشرة والشعر بالطريقة الصحيحة</p>
            </div>
        </div>
    </section>

    <main class="guidelines-section">
        <div class="container">
            <!-- Enhanced Category Filter -->
            <div class="category-filter-container">
                <div class="category-buttons-wrapper">
                    <button class="category-btn active" onclick="filterGuidelines('all')">
                        <i class="fas fa-list-ul" style="margin-left: 0.5rem;"></i>
                        جميع الإرشادات
                    </button>
                    <button class="category-btn" onclick="filterGuidelines('عام')">
                        <i class="fas fa-info-circle" style="margin-left: 0.5rem;"></i>
                        عام
                    </button>
                    <button class="category-btn" onclick="filterGuidelines('العناية بالبشرة')">
                        <i class="fas fa-spa" style="margin-left: 0.5rem;"></i>
                        العناية بالبشرة
                    </button>
                    <button class="category-btn" onclick="filterGuidelines('العناية بالشعر')">
                        <i class="fas fa-cut" style="margin-left: 0.5rem;"></i>
                        العناية بالشعر
                    </button>
                </div>
            </div>

            <!-- Loading indicator -->
            <div id="guidelinesLoading" style="text-align: center; padding: 3rem; color: #4a90a4;">
                <div class="loading-spinner"></div>
                <p style="font-family: 'Cairo', sans-serif; font-weight: 600;">جاري تحميل الإرشادات...</p>
            </div>

            <!-- Guidelines content will be loaded here -->
            <div class="guidelines-grid" id="guidelinesContent"></div>

            <!-- No guidelines message -->
            <div id="noGuidelinesMessage" style="text-align: center; padding: 3rem; color: #000000; display: none;">
                <i class="fas fa-lightbulb message-icon"></i>
                <h3 style="font-family: 'Cairo', sans-serif; font-weight: 700;">لا توجد إرشادات متاحة حالياً</h3>
                <p style="font-family: 'Cairo', sans-serif;">نعمل على إضافة المزيد من الإرشادات المفيدة قريباً</p>
            </div>

            <!-- Additional Tips Section -->
            <div class="tips-section" style="margin-top: 4rem;">
                <div class="section-header" style="text-align: center; margin-bottom: 3rem;">
                    <h2 class="section-title">
                        <i class="fas fa-star" style="color: #4a90a4; margin-left: 1rem;"></i>
                        نصائح إضافية
                    </h2>
                    <p class="section-subtitle">
                        نصائح مفيدة ومتخصصة للعناية بالبشرة والشعر
                    </p>
                </div>

                <!-- Tips Loading -->
                <div id="tipsLoading" style="text-align: center; padding: 2rem; color: #4a90a4; display: none;">
                    <i class="fas fa-spinner fa-spin loading-icon"></i>
                    <p style="font-family: 'Cairo', sans-serif;">جاري تحميل النصائح الإضافية...</p>
                </div>

                <!-- Tips content will be loaded here -->
                <div class="tips-grid" id="tipsContent"></div>

                <!-- No tips message -->
                <div id="noTipsMessage" style="text-align: center; padding: 3rem; color: #000000; display: none;">
                    <i class="fas fa-star message-icon"></i>
                    <h3 style="font-family: 'Cairo', sans-serif; font-weight: 700;">لا توجد نصائح إضافية متاحة حالياً</h3>
                    <p style="font-family: 'Cairo', sans-serif;">نعمل على إضافة المزيد من النصائح المفيدة قريباً</p>
                </div>
            </div>



            <div class="warning-box">
                <h4>
                    <i class="fas fa-exclamation-triangle"></i>
                    تنبيه مهم
                </h4>
                <p>
                    إذا كنت تعاني من حساسية أو مشاكل جلدية، يُنصح بالتشاور مع طبيب الجلدية قبل استخدام أي منتج جديد.
                    كما يُنصح بإجراء اختبار الحساسية على منطقة صغيرة من الجلد قبل الاستخدام الكامل.
                </p>
            </div>
        </div>
    </main>



    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days">السبت - الخميس</span>: <span class="working-hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="https://wa.me/9647713688302" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Supabase Configuration Script (must be loaded first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>
        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        // Get shared Supabase client using singleton pattern
        function getSupabaseClient() {
            // Use the singleton pattern from supabase-config.js
            if (window.SupabaseConfig && typeof window.SupabaseConfig.getClient === 'function') {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global function if available
            if (window.getSupabaseClient && typeof window.getSupabaseClient === 'function') {
                return window.getSupabaseClient();
            }

            // Legacy fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            console.warn('⚠️ Supabase client not available. Make sure supabase-config.js is loaded.');
            return null;
        }

        let allGuidelines = [];
        let allTips = [];

        // Load guidelines from database
        async function loadGuidelines() {
            const loading = document.getElementById('guidelinesLoading');
            const content = document.getElementById('guidelinesContent');
            const noMessage = document.getElementById('noGuidelinesMessage');

            if (!loading || !content || !noMessage) {
                console.error('Required elements not found');
                return;
            }

            loading.style.display = 'block';
            content.innerHTML = '';
            noMessage.style.display = 'none';

            try {
                console.log('Attempting to load guidelines from Supabase...');

                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                // Try direct API call if Supabase client fails
                let guidelines, error;
                try {
                    const response = await supabase
                        .from('guidelines')
                        .select('*')
                        .eq('is_active', true)
                        .order('order_index', { ascending: true });
                    guidelines = response.data;
                    error = response.error;
                } catch (clientError) {
                    console.log('Supabase client failed, trying direct API call...');
                    const apiResponse = await fetch('https://krqijjttwllohulmdwgs.supabase.co/rest/v1/guidelines?is_active=eq.true&order=order_index.asc', {
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Content-Type': 'application/json'
                        }
                    });
                    if (apiResponse.ok) {
                        guidelines = await apiResponse.json();
                        error = null;
                    } else {
                        throw new Error(`API call failed: ${apiResponse.status}`);
                    }
                }

                console.log('Supabase response:', { data: guidelines, error });

                if (error) {
                    console.error('Supabase error:', error);
                    throw error;
                }

                loading.style.display = 'none';
                allGuidelines = guidelines || [];

                console.log(`Loaded ${allGuidelines.length} guidelines`);

                if (allGuidelines.length === 0) {
                    noMessage.style.display = 'block';
                    return;
                }

                displayGuidelines(allGuidelines);

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading guidelines:', error);
                noMessage.style.display = 'block';
                noMessage.innerHTML = `
                    <i class="fas fa-exclamation-triangle error-icon"></i>
                    <h3>خطأ في تحميل الإرشادات</h3>
                    <p>تفاصيل الخطأ: ${error.message}</p>
                    <button onclick="loadGuidelines()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #82877a; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        إعادة المحاولة
                    </button>
                `;
            }
        }

        // Display guidelines
        function displayGuidelines(guidelines) {
            const content = document.getElementById('guidelinesContent');
            let html = '';

            guidelines.forEach(guideline => {
                // Split content into paragraphs and create list items
                const contentParts = guideline.content.split(/[.\n]/).filter(part => part.trim());
                let contentHtml = '';

                if (contentParts.length > 1) {
                    contentHtml = '<ol class="guideline-steps">';
                    contentParts.forEach(part => {
                        if (part.trim()) {
                            contentHtml += `<li>${part.trim()}</li>`;
                        }
                    });
                    contentHtml += '</ol>';
                } else {
                    // Split by sentences if no line breaks
                    const sentences = guideline.content.split(/[.!؟]/).filter(s => s.trim());
                    if (sentences.length > 1) {
                        contentHtml = '<ol class="guideline-steps">';
                        sentences.forEach(sentence => {
                            if (sentence.trim()) {
                                contentHtml += `<li>${sentence.trim()}</li>`;
                            }
                        });
                        contentHtml += '</ol>';
                    } else {
                        contentHtml = `<p>${guideline.content}</p>`;
                    }
                }

                html += `
                    <div class="guideline-card" data-category="${guideline.category}">
                        <div class="guideline-icon">
                            <i class="${guideline.icon}"></i>
                        </div>
                        <h3>${guideline.title}</h3>
                        <div style="margin-bottom: 1rem;">
                            <span class="category-tag">
                                ${guideline.category}
                            </span>
                        </div>
                        ${contentHtml}
                    </div>
                `;
            });

            content.innerHTML = html;

            // Add fade-in animation to cards
            const cards = content.querySelectorAll('.guideline-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.classList.add('fade-in');
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // Load tips from database
        async function loadTips() {
            const loading = document.getElementById('tipsLoading');
            const content = document.getElementById('tipsContent');
            const noMessage = document.getElementById('noTipsMessage');

            if (!loading || !content || !noMessage) {
                console.error('Tips elements not found');
                return;
            }

            loading.style.display = 'block';
            content.innerHTML = '';
            noMessage.style.display = 'none';

            try {
                console.log('Attempting to load tips from Supabase...');

                const supabase = getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                // Try direct API call if Supabase client fails
                let tips, error;
                try {
                    const response = await supabase
                        .from('tips')
                        .select('*')
                        .eq('is_active', true)
                        .order('order_index', { ascending: true });
                    tips = response.data;
                    error = response.error;
                } catch (clientError) {
                    console.log('Supabase client failed, trying direct API call...');
                    const apiResponse = await fetch('https://krqijjttwllohulmdwgs.supabase.co/rest/v1/tips?is_active=eq.true&order=order_index.asc', {
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
                            'Content-Type': 'application/json'
                        }
                    });
                    if (apiResponse.ok) {
                        tips = await apiResponse.json();
                        error = null;
                    } else {
                        throw new Error(`API call failed: ${apiResponse.status}`);
                    }
                }

                console.log('Tips response:', { data: tips, error });

                if (error) {
                    console.error('Supabase error:', error);
                    throw error;
                }

                loading.style.display = 'none';
                allTips = tips || [];

                console.log(`Loaded ${allTips.length} tips`);

                if (allTips.length === 0) {
                    noMessage.style.display = 'block';
                    return;
                }

                displayTips(allTips);

            } catch (error) {
                loading.style.display = 'none';
                console.error('Error loading tips:', error);
                noMessage.style.display = 'block';
                noMessage.innerHTML = `
                    <i class="fas fa-exclamation-triangle error-icon"></i>
                    <h3>خطأ في تحميل النصائح الإضافية</h3>
                    <p>تفاصيل الخطأ: ${error.message}</p>
                    <button onclick="loadTips()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #4a90a4; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        إعادة المحاولة
                    </button>
                `;
            }
        }

        // Display tips
        function displayTips(tips) {
            const content = document.getElementById('tipsContent');
            let html = '';

            tips.forEach(tip => {
                html += `
                    <div class="tip-card">
                        <div class="tip-header">
                            <i class="${tip.icon} tip-icon"></i>
                            <div class="tip-title">${tip.title}</div>
                            <div class="tip-category">${tip.category}</div>
                        </div>
                        <div class="tip-content">
                            ${tip.content}
                        </div>
                    </div>
                `;
            });

            content.innerHTML = html;

            // Add fade-in animation to cards
            const cards = content.querySelectorAll('.tip-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.style.transition = 'all 0.6s ease';
                }, index * 150);
            });
        }

        // Filter guidelines by category
        function filterGuidelines(category) {
            const buttons = document.querySelectorAll('.category-btn');

            // Update active button with enhanced styling
            buttons.forEach(btn => {
                btn.classList.remove('active');
                // Remove inline styles to let CSS classes handle styling
                btn.style.background = '';
                btn.style.color = '';
                btn.style.border = '';
            });

            event.target.classList.add('active');

            // Filter guidelines
            let filteredGuidelines = allGuidelines;
            if (category !== 'all') {
                filteredGuidelines = allGuidelines.filter(guideline => guideline.category === category);
            }

            displayGuidelines(filteredGuidelines);
        }

        // Wait for site settings to load, then load guidelines
        function initializePage() {
            console.log('🔄 Guidelines page: Initializing page...');
            console.log('Site settings manager status:', window.siteSettingsManager);
            console.log('Supabase status:', window.supabase);

            // Check if site settings are already loaded
            if (window.siteSettingsManager && window.siteSettingsManager.loaded) {
                console.log('✅ Guidelines page: Site settings already loaded, loading content...');
                loadGuidelines();
                loadTips();
            } else if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.log('⏳ Guidelines page: Waiting for site settings to load...');
                // Listen for site settings loaded event
                window.addEventListener('siteSettingsLoaded', function(event) {
                    console.log('✅ Guidelines page: Site settings loaded event received!', event.detail);
                    loadGuidelines();
                    loadTips();
                }, { once: true });

                // Fallback: load content after delay if settings don't load
                setTimeout(() => {
                    if (!window.siteSettingsManager?.loaded) {
                        console.log('⚠️ Guidelines page: Loading content as fallback...');
                        loadGuidelines();
                        loadTips();
                    }
                }, 3000);
            } else {
                console.log('⏳ Guidelines page: Waiting for Supabase to load...');
                setTimeout(initializePage, 100);
            }
        }





        // Professional Notification System
        function showNotification(title, message, type = 'info', duration = 5000) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const iconMap = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <div class="notification-text">
                        <div class="notification-title">${title}</div>
                        <div class="notification-message">${message}</div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification with animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-hide notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 400);
            }, duration);
        }



        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
            initializePage();
        });
    </script>
</body>
</html>
