/**
 * Professional Logout Modal Styles for Care Admin Dashboard
 * أنماط نافذة تسجيل الخروج الاحترافية للوحة التحكم الإدارية
 */

/* Custom Logout Confirmation Modal */
.logout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.logout-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.logout-modal {
    background: white;
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.7) translateY(-50px);
    transition: all 0.3s ease;
    overflow: hidden;
    direction: rtl;
}

.logout-modal-overlay.show .logout-modal {
    transform: scale(1) translateY(0);
}

.logout-modal-header {
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
    position: relative;
}

.logout-modal-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    animation: fadeInScale 0.5s ease-in-out;
}

@keyframes fadeInScale {
    0% { 
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% { 
        transform: scale(1);
        opacity: 1;
    }
}

.logout-modal-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
}

.logout-modal-close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.logout-modal-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.logout-modal-body {
    padding: 2rem;
    text-align: center;
}

.logout-modal-message {
    font-size: 1.1rem;
    color: #333;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.logout-modal-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: #666;
    text-align: right;
}

.logout-modal-warning {
    font-size: 0.9rem;
    color: #856404;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 0.8rem;
    margin-top: 1rem;
    text-align: right;
}

.logout-modal-footer {
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    display: flex;
    gap: 1rem;
    justify-content: center;
    border-top: 1px solid #eee;
}

.logout-modal-btn {
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    min-width: 120px;
}

.logout-modal-btn-cancel {
    background: #6c757d;
    color: white;
}

.logout-modal-btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

.logout-modal-btn-confirm {
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.logout-modal-btn-confirm:hover {
    background: linear-gradient(135deg, #6b7062 0%, #5a5f54 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(130, 135, 122, 0.4);
}

.logout-modal-btn-confirm:active {
    transform: translateY(0);
}

.logout-modal-btn-confirm.loading {
    pointer-events: none;
    opacity: 0.8;
}

.logout-modal-btn-confirm.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



/* Focus styles for accessibility */
.logout-modal-btn:focus {
    outline: 2px solid #82877a;
    outline-offset: 2px;
}

.logout-modal-close:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}



/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .logout-modal-overlay,
    .logout-modal,
    .logout-modal-btn,
    .logout-modal-close {
        transition: none;
    }
    
    .logout-modal-icon {
        animation: none;
    }
    
    .logout-modal-btn-confirm.loading::after {
        animation: none;
    }
}
