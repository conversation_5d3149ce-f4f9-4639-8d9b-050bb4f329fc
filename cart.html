<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">

    <title>سلة التسوق - Care</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Header - Matching Homepage Design */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }



        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        /* Page Header - Professional Design System Compliant */
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            margin-bottom: 2rem;
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .page-description-enhanced p {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
            box-shadow: 0 10px 30px rgba(130, 135, 122, 0.4), 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-3px);
        }

        /* Cart Section */
        .cart-section {
            padding: 4rem 0;
            background: #f8f9fa;
            min-height: 60vh;
        }

        /* Cart Container - Professional Layout */
        .cart-container {
            display: grid;
            gap: 4rem;
            align-items: start;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Cart Items Container - Professional Design */
        .cart-items {
            background: white;
            border-radius: 16px;
            padding: 2.5rem;
            box-shadow:
                0 4px 20px rgba(0,0,0,0.08),
                0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .cart-items::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        /* Cart Header - Professional Design */
        .cart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2.5rem;
            gap: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f0f2f5;
        }

        .cart-items h2 {
            color: var(--primary-dark);
            font-size: var(--font-size-3xl);
            font-weight: 800;
            margin: 0;
            position: relative;
            letter-spacing: -0.01em;
        }

        .cart-items h2::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
        }

        /* Clear Cart Button - Enhanced */
        .clear-cart-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-family: var(--font-family);
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
            position: relative;
            overflow: hidden;
        }

        .clear-cart-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .clear-cart-btn:hover::before {
            left: 100%;
        }

        .clear-cart-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(231, 76, 60, 0.4);
        }

        .clear-cart-btn:disabled {
            background: linear-gradient(135deg, #ccc, #999);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            opacity: 0.6;
        }

        .clear-cart-btn:disabled::before {
            display: none;
        }

        /* Cart Item - Professional Card Design */
        .cart-item {
            display: grid;
            grid-template-columns: 120px 1fr auto;
            gap: 1.5rem;
            align-items: center;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background: #fafbfc;
            border-radius: 16px;
            border: 1px solid #e8ecef;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .cart-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .cart-item:last-child {
            margin-bottom: 0;
        }

        .cart-item:hover {
            background: white;
            box-shadow:
                0 8px 25px rgba(0,0,0,0.1),
                0 3px 10px rgba(0,0,0,0.08);
            transform: translateY(-2px);
            border-color: var(--primary-color);
        }

        .cart-item:hover::before {
            opacity: 1;
        }

        /* Product Image - Enhanced Design */
        .item-image {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            overflow: hidden;
            border: 2px solid #e8ecef;
            position: relative;
            transition: all 0.3s ease;
        }

        .item-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .item-image:hover::after {
            opacity: 1;
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 14px;
            transition: transform 0.3s ease;
        }

        .cart-item:hover .item-image {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.2);
        }

        .cart-item:hover .item-image img {
            transform: scale(1.05);
        }

        /* Product Information - Enhanced Typography */
        .item-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            min-width: 0;
            flex: 1;
        }

        .item-name {
            font-size: 1.25rem;
            color: #1a1a1a;
            font-weight: 700;
            line-height: 1.3;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            letter-spacing: -0.01em;
        }

        .item-details {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .item-price, .item-subtotal {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .price-label, .subtotal-label {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
            min-width: fit-content;
        }

        .price-value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-color);
            background: rgba(74, 144, 164, 0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            border: 1px solid rgba(74, 144, 164, 0.2);
        }

        .subtotal-value {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: var(--primary-dark);
            background: linear-gradient(135deg, rgba(44, 62, 80, 0.1), rgba(44, 62, 80, 0.05));
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            border: 1px solid rgba(44, 62, 80, 0.15);
        }

        /* Item Controls - Professional Design */
        .item-controls {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: flex-end;
            justify-content: center;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            border: 2px solid #e8ecef;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
        }

        .quantity-controls:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.15);
        }

        .quantity-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
            font-weight: 600;
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .quantity-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .quantity-btn:hover::before {
            left: 100%;
        }

        .quantity-btn:hover {
            background: linear-gradient(135deg, var(--primary-dark), #1a2332);
            transform: scale(1.05);
        }

        .quantity-btn:disabled {
            background: linear-gradient(135deg, #ccc, #999);
            cursor: not-allowed;
            transform: none;
        }

        .quantity-btn:disabled::before {
            display: none;
        }

        .quantity-display {
            padding: 0.75rem 1rem;
            background: #f8f9fa;
            border: none;
            text-align: center;
            font-weight: 700;
            min-width: 60px;
            font-size: 1.125rem;
            color: var(--primary-dark);
            letter-spacing: 0.5px;
        }

        /* Remove Button - Professional Design */
        .remove-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
            position: relative;
            overflow: hidden;
        }

        .remove-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .remove-btn:hover::before {
            left: 100%;
        }

        .remove-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .remove-btn:active {
            transform: translateY(0);
        }

        /* Empty Cart - Professional Design */
        .empty-cart {
            text-align: center;
            padding: 4rem 3rem;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 24px;
            border: 1px solid #e8ecef;
            margin: 2rem 0;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .empty-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        .empty-cart-content {
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .empty-cart-icon {
            margin-bottom: 2rem;
            position: relative;
        }

        .empty-cart-icon i {
            font-size: 6rem;
            color: var(--primary-color);
            opacity: 0.8;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .empty-cart h3 {
            color: var(--primary-dark);
            margin-bottom: 1.5rem;
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: -0.02em;
        }

        .empty-cart p {
            color: #6c757d;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            line-height: 1.6;
            font-weight: 400;
        }

        .empty-cart-subtitle {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 3rem !important;
            font-size: 1.125rem;
        }

        .empty-cart-actions {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            align-items: center;
        }

        /* Continue Shopping Button - Enhanced */
        .continue-shopping {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1.25rem 2.5rem;
            text-decoration: none;
            border-radius: 16px;
            font-weight: 700;
            font-size: 1.125rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 250px;
            justify-content: center;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .continue-shopping::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .continue-shopping:hover::before {
            left: 100%;
        }

        .continue-shopping.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: 0 6px 20px rgba(74, 144, 164, 0.4);
        }

        .continue-shopping.primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), #1a2332);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(74, 144, 164, 0.5);
        }

        /* Order Summary - Professional Design */
        .order-summary {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow:
                0 8px 30px rgba(0,0,0,0.12),
                0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid #e8ecef;
            position: sticky;
            top: 2rem;
            overflow: hidden;
            position: relative;
        }

        .order-summary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        .order-summary h2 {
            color: var(--primary-dark);
            margin-bottom: 2rem;
            font-size: 1.75rem;
            font-weight: 800;
            text-align: center;
            position: relative;
            padding-bottom: 1rem;
        }

        .order-summary h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
        }

        /* Summary Rows - Enhanced Design */
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            font-size: var(--font-size-base);
            background: var(--bg-secondary);
            border-radius: 10px;
            transition: all 0.3s ease;
            min-height: 60px;
        }

        .summary-row:hover {
            background: #f5f7fa;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .summary-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .summary-row .label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.4;
            flex: 1;
            text-align: right;
            padding-left: var(--spacing-md);
        }

        .summary-row .value {
            font-weight: 700;
            color: var(--primary-color);
            font-size: var(--font-size-lg);
            line-height: 1.4;
            text-align: left;
            white-space: nowrap;
        }

        .summary-row.total {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            border-radius: 15px;
            padding: var(--spacing-xl);
            margin-top: var(--spacing-xl);
            margin-bottom: 0;
            font-weight: 800;
            font-size: var(--font-size-xl);
            box-shadow: 0 6px 20px rgba(74, 144, 164, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 80px;
        }

        .summary-row.total::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.8s ease;
        }

        .summary-row.total:hover::before {
            left: 100%;
        }

        .summary-row.total .label,
        .summary-row.total .value {
            color: white;
            position: relative;
            z-index: 1;
            font-size: var(--font-size-xl);
            font-weight: 800;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .summary-row.total .label {
            flex: 1;
            text-align: right;
            padding-left: var(--spacing-md);
        }

        .summary-row.total .value {
            text-align: left;
            white-space: nowrap;
        }

        /* Customer Form - Professional Design */
        .customer-form {
            margin-top: 2.5rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 16px;
            border: 1px solid #e8ecef;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .customer-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        .customer-form h3 {
            color: var(--primary-dark);
            margin-bottom: 2rem;
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
            position: relative;
            padding-bottom: 1rem;
        }

        .customer-form h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        /* Enhanced Form Fields */
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: var(--spacing-lg) var(--spacing-xl);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            transition: all 0.3s ease;
            background: var(--bg-primary);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            direction: rtl;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow:
                0 0 0 3px rgba(74, 144, 164, 0.2),
                0 4px 20px rgba(74, 144, 164, 0.15);
            background: var(--bg-primary);
            transform: translateY(-2px);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: var(--text-secondary);
            font-weight: 400;
        }

        /* Enhanced Select Dropdown Styling */
        .form-group select {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234a90a4' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 1.2rem center;
            background-size: 1.4rem;
            padding-left: 3.5rem;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            cursor: pointer;
            position: relative;
        }

        .form-group select:hover {
            border-color: var(--primary-light);
            box-shadow: 0 4px 15px rgba(74, 144, 164, 0.12);
            transform: translateY(-1px);
        }

        .form-group select option {
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            font-weight: 500;
            direction: rtl;
            line-height: 1.5;
            border-bottom: 1px solid rgba(74, 144, 164, 0.1);
        }

        .form-group select option:hover,
        .form-group select option:checked {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        /* Enhanced Delivery Location Styling */
        #deliveryLocation {
            position: relative;
            font-weight: 600;
        }

        #deliveryLocation.enhanced-dropdown {
            background: linear-gradient(135deg, var(--bg-primary) 0%, #fafbfc 100%);
            border: 2px solid var(--border-color);
            box-shadow:
                0 2px 8px rgba(0,0,0,0.05),
                inset 0 1px 0 rgba(255,255,255,0.8);
        }

        #deliveryLocation.enhanced-dropdown:focus {
            background: var(--bg-primary);
            border-color: var(--primary-color);
            box-shadow:
                0 0 0 3px rgba(74, 144, 164, 0.2),
                0 4px 20px rgba(74, 144, 164, 0.15),
                inset 0 1px 0 rgba(255,255,255,0.8);
        }

        #deliveryLocation option[value=""] {
            color: var(--text-secondary);
            font-style: italic;
            font-weight: 400;
            background: #f8f9fa;
        }

        #deliveryLocation option:not([value=""]) {
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Improved option spacing and typography */
        #deliveryLocation option {
            padding: 12px 16px;
            margin: 2px 0;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        /* Professional dropdown container styling */
        .form-group:has(#deliveryLocation) {
            position: relative;
        }

        .form-group:has(#deliveryLocation)::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .form-group:has(#deliveryLocation:focus)::after {
            opacity: 1;
        }

        /* Enhanced label styling for delivery location */
        .form-group label[for="deliveryLocation"] {
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-group label[for="deliveryLocation"]::before {
            content: '📍';
            font-size: 1.1rem;
        }

        /* Smooth animations for dropdown interactions */
        #deliveryLocation {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #deliveryLocation:focus {
            animation: dropdownFocus 0.3s ease-out;
        }

        @keyframes dropdownFocus {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Enhanced visual feedback for selection */
        #deliveryLocation:valid:not(:focus) {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, var(--bg-primary) 100%);
        }

        #deliveryLocation:valid:not(:focus)::after {
            content: '✓';
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #28a745;
            font-weight: bold;
            pointer-events: none;
        }

        .form-group.required label::after {
            content: ' *';
            color: #e74c3c;
        }        /* Main Content Bottom Spacing */
        main {
            padding-bottom: var(--spacing-xxl);
            min-height: calc(100vh - 120px);
        }

        /* Discount Code Styling */
        .discount-input-container {
            display: flex;
            gap: 0.5rem;
            align-items: stretch;
        }

        .discount-input-container input {
            flex: 1;
        }

        .apply-discount-btn {
            background: linear-gradient(135deg, #82877a, #6b7062);
            color: white;
            border: none;
            padding: 1.2rem 1.5rem;
            border-radius: 15px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 80px;
        }

        .apply-discount-btn:hover {
            background: linear-gradient(135deg, #6b7062, #5a5f52);
            transform: translateY(-2px);
        }

        .apply-discount-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .remove-discount-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 1.2rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 80px;
        }

        .remove-discount-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
        }

        .remove-discount-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .discount-message {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            padding: 0.5rem;
            border-radius: 8px;
            text-align: center;
        }

        .discount-message.success {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .discount-message.error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .discount-message.info {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        /* Checkout Buttons Styling */
        .checkout-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        /* Checkout Buttons - Professional Design */
        .checkout-btn {
            padding: 1.25rem 2rem;
            border: none;
            border-radius: 16px;
            font-family: 'Cairo', sans-serif;
            font-size: 1.125rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .checkout-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .checkout-btn:hover::before {
            left: 100%;
        }

        .checkout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .checkout-btn:active {
            transform: translateY(-1px);
        }

        .checkout-btn.online-checkout {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: 0 6px 20px rgba(74, 144, 164, 0.4);
        }

        .checkout-btn.online-checkout:hover {
            background: linear-gradient(135deg, var(--primary-dark), #1a2332);
            box-shadow: 0 10px 30px rgba(74, 144, 164, 0.5);
            transform: translateY(-4px);
        }

        .checkout-btn.whatsapp-checkout {
            background: linear-gradient(135deg, #25d366, #128c7e);
            color: white;
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .checkout-btn.whatsapp-checkout:hover {
            background: linear-gradient(135deg, #128c7e, #075e54);
            box-shadow: 0 10px 30px rgba(37, 211, 102, 0.5);
            transform: translateY(-4px);
        }

        .checkout-btn:disabled {
            background: linear-gradient(135deg, #ccc, #999) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            opacity: 0.6;
        }

        .checkout-btn:disabled::before {
            display: none;
        }

        .whatsapp-note {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin: 0.5rem 0 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .whatsapp-note i {
            color: #82877a;
        }




















    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>



                <nav>
                    <ul class="nav-menu" id="navMenu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount" aria-label="عدد المنتجات في السلة">0</span>
                </div>
            </div>
        </div>


    </header>

    <section class="page-header">
        <div class="container">
            <h1>سلة التسوق</h1>
            <p class="cart-page-description" data-setting="cart_page_description">راجع منتجاتك المختارة واكمل عملية الشراء</p>
        </div>
    </section>

    <main class="cart-section">
        <div class="container">
            <!-- Professional Section Divider -->
            <div class="section-divider"></div>

            <div class="cart-container" id="cartContainer">
                <div class="cart-items">
                    <div class="cart-header">
                        <h2>المنتجات المختارة</h2>
                        <button class="clear-cart-btn" onclick="clearCartWithConfirmation()" title="مسح جميع المنتجات">
                            <i class="fas fa-trash-alt"></i>
                            مسح السلة
                        </button>
                    </div>
                    <div id="cartItemsList"></div>
                </div>

                <div class="order-summary">
                    <h2>ملخص الطلب</h2>
                    <div class="summary-row">
                        <span class="label">المجموع الفرعي:</span>
                        <span class="value" id="subtotal">0 د.ع</span>
                    </div>
                    <div class="summary-row" id="discountRow" style="display: none;">
                        <span class="label">الخصم:</span>
                        <span class="value" id="discountAmount">0 د.ع</span>
                    </div>
                    <div class="summary-row">
                        <span class="label">رسوم التوصيل:</span>
                        <span class="value" id="deliveryFee">مجاني</span>
                    </div>
                    <div class="summary-row total">
                        <span class="label">المجموع الكلي بعد الخصم:</span>
                        <span class="value" id="total">0 د.ع</span>
                    </div>

                    <div class="customer-form">
                        <h3>معلومات العميل</h3>
                        <form id="checkoutForm">
                            <div class="form-group required">
                                <label for="customerName">الاسم الكامل</label>
                                <input type="text" id="customerName" name="customerName" required>
                            </div>

                            <div class="form-group required">
                                <label for="customerPhone">رقم الهاتف</label>
                                <input type="tel" id="customerPhone" name="customerPhone" placeholder="07XXXXXXXX" required>
                            </div>

                            <div class="form-group required">
                                <label for="customerAddress">العنوان الكامل</label>
                                <textarea id="customerAddress" name="customerAddress" rows="3" required></textarea>
                            </div>

                            <div class="form-group required">
                                <label for="deliveryLocation">منطقة التوصيل</label>
                                <select id="deliveryLocation" name="deliveryLocation" required>
                                    <option value="">اختر المحافظة</option>
                                    <option value="baghdad">بغداد</option>
                                    <option value="anbar">الأنبار</option>
                                    <option value="babylon">بابل</option>
                                    <option value="basra">البصرة</option>
                                    <option value="dhi-qar">ذي قار</option>
                                    <option value="diyala">ديالى</option>
                                    <option value="dohuk">دهوك</option>
                                    <option value="erbil">أربيل</option>
                                    <option value="halabja">حلبجة</option>
                                    <option value="karbala">كربلاء</option>
                                    <option value="kirkuk">كركوك</option>
                                    <option value="maysan">ميسان</option>
                                    <option value="muthanna">المثنى</option>
                                    <option value="najaf">النجف</option>
                                    <option value="nineveh">نينوى</option>
                                    <option value="qadisiyyah">القادسية (الديوانية)</option>
                                    <option value="salah-al-din">صلاح الدين</option>
                                    <option value="sulaymaniyah">السليمانية</option>
                                    <option value="wasit">واسط</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="discountCode">كود الخصم (اختياري)</label>
                                <div class="discount-input-container">
                                    <input type="text" id="discountCode" name="discountCode" placeholder="أدخل كود الخصم">
                                    <button type="button" id="applyDiscountBtn" class="apply-discount-btn">تطبيق</button>
                                    <button type="button" id="removeDiscountBtn" class="remove-discount-btn" style="display: none;">إزالة</button>
                                </div>
                                <div id="discountMessage" class="discount-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="customerNotes">ملاحظات إضافية</label>
                                <textarea id="customerNotes" name="customerNotes" rows="2" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                            </div>

                            <div class="checkout-buttons">
                                <button type="button" class="checkout-btn online-checkout" id="onlineCheckoutBtn" disabled>
                                    <i class="fas fa-shopping-cart"></i>
                                    إتمام الطلب أونلاين
                                </button>

                                <button type="submit" class="checkout-btn whatsapp-checkout" id="whatsappCheckoutBtn" disabled>
                                    <i class="fab fa-whatsapp"></i>
                                    إتمام الطلب عبر واتساب
                                </button>

                                <p class="whatsapp-note">
                                    <i class="fas fa-info-circle"></i>
                                    يجب أن يحتوي الرقم على واتساب
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="empty-cart" id="emptyCart" style="display: none;">
                <div class="empty-cart-content">
                    <div class="empty-cart-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3>سلة التسوق فارغة</h3>
                    <p>لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
                    <p class="empty-cart-subtitle">اكتشف مجموعتنا الواسعة من منتجات العناية</p>

                    <div class="empty-cart-actions">
                        <a href="products.html" class="continue-shopping primary">
                            <i class="fas fa-box"></i>
                            تصفح جميع المنتجات
                        </a>
                        <a href="offers.html" class="continue-shopping secondary">
                            <i class="fas fa-tags"></i>
                            العروض الخاصة
                        </a>
                        <a href="index.html" class="continue-shopping tertiary">
                            <i class="fas fa-home"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <script>
        // ===== MOBILE NAVIGATION FUNCTIONALITY =====
        function toggleMobileMenu() {
            const navMenu = document.getElementById('navMenu');
            const mobileOverlay = document.getElementById('mobileOverlay');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            navMenu.classList.toggle('active');
            mobileOverlay.classList.toggle('active');

            // Update button icon
            const icon = mobileMenuBtn.querySelector('i');
            if (navMenu.classList.contains('active')) {
                icon.className = 'fas fa-times';
                mobileMenuBtn.setAttribute('aria-label', 'إغلاق القائمة');
                // Prevent body scroll when menu is open
                document.body.style.overflow = 'hidden';
            } else {
                icon.className = 'fas fa-bars';
                mobileMenuBtn.setAttribute('aria-label', 'فتح القائمة');
                // Restore body scroll
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const navMenu = document.getElementById('navMenu');
            const mobileOverlay = document.getElementById('mobileOverlay');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            navMenu.classList.remove('active');
            mobileOverlay.classList.remove('active');

            // Reset button icon
            const icon = mobileMenuBtn.querySelector('i');
            icon.className = 'fas fa-bars';
            mobileMenuBtn.setAttribute('aria-label', 'فتح القائمة');

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Close mobile menu when clicking on navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-menu a');
            navLinks.forEach(link => {
                link.addEventListener('click', closeMobileMenu);
            });

            // Close mobile menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeMobileMenu();
                }
            });

            // Close mobile menu on window resize to desktop
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    closeMobileMenu();
                }
            });
        });

        // Get Supabase client using shared configuration (singleton pattern)
        function getSupabaseClient() {
            // Use shared configuration if available
            if (window.SupabaseConfig) {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            // Last resort: create client directly (should not happen if supabase-config.js is loaded)
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.warn('⚠️ Creating Supabase client directly in cart.html - supabase-config.js may not be loaded');
                const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
                window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return window.globalSupabaseClient;
            }

            console.error('خطأ في تهيئة Supabase: المكتبة غير متاحة');
            return null;
        }

        // Initialize Supabase client
        const supabase = getSupabaseClient();



        // Global variables
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        let deliveryFee = 0;
        let discountAmount = 0;
        let appliedDiscountCode = null;
        let deliveryPricingData = [];
        let discountCodesData = [];

        // Mapping between cart option values and database governorate names
        const governorateMapping = {
            'baghdad': 'بغداد',
            'anbar': 'الأنبار',
            'babylon': 'بابل',
            'basra': 'البصرة',
            'dohuk': 'دهوك',
            'diyala': 'ديالى',
            'dhi-qar': 'ذي قار',
            'erbil': 'أربيل',
            'qadisiyyah': 'القادسية (الديوانية)',
            'sulaymaniyah': 'السليمانية',
            'salah-al-din': 'صلاح الدين',
            'halabja': 'حلبجة',
            'karbala': 'كربلاء',
            'kirkuk': 'كركوك',
            'maysan': 'ميسان',
            'muthanna': 'المثنى',
            'najaf': 'النجف',
            'nineveh': 'نينوى',
            'wasit': 'واسط'
        };

        // Load delivery pricing data from database
        async function loadDeliveryPricing() {
            try {
                const { data, error } = await supabase
                    .from('delivery_pricing')
                    .select('*')
                    .eq('is_active', true);

                if (error) throw error;

                deliveryPricingData = data || [];
                return true;
            } catch (error) {
                console.error('Error loading delivery pricing:', error);
                deliveryPricingData = [];
                return false;
            }
        }

        // Get delivery fee for selected governorate with error handling
        function getDeliveryFeeForGovernorate(governorateValue) {
            try {
                if (!governorateValue || !governorateMapping[governorateValue]) {
                    return 0;
                }

                // Check if delivery pricing data is loaded
                if (!deliveryPricingData || deliveryPricingData.length === 0) {
                    console.warn('Delivery pricing data not loaded, using fallback');
                    return getFallbackDeliveryFee(governorateValue);
                }

                const governorateNameAr = governorateMapping[governorateValue];
                const pricingRecord = deliveryPricingData.find(record =>
                    record.governorate_name_ar === governorateNameAr
                );

                if (pricingRecord) {
                    if (pricingRecord.is_free_delivery) {
                        return 0;
                    }
                    return parseFloat(pricingRecord.delivery_price) || 0;
                }

                // Fallback if no pricing found
                console.warn('No delivery pricing found for:', governorateNameAr, 'using fallback');
                return getFallbackDeliveryFee(governorateValue);
            } catch (error) {
                console.error('Error getting delivery fee:', error);
                return getFallbackDeliveryFee(governorateValue);
            }
        }

        // Fallback delivery fees when database is unavailable
        function getFallbackDeliveryFee(governorateValue) {
            const fallbackFees = {
                'baghdad': 5000,
                'babylon': 8000,
                'najaf': 8500,
                'karbala': 9000,
                'basra': 10000,
                'anbar': 10000,
                'qadisiyyah': 10000,
                'dhi-qar': 11000,
                'maysan': 11500,
                'wasit': 12000,
                'diyala': 12500,
                'salah-al-din': 13000,
                'sulaymaniyah': 13000,
                'muthanna': 13500,
                'erbil': 14000,
                'kirkuk': 14000,
                'nineveh': 14500,
                'halabja': 14500,
                'dohuk': 15000
            };

            return fallbackFees[governorateValue] || 10000;
        }

        // Enhanced error handling for discount validation
        function validateDiscountCodeSafely(code, subtotal) {
            try {
                // Allow cart functionality even if discount data is not loaded
                if (!discountCodesData || discountCodesData.length === 0) {
                    console.warn('Discount codes data not loaded, but allowing cart functionality');
                    return { valid: false, message: 'كود الخصم غير متاح حالياً' };
                }

                return validateDiscountCode(code, subtotal);
            } catch (error) {
                console.error('Error validating discount code:', error);
                // Don't block cart functionality due to discount validation errors
                return { valid: false, message: 'كود الخصم غير متاح حالياً' };
            }
        }

        // Cart functionality
        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price) + ' د.ع';
        }

        function updateQuantity(productId, newQuantity) {
            if (newQuantity <= 0) {
                removeFromCart(productId);
                return;
            }

            const item = cart.find(item => item.id === productId);
            if (item) {
                item.quantity = newQuantity;
                localStorage.setItem('cart', JSON.stringify(cart));

                // Recalculate discount based on new subtotal
                recalculateDiscount();

                displayCart();
                updateCartCount();
            }
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            localStorage.setItem('cart', JSON.stringify(cart));

            // Recalculate discount based on new subtotal
            recalculateDiscount();

            displayCart();
            updateCartCount();
        }

        function removeFromCartWithConfirmation(productId, productName) {
            showCartConfirmationModal(
                `هل أنت متأكد من حذف "${productName}" من السلة؟`,
                () => {
                    removeFromCart(productId);
                    showCartSuccessModal(`تم حذف "${productName}" من السلة بنجاح`);
                },
                'danger'
            );
        }

        function clearCart() {
            cart = [];
            localStorage.setItem('cart', JSON.stringify(cart));

            // Remove discount when cart is cleared
            removeDiscount();

            // Reset discount input form
            const discountCodeInput = document.getElementById('discountCode');
            const applyDiscountBtn = document.getElementById('applyDiscountBtn');
            const removeDiscountBtn = document.getElementById('removeDiscountBtn');
            const discountMessage = document.getElementById('discountMessage');

            if (discountCodeInput && applyDiscountBtn) {
                discountCodeInput.value = '';
                discountCodeInput.disabled = false;
                applyDiscountBtn.textContent = 'تطبيق';
                applyDiscountBtn.disabled = true;
                applyDiscountBtn.style.display = 'block';
            }

            if (removeDiscountBtn) {
                removeDiscountBtn.style.display = 'none';
            }

            if (discountMessage) {
                discountMessage.textContent = '';
                discountMessage.className = 'discount-message';
            }

            // Clear customer data when cart is explicitly cleared
            clearCustomerData();

            displayCart();
            updateCartCount();
        }

        function clearCartWithConfirmation() {
            if (cart.length === 0) {
                showCartConfirmationModal('السلة فارغة بالفعل', null, 'info');
                return;
            }

            showCartConfirmationModal(
                'هل أنت متأكد من أنك تريد مسح جميع المنتجات من السلة؟',
                () => {
                    clearCart();
                    showCartSuccessModal('تم مسح جميع المنتجات من السلة بنجاح');
                },
                'warning'
            );
        }

        function calculateSubtotal() {
            return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        }

        function calculateTotal() {
            const subtotal = calculateSubtotal();
            return Math.max(0, subtotal - discountAmount + deliveryFee);
        }

        // Load discount codes data from database
        async function loadDiscountCodes() {
            try {
                const { data, error } = await supabase
                    .from('discount_codes')
                    .select('*')
                    .eq('is_active', true);

                if (error) throw error;

                discountCodesData = data || [];
                return true;
            } catch (error) {
                console.error('Error loading discount codes:', error);
                discountCodesData = [];
                return false;
            }
        }

        // Validate discount code against database
        function validateDiscountCode(code, subtotal) {
            const discountRecord = discountCodesData.find(record =>
                record.code_name.toUpperCase() === code.toUpperCase()
            );

            if (!discountRecord) {
                return { valid: false, message: 'كود الخصم غير صحيح' };
            }

            // Check if code is active
            if (!discountRecord.is_active) {
                return { valid: false, message: 'كود الخصم غير نشط' };
            }

            // Check date validity
            const currentDate = new Date();
            const startDate = new Date(discountRecord.start_date);
            const endDate = new Date(discountRecord.end_date);

            if (currentDate < startDate) {
                return { valid: false, message: 'كود الخصم لم يبدأ بعد' };
            }

            if (currentDate > endDate) {
                return { valid: false, message: 'كود الخصم منتهي الصلاحية' };
            }

            // Check minimum order amount
            if (discountRecord.min_order_amount && subtotal < parseFloat(discountRecord.min_order_amount)) {
                const minAmount = formatPrice(parseFloat(discountRecord.min_order_amount));
                return { valid: false, message: `الحد الأدنى للطلب ${minAmount}` };
            }

            // Check usage limits
            if (discountRecord.max_usage && discountRecord.usage_count >= discountRecord.max_usage) {
                return { valid: false, message: 'تم استنفاد عدد مرات الاستخدام لهذا الكود' };
            }

            return { valid: true, record: discountRecord };
        }

        // Calculate discount amount based on type
        function calculateDiscountAmount(discountRecord, subtotal) {
            if (discountRecord.discount_type === 'percentage') {
                return Math.round(subtotal * (parseFloat(discountRecord.discount_value) / 100));
            } else if (discountRecord.discount_type === 'fixed') {
                return Math.min(parseFloat(discountRecord.discount_value), subtotal);
            }
            return 0;
        }

        function applyDiscount(code) {
            try {
                const subtotal = calculateSubtotal();
                const validation = validateDiscountCodeSafely(code, subtotal);

                if (!validation.valid) {
                    return { success: false, message: validation.message };
                }

                const discountRecord = validation.record;
                discountAmount = calculateDiscountAmount(discountRecord, subtotal);
                appliedDiscountCode = code.toUpperCase();

                let description = '';
                if (discountRecord.discount_type === 'percentage') {
                    description = `خصم ${discountRecord.discount_value}%`;
                } else {
                    description = `خصم ${formatPrice(discountRecord.discount_value)}`;
                }

                return {
                    success: true,
                    message: `تم تطبيق ${description}`,
                    amount: discountAmount,
                    record: discountRecord
                };
            } catch (error) {
                console.error('Error applying discount:', error);
                return { success: false, message: 'حدث خطأ أثناء تطبيق كود الخصم' };
            }
        }

        function removeDiscount() {
            discountAmount = 0;
            appliedDiscountCode = null;

            // Reset discount UI elements if they exist
            const discountCodeInput = document.getElementById('discountCode');
            const applyDiscountBtn = document.getElementById('applyDiscountBtn');
            const removeDiscountBtn = document.getElementById('removeDiscountBtn');
            const discountMessage = document.getElementById('discountMessage');

            if (discountCodeInput && applyDiscountBtn) {
                discountCodeInput.value = '';
                discountCodeInput.disabled = false;
                discountCodeInput.placeholder = 'أدخل كود الخصم';
                applyDiscountBtn.textContent = 'تطبيق';
                applyDiscountBtn.disabled = true;
                applyDiscountBtn.style.display = 'block';
            }

            if (removeDiscountBtn) {
                removeDiscountBtn.style.display = 'none';
            }

            if (discountMessage) {
                discountMessage.textContent = '';
                discountMessage.className = 'discount-message';
            }
        }

        // Recalculate discount when cart contents change
        function recalculateDiscount() {
            if (!appliedDiscountCode) {
                return; // No discount applied, nothing to recalculate
            }

            try {
                const subtotal = calculateSubtotal();

                // If cart is empty, remove discount
                if (subtotal === 0) {
                    removeDiscount();
                    return;
                }

                // Find the applied discount record
                const discountRecord = discountCodesData.find(record =>
                    record.code_name.toUpperCase() === appliedDiscountCode.toUpperCase()
                );

                if (!discountRecord) {
                    console.warn('Applied discount code not found in database, removing discount');
                    removeDiscount();
                    return;
                }

                // Validate the discount is still valid for the new subtotal
                // Don't remove discount automatically to avoid disrupting user experience
                const validation = validateDiscountCodeSafely(appliedDiscountCode, subtotal);
                if (!validation.valid) {
                    console.log('Discount validation warning for current subtotal:', validation.message);
                    // Keep discount but show warning instead of removing it
                    const discountMessage = document.getElementById('discountMessage');
                    if (discountMessage) {
                        discountMessage.textContent = 'تحذير: قد لا يكون كود الخصم صالحاً للمبلغ الحالي';
                        discountMessage.className = 'discount-message warning';
                    }
                    return;
                }

                // Recalculate discount amount based on new subtotal
                const newDiscountAmount = calculateDiscountAmount(discountRecord, subtotal);

                if (newDiscountAmount !== discountAmount) {
                    discountAmount = newDiscountAmount;
                    console.log('Discount recalculated:', appliedDiscountCode, 'New amount:', discountAmount);

                    // Update discount message to show new amount
                    const discountMessage = document.getElementById('discountMessage');
                    if (discountMessage && discountMessage.className.includes('success')) {
                        let description = '';
                        if (discountRecord.discount_type === 'percentage') {
                            description = `خصم ${discountRecord.discount_value}%`;
                        } else {
                            description = `خصم ${formatPrice(discountRecord.discount_value)}`;
                        }
                        discountMessage.textContent = `تم تطبيق ${description} - المبلغ المحدث: ${formatPrice(discountAmount)}`;
                    }
                }

            } catch (error) {
                console.error('Error recalculating discount:', error);
                // Keep existing discount in case of error
            }
        }

        // Customer information persistence functions
        function saveCustomerData() {
            try {
                const customerData = {
                    customerName: document.getElementById('customerName')?.value || '',
                    customerPhone: document.getElementById('customerPhone')?.value || '',
                    customerAddress: document.getElementById('customerAddress')?.value || '',
                    deliveryLocation: document.getElementById('deliveryLocation')?.value || '',
                    customerNotes: document.getElementById('customerNotes')?.value || '',
                    // Save delivery fee and applied discount for persistence
                    savedDeliveryFee: deliveryFee,
                    savedDiscountCode: appliedDiscountCode,
                    savedDiscountAmount: discountAmount
                };

                localStorage.setItem('customerData', JSON.stringify(customerData));
            } catch (error) {
                console.error('Error saving customer data:', error);
            }
        }

        function restoreCustomerData() {
            try {
                const savedData = localStorage.getItem('customerData');
                if (!savedData) return;

                const customerData = JSON.parse(savedData);

                // Restore form fields
                const fields = [
                    'customerName',
                    'customerPhone',
                    'customerAddress',
                    'deliveryLocation',
                    'customerNotes'
                ];

                fields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element && customerData[fieldId]) {
                        element.value = customerData[fieldId];

                        // Trigger change event for delivery location to update delivery fee
                        if (fieldId === 'deliveryLocation') {
                            // Restore delivery fee from saved data first
                            if (customerData.savedDeliveryFee !== undefined) {
                                deliveryFee = customerData.savedDeliveryFee;
                            }
                            element.dispatchEvent(new Event('change'));
                        }
                    }
                });

                // Restore discount information if available
                if (customerData.savedDiscountCode && customerData.savedDiscountAmount !== undefined) {
                    appliedDiscountCode = customerData.savedDiscountCode;
                    discountAmount = customerData.savedDiscountAmount;

                    // Update discount UI
                    const discountCodeInput = document.getElementById('discountCode');
                    const applyDiscountBtn = document.getElementById('applyDiscountBtn');
                    const removeDiscountBtn = document.getElementById('removeDiscountBtn');
                    const discountMessage = document.getElementById('discountMessage');

                    if (discountCodeInput && applyDiscountBtn && discountMessage) {
                        discountCodeInput.value = appliedDiscountCode;
                        discountCodeInput.disabled = false;
                        discountCodeInput.placeholder = `كود مطبق: ${appliedDiscountCode}`;
                        applyDiscountBtn.style.display = 'none';

                        if (removeDiscountBtn) {
                            removeDiscountBtn.style.display = 'block';
                        }

                        discountMessage.textContent = `تم تطبيق كود الخصم: ${appliedDiscountCode}`;
                        discountMessage.className = 'discount-message success';
                    }
                }

                // Update summary and validate form after restoration
                updateSummary();
                validateForm();

            } catch (error) {
                console.error('Error restoring customer data:', error);
            }
        }

        function clearCustomerData() {
            try {
                localStorage.removeItem('customerData');
                console.log('Customer data cleared from localStorage');
            } catch (error) {
                console.error('Error clearing customer data:', error);
            }
        }

        // Show cart messages to user
        function showCartMessage(message, type = 'info') {
            // Create or update message element
            let messageEl = document.getElementById('cartMessage');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'cartMessage';
                messageEl.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    z-index: 10000;
                    max-width: 300px;
                    text-align: right;
                    font-family: 'Cairo', sans-serif;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(messageEl);
            }

            // Set message type styling
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };

            messageEl.style.backgroundColor = colors[type] || colors.info;
            messageEl.textContent = message;
            messageEl.style.display = 'block';
            messageEl.style.opacity = '1';

            // Auto hide after 5 seconds
            setTimeout(() => {
                if (messageEl) {
                    messageEl.style.opacity = '0';
                    setTimeout(() => {
                        if (messageEl && messageEl.parentNode) {
                            messageEl.parentNode.removeChild(messageEl);
                        }
                    }, 300);
                }
            }, 5000);
        }

        function updateSummary() {
            const subtotal = calculateSubtotal();
            const total = calculateTotal();

            document.getElementById('subtotal').textContent = formatPrice(subtotal);

            // Update delivery fee display with governorate info
            const deliveryFeeEl = document.getElementById('deliveryFee');
            const deliveryLocationEl = document.getElementById('deliveryLocation');

            if (deliveryFee > 0) {
                const selectedOption = deliveryLocationEl?.selectedOptions[0];
                const governorateName = selectedOption ? selectedOption.textContent : '';
                deliveryFeeEl.textContent = formatPrice(deliveryFee);
                deliveryFeeEl.title = governorateName ? `رسوم التوصيل إلى ${governorateName}` : '';
            } else {
                deliveryFeeEl.textContent = 'مجاني';
                deliveryFeeEl.title = 'التوصيل مجاني';
            }

            document.getElementById('total').textContent = formatPrice(total);

            // Update discount display
            const discountRow = document.getElementById('discountRow');
            const discountAmountEl = document.getElementById('discountAmount');

            if (discountAmount > 0) {
                discountRow.style.display = 'flex';
                discountAmountEl.textContent = '-' + formatPrice(discountAmount);

                // Add discount code name if available
                if (appliedDiscountCode) {
                    discountAmountEl.title = `كود الخصم: ${appliedDiscountCode}`;
                }
            } else {
                discountRow.style.display = 'none';
            }
        }

        function displayCart() {
            const cartContainer = document.getElementById('cartContainer');
            const emptyCart = document.getElementById('emptyCart');
            const cartItemsList = document.getElementById('cartItemsList');

            // التحقق من وجود العناصر المطلوبة
            if (!cartContainer || !emptyCart || !cartItemsList) {
                console.error('عناصر السلة المطلوبة غير موجودة في DOM');
                return;
            }

            if (cart.length === 0) {
                cartContainer.style.display = 'none';
                emptyCart.style.display = 'block';
                return;
            }

            cartContainer.style.display = 'grid';
            emptyCart.style.display = 'none';

            cartItemsList.innerHTML = cart.map(item => `
                <div class="cart-item">
                    <div class="item-image">
                        ${item.image_url ?
                            `<img src="${item.image_url}" alt="${item.name}" loading="lazy">` :
                            '<i class="fas fa-box-open"></i>'
                        }
                    </div>
                    <div class="item-info">
                        <div class="item-name" title="${item.name}">${item.name}</div>
                        <div class="item-details">
                            <div class="item-price">
                                <span class="price-label">السعر:</span>
                                <span class="price-value">${formatPrice(item.price)}</span>
                            </div>
                            <div class="item-subtotal">
                                <span class="subtotal-label">المجموع:</span>
                                <span class="subtotal-value">${formatPrice(item.price * item.quantity)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="item-controls">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity - 1})" ${item.quantity <= 1 ? 'disabled' : ''} title="تقليل الكمية">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="quantity-display">${item.quantity}</span>
                            <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity + 1})" title="زيادة الكمية">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <button class="remove-btn" onclick="removeFromCartWithConfirmation('${item.id}', '${item.name.replace(/'/g, '&#39;')}')" title="حذف المنتج">
                            <i class="fas fa-trash-alt"></i>
                            <span class="remove-text">حذف</span>
                        </button>
                    </div>
                </div>
            `).join('');

            try {
                updateSummary();
                updateClearCartButton();
            } catch (error) {
                console.error('خطأ في تحديث ملخص السلة:', error);
            }
        }

        function updateClearCartButton() {
            const clearCartBtn = document.querySelector('.clear-cart-btn');
            if (clearCartBtn) {
                clearCartBtn.disabled = cart.length === 0;
                clearCartBtn.style.display = cart.length === 0 ? 'none' : 'flex';
            }
        }

        function validateForm() {
            const form = document.getElementById('checkoutForm');
            if (!form) {
                console.warn('Checkout form not found, allowing cart functionality');
                return true;
            }

            const formData = new FormData(form);
            const requiredFields = ['customerName', 'customerPhone', 'customerAddress', 'deliveryLocation'];
            let isValid = true;

            for (const field of requiredFields) {
                const value = formData.get(field);
                if (!value || value.trim() === '') {
                    isValid = false;
                    break;
                }
            }

            const onlineCheckoutBtn = document.getElementById('onlineCheckoutBtn');
            const whatsappCheckoutBtn = document.getElementById('whatsappCheckoutBtn');

            // Only disable buttons if they exist and cart is empty
            if (onlineCheckoutBtn) {
                onlineCheckoutBtn.disabled = !isValid || cart.length === 0;
            }
            if (whatsappCheckoutBtn) {
                whatsappCheckoutBtn.disabled = !isValid || cart.length === 0;
            }

            return isValid;
        }

        function updateDeliveryFee() {
            const deliveryLocationEl = document.getElementById('deliveryLocation');
            if (!deliveryLocationEl) return;

            const selectedGovernorate = deliveryLocationEl.value;
            deliveryFee = getDeliveryFeeForGovernorate(selectedGovernorate);

            console.log('Delivery fee updated for', selectedGovernorate, ':', deliveryFee);
            updateSummary();
            validateForm();
        }

        // Enhanced dropdown styling and functionality
        function enhanceDeliveryDropdown() {
            const deliveryLocationEl = document.getElementById('deliveryLocation');
            if (!deliveryLocationEl) return;

            // Add data attributes for delivery fees (for internal use only)
            const options = deliveryLocationEl.querySelectorAll('option[value]:not([value=""])');
            options.forEach(option => {
                const governorateValue = option.value;
                const fee = getDeliveryFeeForGovernorate(governorateValue);
                option.setAttribute('data-fee', fee);
            });

            // Add visual enhancements
            deliveryLocationEl.classList.add('enhanced-dropdown');
        }

        async function submitOrder(orderData) {
            try {
                // التحقق من تهيئة Supabase
                if (!supabase) {
                    throw new Error('خطأ في الاتصال بقاعدة البيانات');
                }

                // التحقق من البيانات المطلوبة
                if (!orderData.customerName || !orderData.customerPhone || !orderData.customerAddress || !orderData.deliveryLocation) {
                    throw new Error('معلومات العميل المطلوبة مفقودة');
                }

                if (cart.length === 0) {
                    console.warn('Cart is empty, but allowing order processing to continue');
                    showCartMessage('سلة التسوق فارغة. يرجى إضافة منتجات أولاً.', 'warning');
                    return null;
                }

                // تحضير بيانات الطلب للإدراج
                const orderInsertData = {
                    customer_name: orderData.customerName.trim(),
                    customer_phone: orderData.customerPhone.trim(),
                    customer_address: orderData.customerAddress.trim(),
                    customer_notes: orderData.customerNotes ? orderData.customerNotes.trim() : null,
                    delivery_location: orderData.deliveryLocation,
                    delivery_fee: Number(deliveryFee) || 0,
                    discount_code: appliedDiscountCode || null,
                    discount_amount: Number(discountAmount) || 0,
                    subtotal: Number(calculateSubtotal()),
                    total: Number(calculateTotal()),
                    status: 'pending'
                };

                // إدراج الطلب في قاعدة البيانات
                const { data: order, error: orderError } = await supabase
                    .from('orders')
                    .insert([orderInsertData])
                    .select()
                    .single();

                if (orderError) {
                    throw new Error(`فشل في إنشاء الطلب: ${orderError.message}`);
                }

                // تحضير عناصر الطلب للإدراج
                const orderItems = cart.map(item => ({
                    order_id: order.id,
                    product_id: item.id,
                    quantity: Number(item.quantity),
                    price: Number(item.price)
                }));

                // إدراج عناصر الطلب
                const { error: itemsError } = await supabase
                    .from('order_items')
                    .insert(orderItems);

                if (itemsError) {
                    throw new Error(`فشل في إضافة عناصر الطلب: ${itemsError.message}`);
                }

                return order;
            } catch (error) {
                console.error('خطأ في إرسال الطلب:', error);
                throw error;
            }
        }

        function generateWhatsAppMessage(orderData, orderId) {
            const subtotal = calculateSubtotal();
            const total = calculateTotal();

            let message = `🛍️ *طلب جديد من متجر Care*\n\n`;
            message += `📋 *رقم الطلب:* ${orderId}\n\n`;
            message += `👤 *معلومات العميل:*\n`;
            message += `الاسم: ${orderData.customerName}\n`;
            message += `الهاتف: ${orderData.customerPhone}\n`;
            message += `العنوان: ${orderData.customerAddress}\n`;
            message += `منطقة التوصيل: ${getGovernorateNameArabic(orderData.deliveryLocation)}\n`;

            if (orderData.customerNotes) {
                message += `ملاحظات: ${orderData.customerNotes}\n`;
            }

            message += `\n🛒 *المنتجات المطلوبة:*\n`;
            cart.forEach(item => {
                message += `• ${item.name}\n`;
                message += `  الكمية: ${item.quantity}\n`;
                message += `  السعر: ${formatPrice(item.price)}\n`;
                message += `  المجموع: ${formatPrice(item.price * item.quantity)}\n\n`;
            });

            message += `💰 *ملخص الطلب:*\n`;
            message += `المجموع الفرعي: ${formatPrice(subtotal)}\n`;

            if (discountAmount > 0) {
                message += `الخصم (${appliedDiscountCode}): -${formatPrice(discountAmount)}\n`;
            }

            message += `رسوم التوصيل: ${deliveryFee > 0 ? formatPrice(deliveryFee) : 'مجاني'}\n`;
            message += `*المجموع الكلي: ${formatPrice(total)}*\n\n`;
            message += `📞 شكراً لاختياركم متجر Care!`;

            return encodeURIComponent(message);
        }

        function getGovernorateNameArabic(value) {
            const governorates = {
                'baghdad': 'بغداد',
                'anbar': 'الأنبار',
                'babylon': 'بابل',
                'basra': 'البصرة',
                'dohuk': 'دهوك',
                'diyala': 'ديالى',
                'dhi-qar': 'ذي قار',
                'erbil': 'أربيل',
                'qadisiyyah': 'القادسية (الديوانية)',
                'sulaymaniyah': 'السليمانية',
                'salah-al-din': 'صلاح الدين',
                'halabja': 'حلبجة',
                'karbala': 'كربلاء',
                'kirkuk': 'كركوك',
                'maysan': 'ميسان',
                'muthanna': 'المثنى',
                'najaf': 'النجف',
                'nineveh': 'نينوى',
                'wasit': 'واسط'
            };
            return governorates[value] || value;
        }

        function showSuccessMessage(message) {
            // Clear customer data on successful order
            clearCustomerData();

            // Create success modal
            const modal = document.createElement('div');
            modal.className = 'success-modal';
            modal.innerHTML = `
                <div class="success-modal-content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>تم إرسال الطلب بنجاح</h3>
                    <p>${message}</p>
                    <button class="success-btn" onclick="closeSuccessModal();">
                        العودة للرئيسية
                    </button>
                </div>
            `;

            // Add modal styles
            const style = document.createElement('style');
            style.textContent = `
                .success-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(5px);
                }
                .success-modal-content {
                    background: white;
                    padding: 3rem;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 500px;
                    margin: 2rem;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                }
                .success-icon i {
                    font-size: 4rem;
                    color: #27ae60;
                    margin-bottom: 1rem;
                }
                .success-modal-content h3 {
                    color: #27ae60;
                    margin-bottom: 1rem;
                    font-size: 1.5rem;
                }
                .success-modal-content p {
                    color: #666;
                    margin-bottom: 2rem;
                    line-height: 1.6;
                }
                .success-btn {
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                    border: none;
                    padding: 1rem 2rem;
                    border-radius: 10px;
                    font-family: 'Cairo', sans-serif;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                .success-btn:hover {
                    background: linear-gradient(135deg, #2ecc71, #27ae60);
                    transform: translateY(-2px);
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(modal);
        }

        function closeSuccessModal() {
            const modal = document.querySelector('.success-modal');
            if (modal) {
                modal.remove();
            }

            // Clear customer data and cart on successful completion
            clearCustomerData();
            clearCart();

            // Redirect to home page
            window.location.href = 'index.html';
        }

        // Professional Cart Confirmation Modal System
        function showCartConfirmationModal(message, onConfirm = null, type = 'warning') {
            // Remove any existing modals
            const existingModal = document.querySelector('.cart-confirmation-modal-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'cart-confirmation-modal-overlay';

            // Determine icon and colors based on type
            let iconClass, headerColor, confirmButtonClass;
            switch (type) {
                case 'warning':
                    iconClass = 'fas fa-exclamation-triangle';
                    headerColor = '#f39c12';
                    confirmButtonClass = 'cart-confirm-btn-warning';
                    break;
                case 'danger':
                    iconClass = 'fas fa-trash-alt';
                    headerColor = '#e74c3c';
                    confirmButtonClass = 'cart-confirm-btn-danger';
                    break;
                case 'info':
                    iconClass = 'fas fa-info-circle';
                    headerColor = '#3498db';
                    confirmButtonClass = 'cart-confirm-btn-info';
                    break;
                default:
                    iconClass = 'fas fa-question-circle';
                    headerColor = '#95a5a6';
                    confirmButtonClass = 'cart-confirm-btn-default';
            }

            overlay.innerHTML = `
                <div class="cart-confirmation-modal">
                    <div class="cart-confirmation-header" style="background: linear-gradient(135deg, ${headerColor}, ${headerColor}dd);">
                        <button class="cart-modal-close" onclick="closeCartConfirmationModal()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="cart-confirmation-icon">
                            <i class="${iconClass}"></i>
                        </div>
                        <h3 class="cart-confirmation-title">${onConfirm ? 'تأكيد العملية' : 'تنبيه'}</h3>
                    </div>
                    <div class="cart-confirmation-body">
                        <p class="cart-confirmation-message">${message}</p>
                        ${onConfirm ? `
                            <div class="cart-confirmation-actions">
                                <button class="cart-confirmation-btn cart-cancel-btn" onclick="closeCartConfirmationModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button class="cart-confirmation-btn ${confirmButtonClass}" onclick="confirmCartAction()">
                                    <i class="fas fa-check"></i>
                                    تأكيد
                                </button>
                            </div>
                        ` : `
                            <div class="cart-confirmation-actions">
                                <button class="cart-confirmation-btn cart-confirm-btn-info" onclick="closeCartConfirmationModal()">
                                    <i class="fas fa-check"></i>
                                    موافق
                                </button>
                            </div>
                        `}
                    </div>
                </div>
            `;

            // Store the confirm callback
            if (onConfirm) {
                window.cartConfirmCallback = onConfirm;
            }

            // Add modal styles if not already added
            if (!document.querySelector('#cart-confirmation-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'cart-confirmation-modal-styles';
                style.textContent = `
                    .cart-confirmation-modal-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.6);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                        opacity: 0;
                        visibility: hidden;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(5px);
                    }

                    .cart-confirmation-modal-overlay.show {
                        opacity: 1;
                        visibility: visible;
                    }

                    .cart-confirmation-modal {
                        background: white;
                        border-radius: 15px;
                        padding: 0;
                        max-width: 500px;
                        width: 90%;
                        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                        transform: scale(0.7) translateY(-50px);
                        transition: all 0.3s ease;
                        overflow: hidden;
                        direction: rtl;
                        font-family: 'Cairo', sans-serif;
                    }

                    .cart-confirmation-modal-overlay.show .cart-confirmation-modal {
                        transform: scale(1) translateY(0);
                    }

                    .cart-confirmation-header {
                        padding: 2rem;
                        text-align: center;
                        color: white;
                        position: relative;
                    }

                    .cart-modal-close {
                        position: absolute;
                        top: 1rem;
                        left: 1rem;
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.5rem;
                        cursor: pointer;
                        opacity: 0.7;
                        transition: opacity 0.3s;
                        width: 35px;
                        height: 35px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                    }

                    .cart-modal-close:hover {
                        opacity: 1;
                        background: rgba(255, 255, 255, 0.1);
                    }

                    .cart-confirmation-icon {
                        font-size: 3rem;
                        margin-bottom: 1rem;
                        animation: pulse 2s infinite;
                    }

                    @keyframes pulse {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.1); }
                    }

                    .cart-confirmation-title {
                        font-size: 1.5rem;
                        font-weight: 700;
                        margin: 0;
                    }

                    .cart-confirmation-body {
                        padding: 2rem;
                        text-align: center;
                    }

                    .cart-confirmation-message {
                        font-size: 1.1rem;
                        color: #333;
                        line-height: 1.6;
                        margin-bottom: 2rem;
                    }

                    .cart-confirmation-actions {
                        display: flex;
                        gap: 1rem;
                        justify-content: center;
                        flex-wrap: wrap;
                    }

                    .cart-confirmation-btn {
                        padding: 0.8rem 1.5rem;
                        border: none;
                        border-radius: 10px;
                        font-family: 'Cairo', sans-serif;
                        font-weight: 600;
                        font-size: 1rem;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        min-width: 120px;
                        justify-content: center;
                    }

                    .cart-cancel-btn {
                        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
                        color: white;
                    }

                    .cart-cancel-btn:hover {
                        background: linear-gradient(135deg, #7f8c8d, #95a5a6);
                        transform: translateY(-2px);
                    }

                    .cart-confirm-btn-warning {
                        background: linear-gradient(135deg, #f39c12, #e67e22);
                        color: white;
                    }

                    .cart-confirm-btn-warning:hover {
                        background: linear-gradient(135deg, #e67e22, #f39c12);
                        transform: translateY(-2px);
                    }

                    .cart-confirm-btn-danger {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                        color: white;
                    }

                    .cart-confirm-btn-danger:hover {
                        background: linear-gradient(135deg, #c0392b, #e74c3c);
                        transform: translateY(-2px);
                    }

                    .cart-confirm-btn-info {
                        background: linear-gradient(135deg, #3498db, #2980b9);
                        color: white;
                    }

                    .cart-confirm-btn-info:hover {
                        background: linear-gradient(135deg, #2980b9, #3498db);
                        transform: translateY(-2px);
                    }

                    /* Mobile responsiveness */
                    @media (max-width: 768px) {
                        .cart-confirmation-modal {
                            width: 95%;
                            margin: 1rem;
                        }

                        .cart-confirmation-header {
                            padding: 1.5rem;
                        }

                        .cart-confirmation-body {
                            padding: 1.5rem;
                        }

                        .cart-confirmation-actions {
                            flex-direction: column;
                        }

                        .cart-confirmation-btn {
                            width: 100%;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // Add to body and show
            document.body.appendChild(overlay);
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);

            // Add keyboard support
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    closeCartConfirmationModal();
                } else if (e.key === 'Enter' && onConfirm) {
                    confirmCartAction();
                }
            };
            document.addEventListener('keydown', handleKeydown);
            overlay.addEventListener('keydown', handleKeydown);
        }

        function closeCartConfirmationModal() {
            const modal = document.querySelector('.cart-confirmation-modal-overlay');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                    // Clean up callback
                    if (window.cartConfirmCallback) {
                        delete window.cartConfirmCallback;
                    }
                }, 300);
            }
        }

        function confirmCartAction() {
            if (window.cartConfirmCallback) {
                window.cartConfirmCallback();
                delete window.cartConfirmCallback;
            }
            closeCartConfirmationModal();
        }

        // Professional Cart Success Modal
        function showCartSuccessModal(message) {
            // Remove any existing modals
            const existingModal = document.querySelector('.cart-success-notification-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'cart-success-notification-overlay';

            overlay.innerHTML = `
                <div class="cart-success-notification-modal">
                    <div class="cart-success-notification-header">
                        <div class="cart-success-notification-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="cart-success-notification-title">تم بنجاح</h3>
                    </div>
                    <div class="cart-success-notification-body">
                        <p class="cart-success-notification-message">${message}</p>
                        <button class="cart-success-notification-btn" onclick="closeCartSuccessModal()">
                            <i class="fas fa-check"></i>
                            موافق
                        </button>
                    </div>
                </div>
            `;

            // Add modal styles if not already added
            if (!document.querySelector('#cart-success-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'cart-success-notification-styles';
                style.textContent = `
                    .cart-success-notification-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.6);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                        opacity: 0;
                        visibility: hidden;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(5px);
                    }

                    .cart-success-notification-overlay.show {
                        opacity: 1;
                        visibility: visible;
                    }

                    .cart-success-notification-modal {
                        background: white;
                        border-radius: 15px;
                        padding: 0;
                        max-width: 450px;
                        width: 90%;
                        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                        transform: scale(0.7) translateY(-50px);
                        transition: all 0.3s ease;
                        overflow: hidden;
                        direction: rtl;
                        font-family: 'Cairo', sans-serif;
                    }

                    .cart-success-notification-overlay.show .cart-success-notification-modal {
                        transform: scale(1) translateY(0);
                    }

                    .cart-success-notification-header {
                        background: linear-gradient(135deg, #27ae60, #2ecc71);
                        padding: 2rem;
                        text-align: center;
                        color: white;
                    }

                    .cart-success-notification-icon {
                        font-size: 3rem;
                        margin-bottom: 1rem;
                        animation: successPulse 1.5s ease-in-out;
                    }

                    @keyframes successPulse {
                        0% { transform: scale(0.5); opacity: 0; }
                        50% { transform: scale(1.1); opacity: 1; }
                        100% { transform: scale(1); opacity: 1; }
                    }

                    .cart-success-notification-title {
                        font-size: 1.5rem;
                        font-weight: 700;
                        margin: 0;
                    }

                    .cart-success-notification-body {
                        padding: 2rem;
                        text-align: center;
                    }

                    .cart-success-notification-message {
                        font-size: 1.1rem;
                        color: #333;
                        line-height: 1.6;
                        margin-bottom: 2rem;
                    }

                    .cart-success-notification-btn {
                        background: linear-gradient(135deg, #27ae60, #2ecc71);
                        color: white;
                        border: none;
                        padding: 0.8rem 2rem;
                        border-radius: 10px;
                        font-family: 'Cairo', sans-serif;
                        font-weight: 600;
                        font-size: 1rem;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        margin: 0 auto;
                    }

                    .cart-success-notification-btn:hover {
                        background: linear-gradient(135deg, #2ecc71, #27ae60);
                        transform: translateY(-2px);
                    }

                    /* Mobile responsiveness */
                    @media (max-width: 768px) {
                        .cart-success-notification-modal {
                            width: 95%;
                            margin: 1rem;
                        }

                        .cart-success-notification-header {
                            padding: 1.5rem;
                        }

                        .cart-success-notification-body {
                            padding: 1.5rem;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // Add to body and show
            document.body.appendChild(overlay);
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);

            // Auto-close after 3 seconds
            setTimeout(() => {
                closeCartSuccessModal();
            }, 3000);
        }

        function closeCartSuccessModal() {
            const modal = document.querySelector('.cart-success-notification-overlay');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        async function handleOnlineCheckout() {
            if (!validateForm() || cart.length === 0) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const form = document.getElementById('checkoutForm');
            const formData = new FormData(form);
            const orderData = {
                customerName: formData.get('customerName'),
                customerPhone: formData.get('customerPhone'),
                customerAddress: formData.get('customerAddress'),
                customerNotes: formData.get('customerNotes'),
                deliveryLocation: formData.get('deliveryLocation')
            };

            const onlineCheckoutBtn = document.getElementById('onlineCheckoutBtn');

            try {
                // إظهار حالة التحميل
                onlineCheckoutBtn.disabled = true;
                onlineCheckoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إرسال الطلب...';

                // إرسال الطلب إلى قاعدة البيانات
                const order = await submitOrder(orderData);

                // مسح السلة وتحديث العرض
                clearCart();

                // إظهار رسالة النجاح
                showSuccessMessage(`تم استلام طلبك برقم ${order.id.substring(0, 8)} وسيتم التواصل معك قريباً لتأكيد التفاصيل.`);

            } catch (error) {
                // إظهار رسالة خطأ محددة
                let errorMessage = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.';

                if (error.message) {
                    if (error.message.includes('معلومات العميل المطلوبة مفقودة')) {
                        errorMessage = 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح.';
                    } else if (error.message.includes('سلة التسوق فارغة')) {
                        errorMessage = 'سلة التسوق فارغة. يرجى إضافة منتجات أولاً.';
                    } else if (error.message.includes('فشل في إنشاء الطلب')) {
                        errorMessage = 'فشل في حفظ الطلب. يرجى التحقق من البيانات والمحاولة مرة أخرى.';
                    }
                }

                alert(errorMessage);

                // إعادة تعيين الزر
                onlineCheckoutBtn.disabled = false;
                onlineCheckoutBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> إتمام الطلب أونلاين';
            }
        }

        async function handleWhatsAppCheckout(event) {
            event.preventDefault();

            if (!validateForm() || cart.length === 0) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const formData = new FormData(event.target);
            const orderData = {
                customerName: formData.get('customerName'),
                customerPhone: formData.get('customerPhone'),
                customerAddress: formData.get('customerAddress'),
                customerNotes: formData.get('customerNotes'),
                deliveryLocation: formData.get('deliveryLocation')
            };

            const whatsappCheckoutBtn = document.getElementById('whatsappCheckoutBtn');

            try {
                // إظهار حالة التحميل
                whatsappCheckoutBtn.disabled = true;
                whatsappCheckoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إرسال الطلب...';

                // إرسال الطلب إلى قاعدة البيانات
                const order = await submitOrder(orderData);

                // إنشاء رسالة واتساب
                const whatsappMessage = generateWhatsAppMessage(orderData, order.id);
                const whatsappUrl = `https://wa.me/9647713688302?text=${whatsappMessage}`;

                // مسح السلة وتحديث العرض
                clearCart();

                // فتح واتساب
                window.open(whatsappUrl, '_blank');

                // إظهار رسالة النجاح
                showSuccessMessage(`تم إرسال طلبك برقم ${order.id.substring(0, 8)} بنجاح! سيتم توجيهك إلى واتساب لإكمال الطلب.`);

            } catch (error) {
                // إظهار رسالة خطأ محددة
                let errorMessage = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.';

                if (error.message) {
                    if (error.message.includes('معلومات العميل المطلوبة مفقودة')) {
                        errorMessage = 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح.';
                    } else if (error.message.includes('سلة التسوق فارغة')) {
                        errorMessage = 'سلة التسوق فارغة. يرجى إضافة منتجات أولاً.';
                    } else if (error.message.includes('فشل في إنشاء الطلب')) {
                        errorMessage = 'فشل في حفظ الطلب. يرجى التحقق من البيانات والمحاولة مرة أخرى.';
                    }
                }

                alert(errorMessage);

                // إعادة تعيين الزر
                whatsappCheckoutBtn.disabled = false;
                whatsappCheckoutBtn.innerHTML = '<i class="fab fa-whatsapp"></i> إتمام الطلب عبر واتساب';
            }
        }







        // Event listeners
        document.addEventListener('DOMContentLoaded', async function() {
            // Load dynamic data from database with error handling
            console.log('Loading delivery pricing and discount codes...');
            try {
                const [deliveryLoaded, discountLoaded] = await Promise.all([
                    loadDeliveryPricing(),
                    loadDiscountCodes()
                ]);

                if (!deliveryLoaded) {
                    console.warn('Failed to load delivery pricing, using fallback values');
                    showCartMessage('تم استخدام أسعار التوصيل الافتراضية', 'warning');
                }

                if (!discountLoaded) {
                    console.warn('Failed to load discount codes');
                    showCartMessage('خدمة أكواد الخصم غير متاحة حالياً', 'warning');
                }

                if (deliveryLoaded && discountLoaded) {
                    console.log('All cart data loaded successfully');
                console.log('Delivery pricing records:', deliveryPricingData.length);
                console.log('Discount codes records:', discountCodesData.length);
                }
            } catch (error) {
                console.error('Error loading cart data:', error);
                // Don't show error message to user, just use defaults and continue
                console.log('Using default values for cart functionality');
                deliveryPricingData = [];
                discountCodesData = [];
            }

            // تحديث عداد السلة وعرض المحتويات
            updateCartCount();
            displayCart();

            // Restore customer data from localStorage
            restoreCustomerData();

            // Enhance delivery dropdown with fee information
            setTimeout(() => {
                enhanceDeliveryDropdown();
            }, 500);

            // Form validation with safe DOM access
            const form = document.getElementById('checkoutForm');
            if (form) {
                const inputs = form.querySelectorAll('input, select, textarea');

                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        validateForm();
                        saveCustomerData(); // Save data on every input
                    });
                    input.addEventListener('change', function() {
                        validateForm();
                        saveCustomerData(); // Save data on every change
                    });
                });

                // Form submission
                form.addEventListener('submit', handleWhatsAppCheckout);
            }

            // Delivery location change with safe DOM access
            const deliveryLocationEl = document.getElementById('deliveryLocation');
            if (deliveryLocationEl) {
                deliveryLocationEl.addEventListener('change', function() {
                    updateDeliveryFee();
                    saveCustomerData(); // Save data when delivery location changes
                });
            }

            // Online checkout button with safe DOM access
            const onlineCheckoutBtn = document.getElementById('onlineCheckoutBtn');
            if (onlineCheckoutBtn) {
                onlineCheckoutBtn.addEventListener('click', handleOnlineCheckout);
            }

            // Discount code functionality with safe DOM access
            const applyDiscountBtn = document.getElementById('applyDiscountBtn');
            const discountCodeInput = document.getElementById('discountCode');
            const discountMessage = document.getElementById('discountMessage');

            if (applyDiscountBtn && discountCodeInput && discountMessage) {
                applyDiscountBtn.addEventListener('click', async function() {
                    const discountCode = discountCodeInput.value.trim();

                    if (!discountCode) {
                        discountMessage.textContent = 'يرجى إدخال كود الخصم';
                        discountMessage.className = 'discount-message error';
                        return;
                    }

                    // Show loading state
                    applyDiscountBtn.disabled = true;
                    applyDiscountBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';
                    discountMessage.textContent = 'جاري التحقق من كود الخصم...';
                    discountMessage.className = 'discount-message info';

                    try {
                        // Ensure discount codes are loaded
                        if (discountCodesData.length === 0) {
                            console.log('Discount codes not loaded, attempting to reload...');
                            await loadDiscountCodes();
                        }

                        // Clear any existing discount before applying new one
                        if (appliedDiscountCode) {
                            removeDiscount();
                        }

                        const result = applyDiscount(discountCode);
                        discountMessage.textContent = result.message;
                        discountMessage.className = `discount-message ${result.success ? 'success' : 'error'}`;

                        if (result.success) {
                            const removeDiscountBtn = document.getElementById('removeDiscountBtn');

                            discountCodeInput.disabled = false;
                            discountCodeInput.placeholder = `كود مطبق: ${discountCode.toUpperCase()}`;
                            applyDiscountBtn.style.display = 'none';

                            if (removeDiscountBtn) {
                                removeDiscountBtn.style.display = 'block';
                            }

                            updateSummary();
                            saveCustomerData(); // Save data when discount is applied
                        } else {
                            // Reset button state on failure
                            applyDiscountBtn.textContent = 'تطبيق';
                            applyDiscountBtn.disabled = false;
                        }
                    } catch (error) {
                        console.error('Error applying discount:', error);
                        discountMessage.textContent = 'حدث خطأ أثناء تطبيق كود الخصم';
                        discountMessage.className = 'discount-message error';
                        applyDiscountBtn.textContent = 'تطبيق';
                        applyDiscountBtn.disabled = false;
                    }
                });

                // Remove discount button functionality
                const removeDiscountBtn = document.getElementById('removeDiscountBtn');
                if (removeDiscountBtn) {
                    removeDiscountBtn.addEventListener('click', function() {
                        removeDiscount();
                        updateSummary();
                        saveCustomerData(); // Save data when discount is removed

                        // Show feedback message
                        discountMessage.textContent = 'تم إزالة كود الخصم';
                        discountMessage.className = 'discount-message info';

                        // Clear message after 3 seconds
                        setTimeout(() => {
                            if (discountMessage.textContent === 'تم إزالة كود الخصم') {
                                discountMessage.textContent = '';
                                discountMessage.className = 'discount-message';
                            }
                        }, 3000);
                    });
                }

                // Enhanced discount code input handling
                discountCodeInput.addEventListener('input', function() {
                    const currentValue = this.value.trim();

                    // If user is typing and there's an applied discount, show replacement message
                    if (currentValue !== '' && appliedDiscountCode && currentValue.toUpperCase() !== appliedDiscountCode) {
                        discountMessage.textContent = 'سيتم استبدال كود الخصم الحالي بالكود الجديد';
                        discountMessage.className = 'discount-message info';
                        applyDiscountBtn.style.display = 'block';
                        applyDiscountBtn.textContent = 'استبدال';
                        applyDiscountBtn.disabled = false;

                        const removeDiscountBtn = document.getElementById('removeDiscountBtn');
                        if (removeDiscountBtn) {
                            removeDiscountBtn.style.display = 'none';
                        }
                    }

                    // Enable/disable apply button based on input
                    if (currentValue === '') {
                        if (appliedDiscountCode) {
                            // If there's an applied discount and field is empty, show remove button
                            applyDiscountBtn.style.display = 'none';
                            const removeDiscountBtn = document.getElementById('removeDiscountBtn');
                            if (removeDiscountBtn) {
                                removeDiscountBtn.style.display = 'block';
                            }
                            this.placeholder = `كود مطبق: ${appliedDiscountCode}`;
                        } else {
                            // No discount applied and field is empty
                            applyDiscountBtn.disabled = true;
                            applyDiscountBtn.style.display = 'block';
                            this.placeholder = 'أدخل كود الخصم';
                        }
                    } else if (!appliedDiscountCode || currentValue.toUpperCase() !== appliedDiscountCode) {
                        applyDiscountBtn.disabled = false;
                        applyDiscountBtn.style.display = 'block';
                    }
                });

                // Add Enter key support for discount code application
                discountCodeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !applyDiscountBtn.disabled) {
                        applyDiscountBtn.click();
                    }
                });
            }

            // Phone number formatting with safe DOM access
            const customerPhoneInput = document.getElementById('customerPhone');
            if (customerPhoneInput) {
                customerPhoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 11) {
                        value = value.substring(0, 11);
                    }
                    e.target.value = value;
                });
            }
        });
    </script>
</body>
</html>